(()=>{var e={};e.id=420,e.ids=[420],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5140:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>x,routeModule:()=>m,tree:()=>d});var a=i(260),l=i(8203),s=i(5155),n=i.n(s),o=i(7292),r={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(r[e]=()=>o[e]);i.d(t,r);let d=["",{children:["aboutus",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1501)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\aboutus\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,1485,23)),"next/dist/client/components/unauthorized-error"]}],x=["C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\aboutus\\page.jsx"],c={require:i,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/aboutus/page",pathname:"/aboutus",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6455:(e,t,i)=>{Promise.resolve().then(i.bind(i,8907))},1183:(e,t,i)=>{Promise.resolve().then(i.bind(i,7695))},7695:(e,t,i)=>{"use strict";i.d(t,{default:()=>x});var a=i(5512);let l=(0,i(1680).A)("MoveRight",[["path",{d:"M18 8L22 12L18 16",key:"1r0oui"}],["path",{d:"M2 12H22",key:"1m8cig"}]]);var s=i(8009),n=i(8531),o=i.n(n),r=i(5103),d=i(5693);let x=()=>{let[e,t]=(0,s.useState)(!1),[i,n]=(0,s.useState)(!1),[x,c]=(0,s.useState)(!1),m=(0,d.r)("1987-10-15"),p=()=>{t(window.innerWidth>=320&&window.innerWidth<=500),n(window.innerWidth>=768&&window.innerWidth<=1e3)};return(0,s.useEffect)(()=>(p(),window.addEventListener("resize",p),()=>window.removeEventListener("resize",p)),[]),(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("div",{className:"relative flex bg-aboutusBG h-[653px] tablet:h-[589px] desktop:h-[612px] flex-col items-center justify-end min-h-[100vh] bg-no-repeat bg-cover bg-center md:bg-center",children:[(0,a.jsxs)("div",{className:"z-10 text-center p-4 mb-16 tablet:mb-24 mobile:mb-24",children:[(0,a.jsx)("h1",{className:"text-2xl tablet:text-[52px] lg:text-5xl xl:text-6xl font-extrabold text-white mobile:text-[35px] mobile:leading-[54px]",children:"The Skyline Aviation Club"}),(0,a.jsx)("h3",{className:"text-lg tablet:text-[21px] tablet:mt-6 lg:text-4xl xl:text-4xl font-bold text-white mt-10 mobile:mt-2 mobile:font-medium mobile:leading-[22px]",children:"Shaping the Future of Aviation, One Pilot at a Time"}),(0,a.jsx)("div",{className:"flex justify-center mt-2",children:(0,a.jsxs)("p",{className:"mx-1 md:mx-0 md:text-base tablet:mb-16 text-white font-medium text-center mb-12 mobile:text-[15px] mobile:leading-[20px] capitalize",children:["Providing Continuous & relentless aviation education services since last ",m," years Trained more than 5000 trainees for Commercial Pilot Licence, Airline Transport Pilot Licence, Flight Dispatcher Licence, Aeronautical Radio Officer, Cabin Crew & Airport Ground Staff."]})})]}),(0,a.jsx)("div",{className:"absolute w-full px-4 md:px-0 top-[86%] mobile:pt-8 tablet:pt-12 desktop:pt-16",children:(0,a.jsxs)("div",{className:"container mx-auto grid grid-cols-1 sm:grid-cols-2 tablet:grid-cols-2 desktop:grid-cols-2 xl:grid-cols-2 gap-6 mobile:max-w-[370px] mobile:gap-2 mobile:mb-8 desktop:px-4 tablet:px-4",children:[(0,a.jsxs)("div",{className:"bg-white shadow-lg rounded-[20px] p-6 text-center mobile:p-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-[#1420CD] mb-4 tablet:text-[26px] mobile:text-center desktop:text-left",children:"Our Mission"}),(0,a.jsx)("p",{className:"text-descLightBlack font-medium text-[16px] leading-[21.2px] mobile:text-[14px] mobile:leading-[18.9px] mobile:font-semibold mobile:text-center tablet:text-[14px] tablet:leading-[18.9px] tablet:font-semibold desktop:text-left",children:"To Create An Effective And Efficient Learning Environment Which Encourages And Motivates And Which Through Instilling The Necessary Qualities Of Self Discipline Required Of Our Professional Aviators, Provides A Pleasant And Friendly Atmosphere In Which To Acquire Aviation Skills."})]}),(0,a.jsxs)("div",{className:"bg-white shadow-lg rounded-[20px] p-6 text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-[#1420CD] mb-4 tablet:text-[26px] mobile:text-center desktop:text-left",children:"Our Vision"}),(0,a.jsx)("p",{className:"text-descLightBlack font-medium text-[16px] leading-[21.2px] mobile:text-[14px] mobile:leading-[18.9px] mobile:font-semibold mobile:text-center tablet:text-[14px] tablet:leading-[18.9px] tablet:font-semibold desktop:text-left",children:"Creating A Legacy Of Excellence In Aviation Training That Resonates With Students, Airlines, And Regulatory Bodies Worldwide And Equipping Future Pilots With The Technical Expertise, Leadership Skills, And Global Perspective Needed To Navigate A Dynamic And Interconnected Aviation Ecosystem."})]})]})})]}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto pt-64 pr-12 pl-8 mobile:pt-12 mobile:mt-[29rem] mobile:pr-4 mobile:pl-4",children:[(0,a.jsx)("section",{className:"mb-12 mobile:mb-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center mx-auto tablet:flex-col tablet:items-center",children:[(0,a.jsx)(r.default,{src:"/cptManek.jpg",alt:"Pilot",className:"object-cover w-full md:w-1/2 rounded-[20px] shadow-md lg:w-1/2 lg:h-[696px] desktop:h-[875px] tablet:w-full tablet:h-[780px]",width:1e3,height:1e3}),(0,a.jsxs)("div",{className:"w-full md:w-1/2 lg:w-1/2 md:ml-8 flex flex-col justify-center h-full tablet:w-full tablet:px-4 tablet:ml-0",children:[(0,a.jsx)("h2",{className:"text-3xl font-light mb-4 text-textBluePrimary mt-4 mobile:text-[26px] mobile:font-semibold mobile:mb-2",children:"Our Story"}),(0,a.jsxs)("p",{className:"text-textGreyPrimary text-[17px] mb-2 capitalize mobile:normal-case leading-[22.8px] desktop:text-[16px] mobile:text-[15px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:["Founded on"," ",(0,a.jsx)("span",{className:"text-textBluePrimary font-bold ",children:"October 15, 1987"}),", The Skyline Aviation Club is a distinguished, independent, non-government, and"," ",(0,a.jsxs)("span",{className:"text-textBluePrimary font-bold",children:["non-profit organization"," "]}),"dedicated to providing classroom coaching for aspiring aviation professionals."]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:`text-textGreyPrimary text-[17px] mb-2 capitalize mobile:normal-case leading-[22.8px] desktop:text-[16px] mobile:text-[15px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold ${x?"":"line-clamp-4"}`,children:["For"," ",(0,a.jsxs)("span",{className:"text-textBluePrimary font-bold",children:["over ",m," years"]}),", it has been at the forefront of aviation education,"," ",(0,a.jsx)("span",{className:"text-textBluePrimary font-bold",children:"preparing over 5,000 trainees"})," ","for various pilot licences and aviation careers, including Airline Transport Pilot, Commercial Pilot, Aircraft Dispatcher, Air Hostess, Aeronautical Radio Officer, and many more. ",(0,a.jsx)("br",{})," The Skyline Aviation Club supports students in"," ",(0,a.jsxs)("span",{className:"text-textBluePrimary font-bold",children:["preparing for oral and written examinations"," "]}),"conducted by esteemed authorities such as the Director General of Civil Aviation (DGCA- India), Wireless Planning and Coordination Wing,(WPC- India), Federal Aviation Administration (FAA- USA), Transport Canada ( TC-Canada), Civil Aviation Safety Authority (CASA- Australia) under well experienced, qualified instructors certified by competent authorities like DGCA, India and FAA, USA.. The Skyline Aviation Club is affiliated with reputable Flight Training Organizations (FTO) / Flying Clubs and Flight Training Institutes in India and abroad to complete required practical flight training to qualify for Pilot's Licence in compliance with Director General of Civil Aviation (DGCA), Government of India."]}),(0,a.jsx)("div",{className:"my-1 text-end",children:(0,a.jsx)("button",{onClick:()=>c(e=>!e),className:"text-textBluePrimary font-semibold underline text-sm",children:x?"Read less":"Read more"})})]}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[17px] mb-2 capitalize mobile:normal-case leading-[22.8px] desktop:text-[16px] mobile:text-[15px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:"Beyond training, The Skyline Aviation Club provides comprehensive career guidance and employment support, helping students excel in airline entrance exams, interviews, group discussions, and psychometric tests. Hundreds of its alumni are now thriving in airlines across India and internationally."}),(0,a.jsxs)("p",{className:"text-textGreyPrimary text-[17px] mb-2 capitalize mobile:normal-case leading-[22.8px] desktop:text-[16px] mobile:text-[15px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:["The Skyline Aviation Club also plays a critical role in"," ",(0,a.jsx)("span",{className:"text-textBluePrimary font-bold",children:"disaster management through its Government of India-licenced Ham Radio Station (Call Sign: VU2ADO), offering emergency communication services during crises such as earthquakes, floods, and tsunamis since 1994."})]}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[17px] mb-2 capitalize mobile:normal-case leading-[22.8px] desktop:text-[16px] mobile:text-[15px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:"Committed to fostering a passion for aviation, Skyline organizes free seminars, career guidance programs, and airshows, inspiring the next generation of aviation professionals while raising public awareness about the aviation industry."})]})]})}),(0,a.jsx)("div",{className:"min-h-screen",children:i||e?(0,a.jsx)("div",{className:"flex justify-center items-center p-8 tablet:p-0 mobile:p-0",children:(0,a.jsx)("div",{className:"max-w-7xl w-full bg-white shadow-courseCardShadow rounded-lg p-6 md:p-8 mobile:p-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-8 mobile:gap-4 justify-items-center items-center md:grid-cols-2 tablet:grid-cols-1",children:[(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"pl-4",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-light my-4 text-textBluePrimary tablet:text-center tablet:text-[40px] tablet:font-bold tablet:mb-8 mobile:text-center mobile:font-bold mobile:my-2",children:"Innovation in Training"}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] font-medium mb-2 leading-[20px] sm:leading-[22.8px] tablet:font-bold tablet:leading-[23.8px] mobile:text-[14px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:"In a rapidly evolving aviation sector, innovation is critical. From leveraging cutting-edge simulators to incorporating virtual reality and advanced flight analytics, The Skyline Aviation Club focuses on staying ahead of the curve to ensure its students are prepared for both current and future challenges in aviation."}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] font-medium mb-4 leading-[20px] sm:leading-[22.8px] tablet:font-bold tablet:leading-[23.8px] mobile:text-[14px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:"The Skyline Aviation Club aspires to be at the forefront of aviation training worldwide, recognised not just as a school, but as a standard-setter in the industry. By fostering innovation and continuously improving its training methodologies, the academy aims to position itself as the top choice for aspiring pilots globally."})]}),(0,a.jsx)("div",{className:"flex justify-center items-center w-full flex-shrink-0 tablet:mt-4",children:(0,a.jsx)(r.default,{src:"/Innovation.svg",alt:"Innovation in Training",className:"rounded-[26px] shadow-lg w-full !max-w-[400px] h-auto tablet:w-full tablet:max-w-full mobile:mx-4 object-contain",width:100,height:100})})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"pl-4",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-light mb-4 text-textBluePrimary mt-4 tablet:text-center tablet:text-[40px]    tablet:font-bold tablet:mb-8 tablet:font-bold tablet:mb-8 mobile:text-center   mobile:font-bold mobile:my-2 mobile:text-[22px]",children:"Exceptional Pilot Graduates"}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] sm:text-[17px] font-medium mb-4 leading-[20px] sm:leading-[22.8px] tablet:font-bold tablet:leading-[23.8px] mobile:text-[14px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:"The ultimate goal is to produce pilots who are not only skilled but also confident, resilient, and ready to excel in competitive and challenging aviation roles. The Skyline Aviation Club's commitment to quality education and robust training ensures its graduates are industry-ready and sought after by leading airlines and aviation companies."}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] sm:text-[17px] font-medium mb-4 leading-[20px] sm:leading-[22.8px] tablet:font-bold tablet:leading-[23.8px] mobile:text-[14px] tablet:text-[16px] mobile:leading-[18.9px] mobile:font-semibold",children:"The academy emphasizes professionalism in every aspect, from its teaching faculty to the standards expected of its students. By instilling values such as discipline, safety, and ethical responsibility, The Skyline Aviation Club ensures its graduates uphold the highest professional standards."})]}),(0,a.jsx)("div",{className:"flex justify-center items-center w-full flex-shrink-0 tablet:mt-4 mobile:pb-4",children:(0,a.jsx)(r.default,{src:"/Exceptional.svg",alt:"Exceptional Pilot Graduates",className:"rounded-[26px] shadow-lg w-full !max-w-[400px] h-auto tablet:w-full tablet:max-w-full mobile:mx-4 object-contain",width:100,height:100})})]})]})})}):(0,a.jsx)("div",{className:"flex justify-center items-center p-8",children:(0,a.jsx)("div",{className:"max-w-7xl w-full bg-white shadow-courseCardShadow rounded-lg p-6 md:p-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 justify-items-center items-center",children:[(0,a.jsxs)("div",{className:"pl-4 w-full",children:[(0,a.jsx)("h2",{className:"text-2xl font-light mb-4 text-textBluePrimary mt-4",children:"Innovation in Training"}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] font-semibold mb-2 capitalize leading-[20px]",children:"In a rapidly evolving aviation sector, innovation is critical. From leveraging cutting-edge simulators to incorporating virtual reality and advanced flight analytics, The Skyline Aviation Club focuses on staying ahead of the curve to ensure its students are prepared for both current and future challenges in aviation."}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] font-semibold mb-2 capitalize leading-[20px]",children:"The Skyline Aviation Club aspires to be at the forefront of aviation training worldwide, recognised not just as a school, but as a standard-setter in the industry. By fostering innovation and continuously improving its training methodologies, the academy aims to position itself as the top choice for aspiring pilots globally."})]}),(0,a.jsx)("div",{className:"flex justify-center items-center w-full flex-shrink-0 tablet:mt-4",children:(0,a.jsx)(r.default,{src:"/training.svg",alt:"Innovation in Training",className:"rounded-[20px] shadow-lg w-full max-w-[400px] h-auto tablet:w-full tablet:max-w-full",width:100,height:100})}),(0,a.jsx)("div",{className:"flex justify-center items-center w-full flex-shrink-0 tablet:mt-4",children:(0,a.jsx)(r.default,{src:"/graduates.svg",alt:"Exceptional Pilot Graduates",className:"rounded-[20px] shadow-lg w-full max-w-[400px] h-auto tablet:w-full tablet:max-w-full",width:100,height:100})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-light mb-4 text-textBluePrimary mt-4",children:"Exceptional Pilot Graduates"}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] font-semibold mb-2 capitalize leading-[20px]",children:"The ultimate goal is to produce pilots who are not only skilled but also confident, resilient, and ready to excel in competitive and challenging aviation roles. The Skyline Aviation Club's commitment to quality education and robust training ensures its graduates are industry-ready and sought after by leading airlines and aviation companies."}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-[15px] font-semibold mb-2 capitalize leading-[20px]",children:"The academy emphasizes professionalism in every aspect, from its teaching faculty to the standards expected of its students. By instilling values such as discipline, safety, and ethical responsibility, The Skyline Aviation Club ensures its graduates uphold the highest professional standards."})]})]})})})})]}),(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("section",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-semibold text-textBluePrimary mt-4  mb-8 mobile:text-[26px] tablet:my-8 mobile:font-semibold mobile:mb-2 mobile:mt-8",children:"Meet our Team"}),e&&(0,a.jsx)("p",{className:"text-[14px] text-textGreyPrimary leading-[18.9px] font-semibold px-6 mb-4",children:"Creating a legacy of excellence in aviation training that resonates with students, airlines, and regulatory bodies worldwide and equipping future pilots with the technical expertise, leadership skills, and global perspective needed to navigate a dynamic and interconnected aviation ecosystem."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto px-6 mb-16 desktop:mx-auto desktop:w-[90%] mobile:mb-8",children:[(0,a.jsxs)("div",{className:"relative bg-white shadow-md rounded-[20px] overflow-hidden",children:[(0,a.jsx)(r.default,{src:"/founderTeam.svg",alt:"Founder",className:"w-full md:w-[554px] h-auto md:h-[390px] object-cover rounded-[20px]",width:100,height:100}),(0,a.jsxs)(o(),{href:"/founder",className:"absolute inset-0 flex flex-col top-[284px] justify-center items-start text-white pl-6 mobile:pl-4 mobile:top-[65%]",children:[(0,a.jsx)("h3",{className:"text-3xl font-medium leading-[40px] mobile:text-[20px] mobile:font-semibold mobile:leading-[28px]",children:"Capt AD Manek ( Founder )"}),(0,a.jsx)("p",{className:"text-xl mobile:text-[14px] mobile:font-medium",children:"Learn More →"})]})]}),(0,a.jsxs)("div",{className:"relative bg-white shadow-md  rounded-[20px] overflow-hidden",children:[(0,a.jsx)(r.default,{src:"/facultyTeam.svg",alt:"Faculty Team",className:"w-full md:w-[554px] h-auto md:h-[390px] object-cover rounded-[20px]",width:100,height:100}),(0,a.jsxs)(o(),{href:"/faculty",className:"absolute inset-0 flex flex-col top-[284px] justify-center items-start text-white pl-6 mobile:pl-4 mobile:top-[65%]",children:[(0,a.jsx)("h3",{className:"text-3xl font-medium mobile:text-[20px] mobile:font-semibold mobile:leading-[28px]",children:"Faculty Team"}),(0,a.jsx)("p",{className:"text-xl mobile:text-[14px] mobile:font-medium",children:"Learn More →"})]})]})]})]}),(0,a.jsxs)("section",{className:"relative bg-textBluePrimary text-white py-4 md:py-12",children:[(0,a.jsx)("div",{className:"absolute inset-0 mobile:top-[30%]",children:(0,a.jsx)(r.default,{src:"/Facultybg.svg",alt:"Faculty Background",className:"w-full md:w-[1500px] h-[200px] md:h-[480px] object-cover",width:100,height:100,style:{objectPosition:"center"}})}),(0,a.jsxs)("div",{className:"relative max-w-[80%] mobile:max-w-[100%] mx-auto px-6 text-center z-10 mobile:px-12",children:[(0,a.jsx)("h2",{className:"font-bold text-[32px] mb-12 mobile:text-lg mobile:mb-3",children:"Awards and Recognition"}),(0,a.jsxs)("p",{className:"text-[16px] leading-[20px] text-center font-medium capitalize mb-4 mobile:leading-[21px] mobile:text-[12px] tablet:leading-[23px] tablet:text-semibold",children:["Since its establishment in 1987, The Skyline Aviation Club has been consistently recognised for its excellence in aviation training. The organization has received several awards and certifications from prestigious aviation authorities and industry bodies, acknowledging its commitment to producing world-class aviation professionals. ",(0,a.jsx)("br",{})," The awards symbolize Skyline's legacy of leadership in aviation education and its dedication to shaping the future of aviation through continuous excellence."]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(o(),{href:"/gallery?activeTab=awards",children:(0,a.jsxs)("button",{type:"button",className:"flex items-center justify-between bg-white text-textBluePrimary py-2 pl-6 pr-2 transition-all duration-300 shadow-md gap-x-2.5 rounded-full",children:[(0,a.jsx)("span",{className:"text-[16.73px] font-bold",children:"View Awards"}),(0,a.jsx)(l,{className:"bg-textBluePrimary w-10 text-white h-10 py-1.5 px-1.75 rounded-[25px]"})]})})})]})]}),(0,a.jsx)("section",{className:"bg-gray-50 text-white",children:(0,a.jsxs)("div",{className:"relative bg-gray-50 py-4 md:py-12",children:[(0,a.jsx)("div",{className:"absolute inset-0 mobile:top-[30%]",children:(0,a.jsx)(r.default,{src:"/Facultybg.svg",alt:"Faculty Background",className:"w-full md:w-[1500px] h-[200px] md:h-[480px] object-cover",width:100,height:100,style:{objectPosition:"center",paddingBottom:"38px"}})}),(0,a.jsxs)("div",{className:"relative z-10 max-w-[80%] mobile:max-w-[90%] mx-auto text-center px-4 capitalize",children:[(0,a.jsx)("h1",{className:"font-bold text-[32px] text-[#1420CD] mb-12 mobile:text-[21px] mobile:mb-3",children:"Government Approvals"}),(0,a.jsx)("p",{className:"text-[16px] text-customBlack leading-[22.2px] text-center font-medium capitalize text-[#464C3B] mb-8 leading-[20px] text-center font-medium mobile:leading-[21px] mobile:text-[14px] tablet:leading-[23px] tablet:text-semibold",children:"Over the years, the club has not only earned prestigious awards for its contributions to aviation training but has also received multiple government approvals, further solidifying its credibility in the field. These certifications and approvals demonstrate The Skyline Aviation Club's commitment to meeting and exceeding regulatory standards in aviation training, ensuring its students receive a globally acknowledged education."}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(o(),{href:"/gallery?activeTab=government-approvals",children:(0,a.jsxs)("button",{type:"submit",className:"flex items-center justify-between bg-textBluePrimary text-white py-2 pl-6 pr-2 transition-all duration-300 shadow-md gap-x-2.5 rounded-full",style:{background:"linear-gradient(90deg, #0E1795 0%, #1420CD 100%)"},children:[(0,a.jsx)("span",{className:"text-[16.73px] font-bold",children:"View Approvals"}),(0,a.jsx)(l,{className:"bg-white w-10 text-textBluePrimary h-10 py-1.5 px-1.75 rounded-[25px]"})]})})})]})]})}),(0,a.jsxs)("section",{className:"relative text-white py-28 flex items-center mobile:md:flex",children:[(0,a.jsxs)("div",{className:"absolute inset-0",children:[(0,a.jsx)(r.default,{src:"/aviation.svg",alt:"Faculty Background",className:"w-full h-full object-cover",width:100,height:100,style:{objectPosition:"center"}}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-70"})]}),(0,a.jsxs)("div",{className:"relative max-w-5xl mx-auto px-6 text-center z-10",children:[(0,a.jsxs)("h2",{className:"font-bold text-[32px] mb-12 capitalize",children:["Join the legacy of aviation excellence at",(0,a.jsx)("br",{}),"The Skyline Aviation Club."]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(o(),{href:"/enquire",className:"flex items-center justify-between bg-white text-textBluePrimary mt-6 py-2 pl-6 pr-2 mb-6 transition-all duration-300 shadow-md gap-x-2.5 rounded-full",children:[(0,a.jsx)("span",{className:"text-[16.73px] font-bold",children:"Enquire now"}),(0,a.jsx)(l,{className:"bg-textBluePrimary w-10 text-white h-10 py-1.5 px-1.75 rounded-[25px]"})]})})]})]})]})]})}},5693:(e,t,i)=>{"use strict";i.d(t,{r:()=>l});var a=i(8009);function l(e){let[t,i]=(0,a.useState)(0);return t}},1501:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});var a=i(2740),l=i(8907);let s=()=>(0,a.jsx)(l.default,{})},8907:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Skyline-Aviation\\\\client\\\\src\\\\components\\\\AboutUsComponent.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\AboutUsComponent.jsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[756,393],()=>i(5140));module.exports=a})();