"use strict";exports.id=868,exports.ids=[868],exports.modules={7868:(e,t,a)=>{a.d(t,{default:()=>y});var s=a(5512),l=a(8009),r=a(6098),n=a(5907),i=a(4355),c=a(1766),d=a(7537),o=a(8755),m=a(8638),u=a(4849),x=a(6645);let p=d.bL;d.YJ;let f=d.WT,h=l.forwardRef(({className:e,children:t,...a},l)=>(0,s.jsxs)(d.l9,{ref:l,className:(0,x.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,s.jsx)(d.In,{asChild:!0,children:(0,s.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]}));h.displayName=d.l9.displayName;let b=l.forwardRef(({className:e,...t},a)=>(0,s.jsx)(d.PP,{ref:a,className:(0,x.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}));b.displayName=d.PP.displayName;let N=l.forwardRef(({className:e,...t},a)=>(0,s.jsx)(d.wn,{ref:a,className:(0,x.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})}));N.displayName=d.wn.displayName;let g=l.forwardRef(({className:e,children:t,position:a="popper",...l},r)=>(0,s.jsx)(d.ZL,{children:(0,s.jsxs)(d.UC,{ref:r,className:(0,x.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...l,children:[(0,s.jsx)(b,{}),(0,s.jsx)(d.LM,{className:(0,x.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(N,{})]})}));g.displayName=d.UC.displayName,l.forwardRef(({className:e,...t},a)=>(0,s.jsx)(d.JU,{ref:a,className:(0,x.cn)("px-2 py-1.5 text-sm font-semibold",e),...t})).displayName=d.JU.displayName;let j=l.forwardRef(({className:e,children:t,...a},l)=>(0,s.jsxs)(d.q7,{ref:l,className:(0,x.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(d.VF,{children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})}),(0,s.jsx)(d.p4,{children:t})]}));j.displayName=d.q7.displayName,l.forwardRef(({className:e,...t},a)=>(0,s.jsx)(d.wv,{ref:a,className:(0,x.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=d.wv.displayName;let y=()=>{let e=(0,l.useRef)(),{toast:t}=(0,c.dj)(),[a,d]=(0,l.useState)({fullName:"",course:"",contactNumber:"",city:"",email:"",helpText:""}),[o,m]=(0,l.useState)({}),[u,x]=(0,l.useState)(!1),b=a.fullName.trim()&&a.course.trim()&&a.contactNumber.trim()&&a.city.trim()&&a.email.trim(),N=e=>{let{name:t,value:a}=e.target;d(e=>({...e,[t]:a}))},y=()=>{let e={};return(!a.fullName||a.fullName.length>50||/[^a-zA-Z\s]/.test(a.fullName))&&(e.fullName="Full Name should be less than 50 characters and contain only letters."),(!a.contactNumber||a.contactNumber.length>15||/[^0-9]/.test(a.contactNumber))&&(e.contactNumber="Contact Number should be less than 15 digits and contain only numbers."),(!a.city||a.city.length>50||/[^a-zA-Z\s]/.test(a.city))&&(e.city="City should be less than 50 characters and contain only letters."),a.email?/\S+@\S+\.\S+/.test(a.email)||(e.email="Email Address is invalid."):e.email="Email Address is required.",a.course||(e.course="Please select a course."),m(e),0===Object.keys(e).length},w=function(e,t){let a=(0,l.useRef)(null);return(...t)=>{clearTimeout(a.current),a.current=setTimeout(()=>{e(...t)},1e3)}}(e=>i.Ay.send("service_tph07dp","template_hj3d7nh",e,"MG4XYp65GKvErgJlL"),0),v=async e=>{if(e.preventDefault(),!b){t({variant:"destructive",title:"Missing Fields!",description:"Please fill out all required fields."});return}if(!y())return;let s={full_name:a.fullName,course_name:a.course,contact_number:a.contactNumber,city:a.city,email_address:a.email,help_text:a.helpText||"Not provided"};try{x(!0),await w(s),t({variant:"success",title:"Success!",description:"Your enquiry has been sent successfully!"}),d({fullName:"",course:"",contactNumber:"",city:"",email:"",helpText:""})}catch(e){t({variant:"destructive",title:"Error!",description:"Failed to send your enquiry. Please try again later."}),console.error("EmailJS Error:",e.text)}finally{x(!1)}};return(0,s.jsxs)("div",{className:"w-full md:w-1/2 tablet:w-[85%] bg-white shadow-lg rounded-lg p-6 mb-8 relative",children:[(0,s.jsxs)("form",{ref:e,className:"space-y-4",onSubmit:v,children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Full Name",(0,s.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,s.jsx)("input",{type:"text",name:"fullName",placeholder:"Enter full name",value:a.fullName,onChange:N,className:`w-full p-3 bg-formBG text-[15px] text-black rounded-lg focus:outline-none ${o.fullName?"ring-2 ring-red-500":""}`,maxLength:"50",pattern:"[a-zA-Z\\s]*",required:!0}),o.fullName&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:o.fullName})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Select Course Interested In",(0,s.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,s.jsxs)(p,{value:a.course,onValueChange:e=>d(t=>({...t,course:e})),children:[(0,s.jsx)(h,{className:`w-full p-[1.4rem] bg-formBG text-[15px] text-black rounded-lg ${o.course?"ring-2 ring-red-500":""}`,children:(0,s.jsx)(f,{placeholder:"Select a Course"})}),(0,s.jsx)(g,{children:r.U8.map((e,t)=>(0,s.jsx)(j,{value:e.text,children:e.text},t))})]}),o.course&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:o.course})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Contact Number",(0,s.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,s.jsx)("input",{type:"text",name:"contactNumber",value:a.contactNumber,onChange:N,maxLength:"15",pattern:"[0-9]*",required:!0,placeholder:"Enter contact number",className:`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${o.contactNumber?"ring-2 ring-red-500":""}`}),o.contactNumber&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:o.contactNumber})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["City",(0,s.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,s.jsx)("input",{type:"text",name:"city",value:a.city,onChange:N,maxLength:"50",pattern:"[a-zA-Z\\s]*",required:!0,placeholder:"Enter city",className:`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${o.city?"ring-2 ring-red-500":""}`}),o.city&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:o.city})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Email Address",(0,s.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,s.jsx)("input",{type:"email",name:"email",value:a.email,onChange:N,required:!0,placeholder:"Enter email",className:`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${o.email?"ring-2 ring-red-500":""}`}),o.email&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:o.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:"Tell Us How We Can Help"}),(0,s.jsx)("textarea",{name:"helpText",value:a.helpText,onChange:N,placeholder:"Optional message",rows:"3",className:"w-full p-3 bg-formBG text-[15px] text-black rounded-lg"})]}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("button",{type:"submit",className:"flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5",children:[(0,s.jsx)("span",{className:"font-bold",children:"Send Enquiry"}),(0,s.jsx)(n.A,{className:"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1"})]})})]}),u&&(0,s.jsx)("div",{className:"fixed inset-0 bg-[#0000004f] bg-opacity-40 z-50 flex justify-center items-center",children:(0,s.jsx)("div",{className:"w-12 h-12 border-4 border-[#2821ff] border-t-transparent rounded-full animate-spin"})})]})}}};