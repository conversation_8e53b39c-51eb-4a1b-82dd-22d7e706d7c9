"use client";
import FAQContainer from "@/components/FAQContainer";
import OurWorldContainer from "@/components/OurWorldContainer";
import CoursesOffer from "@/components/CoursesOffer";
import AlumniCarousel from "@/components/AlumniCarousel";
import WhyChooseContainer from "@/components/WhyChooseContainer";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import EnquireForm from "@/components/EnquireForm";
import { eventsPageData } from "@/constant/eventsData";
import EventCard from "@/components/common/EventCard";
import { useEffect, useState } from "react";
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import SingleEventCard from "@/components/SingleEventCard";

export default function Home() {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [api, setApi] = useState();

  useEffect(() => {
    if (!api) {
      return;
    }

    setSelectedIndex(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setSelectedIndex(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const scrollTo = (index) => {
    if (api) {
      api.scrollTo(index);
    }
  };




  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await fetch(process.env.NEXT_PUBLIC_URL);
        const data = await response.json();
        // console.log(data.data[0].image);


        // Transform the API data to match the expected format
        const formattedEvents = data.data.map((event) => ({
          title: event.title,
          desc: event.description,
          image: process.env.NEXT_PUBLIC_STRAPI_URL + event.image?.url,
        }
        ));

        // console.log(formattedEvents[0]?.image)
        // console.log("yooo", formattedEvents);
        // console.log(formattedEvents);


        setEvents(formattedEvents);
      } catch (error) {
        console.error("Failed to fetch events:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
    // setEvents(eventsPageData);
    // setLoading(false);
  }, []);

  return (
    <>
      {/* <div className="relative bg-landingBanner bg-cover bg-[-35rem] md:bg-center h-[750px] bg-no-repeat"> */}
      <div className="relative bg-landingBanner bg-cover bg-[90%_center] md:bg-[60%_center] h-[750px] bg-no-repeat">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent"></div>
        <div className="absolute bottom-24 left-0 right-0 mx-4 md:mx-12 lg:mx-32 pb-4 md:pb-6">
          <h1 className="text-[2.3rem] leading-[2.7rem] font-bold md:font-extrabold lg:text-6xl text-white mb-1 md:mb-3 lg:mb-4 lg:leading-tight">
            Reach New <br />
            Heights with <br />
            The Skyline Aviation Club
          </h1>
          <h3 className="font-bold text-lg md:text-3xl text-white mb-1 md:mb-4">
            Your Pilot Journey Begins Here !
          </h3>
          <p className="text-[13px] font-medium md:font-bold md:text-xl text-white mb-4">
            Take the Pilot&apos;s Seat with Confidence - Start Your <br />
            Training Now !
          </p>
          <div className="flex">
            <Link
              href="/enquire"
              className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}
            >
              <span className="font-bold">Enquire Now</span>
              <ArrowRight className="w-6 h-6 bg-white text-arrowIconColor rounded-full p-1" />
            </Link>
          </div>

        </div>
      </div>
      <WhyChooseContainer />
      <CoursesOffer />
      <div className="p-3 md:bg-footerBG lg:p-8 md:bg-alumniCurve bg-cover bg-center pb-7">
        <h3 className="text-center mb-1.5 lg:mb-2.5 font-bold text-xl md:text-2xl lg:text-4xl text-textBluePrimary">
          Recent Events
        </h3>
        <p className="text-center text-sm lg:text-base text-customGrey100 font-medium mb-3 lg:mb-4">
          Discover and explore upcoming events, workshops, and gatherings
          tailored to your interests.
        </p>

        {/* Mobile Carousel */}
        <div className="block sm:hidden">
          <Carousel
            setApi={setApi}
            className="w-full max-w-sm mx-auto relative"
            opts={{
              loop: true,
            }}
          >
            <CarouselContent>
              {events.slice(0, 3).map((event, index) => (
                <CarouselItem key={index} className="flex justify-center">
                  <SingleEventCard event={event} />
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="flex justify-center items-center mt-4 space-x-2">
              {events.slice(0, 3).map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollTo(index)}
                  className={`${selectedIndex === index + 1
                    ? "h-3 w-8 rounded-full bg-textBluePrimary"
                    : "h-3 w-3 rounded-full bg-carouselDotsBG"
                    } cursor-pointer transition-all duration-300 ease-in-out`}
                />
              ))}
            </div>
          </Carousel>
        </div>


        {/* Tablet and Desktop Grid */}
        <div className="hidden sm:grid mx-auto max-w-6xl w-full grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-fr">
          <EventCard cardData={events.slice(0, 3)} isLandingPage={loading} />
        </div>

        <div className="flex justify-center">
          <Link
            href="/events"
            className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10`}
          >
            <span className="font-bold">View All Events</span>
            <ArrowRight className="w-6 h-6 bg-white text-arrowIconColor rounded-full p-1" />
          </Link>
        </div>
        {/* 
        <Link
          href="/events"
          className="mt-7 lg:mt-3 mx-auto text-white text-sm md:text-base py-2 pr-4 pl-5 md:pl-7 w-[180px] md:w-[220px] justify-between font-bold rounded-full border border-transparent bg-buttonBGPrimary flex items-center gap-1 ">
          View All Events
          <ArrowRight className="w-7 h-7 md:w-9 md:h-9 p-1 bg-white rounded-full text-arrowIconColor" />
        </Link> */}
      </div>

      <AlumniCarousel />
      <OurWorldContainer />
      <FAQContainer />
      <div className="md:px-8 lg:px-12 z-0 relative">
        <div className="z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-10 lg:bottom-[0px] left-0 right-0 w-full h-[500px]"></div>
        <div className="px-4 py-6 md:p-8 lg:flex gap-8">
          <div className="flex flex-col mb-4 lg:basis-6/12">
            <h3 className="text-center text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl mb-2 md:mb-8">
              Take Off With The Skyline Aviation Club
            </h3>
            <p className="text-left md:text-center lg:text-left font-medium text-formPara text-xs md:text-sm lg:text-base">
              Ready to embark on your aviation career? Whether you are
              curious about our courses, admission process, or career
              opportunities, we&apos;re here to guide you every step of the way.
              Fill out the form below, and our team will get in touch with you
              shortly. Let&apos;s make your aviation aspirations a reality !
            </p>
          </div>
          <EnquireForm />
        </div>
      </div>
    </>
  );
}
