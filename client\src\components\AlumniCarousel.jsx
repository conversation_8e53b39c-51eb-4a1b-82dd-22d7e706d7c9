"use client";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Image from "next/image";
import { alumniData } from "@/constant/landingPageData";
import { Star } from "lucide-react";
import { useEffect, useState } from "react";

const AlumniCarousel = () => {
  const [api, setApi] = useState();
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setSelectedIndex(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setSelectedIndex(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const scrollTo = (index) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <div className="p-3 md:bg-footerBG lg:p-8 md:bg-alumniCurve bg-cover bg-center">
      <h3 className="text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl md:mb-4 mb-2 text-center">
        5000+ Strong Alumnis
      </h3>
      <p className="text-customGrey100 font-medium lg:text-base md:text-sm text-base mb-8 text-center">
        Some testimonials by our alumnis!
      </p>
      <div className="w-full max-w-3xl mx-auto">
        <Carousel
          setApi={setApi}
          className="shadow-cardsBoxShadow"
          opts={{
            loop: true,
          }}
        >
          <CarouselContent className="flex justify-start">
            {alumniData.map((item, index) => (
              <CarouselItem key={index} className="w-full md:w-1/2 flex-shrink-0 snap-start">
                <Card className="bg-white h-full">
                  <CardContent className="p-6 md:p-12 lg:p-12 flex flex-col items-center gap-6">
                    <div>
                      <div className="flex items-center justify-center space-x-1 mb-3">
                        {Array.from({ length: 5 }).map((_, index) => (
                          <Star
                            className={`${index + 1 <= item.rating
                              ? "fill-fullStarBG text-fullStarBG"
                              : index + 0.5 < item.rating
                                ? "fill-halfStarBG text-halfStarBG"
                                : "fill-emptyStarBG text-emptyStarBG"
                              } w-4 h-4`}
                            key={index}
                          />
                        ))}
                      </div>
                      {item.desc}
                    </div>
                    <div>
                      <Image
                        src={item.image}
                        width={100}
                        height={100}
                        alt="avatar"
                        className="rounded-full w-[60px] h-[60px] md:w-[100px] md:h-[100px] mx-auto mb-4"
                      />
                      <h3 className="font-bold text-textBluePrimary text-center capitalize text-base lg:text-xl">
                        {item.title}
                      </h3>
                      <h4 className="font-medium text-customBlack text-center text-sm md:text-base lg:text-lg">
                        {item.subTitle}
                      </h4>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex border-0 bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6" />
          <CarouselNext className="hidden md:flex border-0 bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6" />
        </Carousel>
        <div className="flex justify-center items-center mt-4 space-x-2">
          {alumniData.map((_, index) => (
            <button
              key={index}
              onClick={() => scrollTo(index)}
              aria-label={`Go to slide ${index + 1}`}    
              className={`${selectedIndex === index + 1
                  ? "h-3 w-8 rounded-full bg-textBluePrimary"
                  : "h-3 w-3 rounded-full bg-carouselDotsBG"
                } cursor-pointer transition-all duration-300 ease-in-out`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default AlumniCarousel;
