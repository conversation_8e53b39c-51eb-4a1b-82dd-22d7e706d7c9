{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/accordion.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDown } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Accordion = AccordionPrimitive.Root\r\n\r\nconst AccordionItem = React.forwardRef(({ className, ...props }, ref) => (\r\n  <AccordionPrimitive.Item ref={ref} className={cn(className)} {...props} />\r\n))\r\nAccordionItem.displayName = \"AccordionItem\"\r\n\r\nconst AccordionTrigger = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Header className=\"flex\">\r\n    <AccordionPrimitive.Trigger\r\n      ref={ref}\r\n      className={cn(\r\n        \"flex flex-1 items-center justify-between transition-all text-left [&[data-state=open]>svg]:rotate-180\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <ChevronDown\r\n        className=\"h-7 w-7 shrink-0 text-white transition-transform duration-200\" />\r\n    </AccordionPrimitive.Trigger>\r\n  </AccordionPrimitive.Header>\r\n))\r\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\r\n\r\nconst AccordionContent = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <AccordionPrimitive.Content\r\n    ref={ref}\r\n    className=\"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\r\n    {...props}>\r\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\r\n  </AccordionPrimitive.Content>\r\n))\r\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,YAAY,yKAAmB,IAAI;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,MAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC,yKAAmB,IAAI;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QAAa,GAAG,KAAK;;;;;;;AAExE,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC5E,6LAAC,yKAAmB,MAAM;QAAC,WAAU;kBACnC,cAAA,6LAAC,yKAAmB,OAAO;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yGACA;YAED,GAAG,KAAK;;gBACR;8BACD,6LAAC,uNAAA,CAAA,cAAW;oBACV,WAAU;;;;;;;;;;;;;;;;;;AAIlB,iBAAiB,WAAW,GAAG,yKAAmB,OAAO,CAAC,WAAW;AAErE,MAAM,iCAAmB,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC5E,6LAAC,yKAAmB,OAAO;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBACT,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;;AAGjD,iBAAiB,WAAW,GAAG,yKAAmB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/faqData.jsx"], "sourcesContent": ["const accordionData = [\r\n  {\r\n    title: \"What type of Learning Environemnt will the students be in ?\",\r\n    content: (\r\n      <p>\r\n        ● We have tried our best efforts to make and maintain high standard\r\n        learning environment. Our Classroom is fully air conditioned to make\r\n        learning in comfortable and relaxed conditions. Our philosophy and\r\n        intention are to limit the number of students in each intake to a\r\n        manageable level of 20 students. This way, we can give each student more\r\n        individual attention resulting in improved overall performance by the\r\n        students. We have no more than 20 students in any phase of training at\r\n        any one time.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    title: \"How are the students taught ?\",\r\n    content: (\r\n      <>\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Flight Simulator Training\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            Students are given opportunity to have hands on training on In-house\r\n            Flight Simulator where student learns and acquires basic flying\r\n            skills through VFR mode and fly in practice area and do touch and\r\n            goes at the airport to develop take off, circuit pattern, approach\r\n            and landings and also develops good instrument scanning while on IFR\r\n            mode.\r\n          </p>\r\n        </section>\r\n\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Audio Visual Aids\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            As technology has changed in each and every field of life, education\r\n            should not be apart from it. So we arrange educational films based\r\n            on new syllabus, the entire study program on audiovisuals as viewing\r\n            has much more impact than hearing. Home Theatre is made available to\r\n            deliver audiovisual lectures via educational DVDs procured from\r\n            world renowned publisher like ASA and Jeppesen companies of USA on\r\n            various aviation topics will be provided for all students.\r\n          </p>\r\n        </section>\r\n\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Radio Communication Practical Facility\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            The Skyline Aviation Club is only aviation training institute in\r\n            India having Amateur (HAM) Radio Station licenced by Wireless\r\n            Planning and Co-Ordination (WPC), Ministry of Telecommunication &\r\n            IT, Government of India since last 30 years. We have well equipped\r\n            and modern wireless lab having HF, VHF and UHF wireless sets where\r\n            each and every wireless communication demonstration are carried out\r\n            to understand the syllabus of Radio Telephony (R) Licence /Flight\r\n            Radio Telephone Licence and Amateur (HAM) Radio Licence.\r\n          </p>\r\n        </section>\r\n\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Aviation Library\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            The Skyline Aviation Club is only aviation training institute in\r\n            India having a well-furnished library. Library is storing 4000 plus\r\n            books on various subjects useful for students with selective and\r\n            informative aviation books, Pilot Training Manuals, ICAO Annexure's,\r\n            ICAO Documents, aviation magazines, periodicals including all\r\n            Commercial Pilot Licence syllabus books as prescribed by the DGCA,\r\n            Ministry of Civil Aviation, Government of India. Pilot Training\r\n            Books/ Manuals as prescribed by Federal Aviation Administration\r\n            (FAA), USA and Transport Canada (TC) Canada.\r\n          </p>\r\n        </section>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"What is the teaching Methodology ?\",\r\n    content: (\r\n      <>\r\n        <p>\r\n          ● The courses are a carefully structured mix of classroom sessions,\r\n          computer based training and audio-visual presentations on Home Theatre\r\n          Screen, Real time Air Traffic Control (ATC) Radio Communication via\r\n          Visuals and audio plus Well-equipped In-house Flight Simulator.\r\n          Theoretical aspects guided by the faculty, help the student test the\r\n          concepts that have been explained in class before assimilating them.\r\n        </p>\r\n        <p>\r\n          ● The courses are made simpler by using latest IFR Jeppesen\r\n          Aeronautical charts for Enroute flying and Instrument Approaches,\r\n          Landing Charts, Aerodrome Obstruction Charts and Terminal, Sectional\r\n          and World Aeronautical Charts (WAC) for VFR air navigation. Moreover,\r\n          the courses are on up-to-date aviation information as per the\r\n          International Civil Aviation Organization (ICAO) Annexes and\r\n          documents, Aeronautical Information Publications (AIP) India, AERADIO,\r\n          NOTAMS and updated Jeppesen Manual especially for the Indian flying\r\n          environment. The Club is one of the rare institutions who boasts of\r\n          having highly qualified, rated and experienced war veterans.\r\n        </p>\r\n        <p>\r\n          ● Personalized instructions are provided to each and every student.\r\n          The student&apos;s progress is continuously assessed through a series\r\n          of internal tests and assignments. These are given significant weight\r\n          in award of Final Certificate of Achievement from the Club\r\n        </p>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"I do not stay in Mumbai. Is Lodging and Boarding Provided ?\",\r\n    content: (\r\n      <>\r\n        <p>\r\n          ● We provide for Lodging and Boarding which is situated walking\r\n          distance from our training center. The facilities are well maintained\r\n          to ensure a peaceful and livable environment with all the basic\r\n          necessities. Separate facilities for boys and girls are available.\r\n        </p>\r\n        <p>● Home cooked meals can also be provided twice a day.</p>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Who will be teaching the students ?\",\r\n    content: (\r\n      <>\r\n        <p>\r\n          ● Our present pilot training courses are managed by well-qualified and\r\n          highly experienced professional pilots holding I Commercial Pilot\r\n          Licence with Pilot Instructor Rating certified by Director General of\r\n          Civil Aviation (DGCA) Government of India and Federal Aviation\r\n          Administration (FAA) Government of United States of America who have\r\n          not just attained knowledge and experiences of commercial pilots, but\r\n          have special training on instrument rating, multi-engine rating and a\r\n          special qualification as PILOT INSTRUCTOR. They hold ADVANCED\r\n          INSTRUMENT GROUND PILOT INSTRUCTOR (A.G.I.I), Certificate from Federal\r\n          Aviation Administration (FAA) USA especially for you ! Some faculty\r\n          members are retired pilots from Indian Air Force and Indian Naval\r\n          Aviation. The faculty is capable of teaching students from zero level\r\n          of Student Pilot through Airline Transport Pilot with their invaluable\r\n          aviation experiences of last forty years.\r\n        </p>\r\n        <p>\r\n          ● The Club and its instructors are members of many aviation\r\n          organizations of India like Federation of Indian Pilots (FIP),\r\n          Aeronautical Society of India (ASI), Indian Women Pilots Association\r\n          (IWPA) and having association with Aircraft Owners & Pilots\r\n          Association (AOPA) USA, International Air Cadet Exchange Association\r\n          (IACEA) Great Britain, Civil Air Patrol (CAP) Auxiliary US Air Force,\r\n          USA. Ninety-nine Inc. (International Woman&apos;s Pilot Association)\r\n          USA, FAPA USA, EAA USA, Major Airlines Aviation Agencies and best\r\n          flight training Organizations (FTO) in India and abroad. A combination\r\n          of such versatile experiences is like adding one more feather to the\r\n          cap of the Club. Our Chief Pilot Instructor Capt. (Dr.) AD Manek had\r\n          personally visited and gained knowledge of functioning of\r\n          International Civil Aviation Organization (ICAO) HQ Montreal, Canada,\r\n          International Telecommunication Union (ITU) HQ Geneva, Switzerland\r\n          which are supreme authorities to control civil aviation throughout\r\n          world consisting 193 countries, along with visit to NASA, USA to share\r\n          the best possible knowledge to students.\r\n        </p>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Once I finish my training at The Skyine Aviation Club what next ?\",\r\n    content: (\r\n      <p>\r\n        ● Students completing Ground Training will be deputed for Practical\r\n        flight training with our affiliated DGCA approved Flight Training\r\n        Organization or Best Flight Training School in foreign countries like\r\n        USA, Canada, Australia, New Zealand & U.K. Students and Parents will be\r\n        provided with all assistance on Visa Information, Foreign Exchange, Air\r\n        Tickets, Insurance and all related Guidance at a single table.\r\n      </p>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default accordionData;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,uBACE,6LAAC;sBAAE;;;;;;IAWP;IACA;QACE,OAAO;QACP,uBACE;;8BACE,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAUvD,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAWvD,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAYvD,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;;;IAc7D;IACA;QACE,OAAO;QACP,uBACE;;8BACE,6LAAC;8BAAE;;;;;;8BAQH,6LAAC;8BAAE;;;;;;8BAYH,6LAAC;8BAAE;;;;;;;;IAQT;IACA;QACE,OAAO;QACP,uBACE;;8BACE,6LAAC;8BAAE;;;;;;8BAMH,6LAAC;8BAAE;;;;;;;;IAGT;IACA;QACE,OAAO;QACP,uBACE;;8BACE,6LAAC;8BAAE;;;;;;8BAgBH,6LAAC;8BAAE;;;;;;;;IAqBT;IACA;QACE,OAAO;QACP,uBACE,6LAAC;sBAAE;;;;;;IASP;CACD;uCAEc"}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/FAQContainer.jsx"], "sourcesContent": ["import {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\nimport accordionData from \"@/constant/faqData\";\r\nconst FAQContainer = () => {\r\n  return (\r\n    <div className=\"bg-[url('/assets/faqImage.webp')] bg-cover bg-center bg-local px-4 py-8 md:p-12 lg:py-28 xl:py-28\">\r\n      <h3 className=\"text-white font-extrabold text-2xl md:text-4xl lg:text-6xl mb-4 lg:mb-16\">\r\n        Frequently Asked Questions.\r\n      </h3>\r\n      <Accordion\r\n        type=\"single\" // Ensures only one can be open at a time\r\n        collapsible\r\n        className=\"grid gap-4 items-start justify-center pt-4 grid-cols-1\"\r\n      >\r\n        {accordionData.map((item, index) => (\r\n          <AccordionItem\r\n            key={index}\r\n            value={`item-${index}`}\r\n            className=\"p-3 xl:p-4 rounded bg-accordionBG\"\r\n          >\r\n            <AccordionTrigger className=\"text-white text-sm font-medium xl:text-base\">\r\n              {item.title}\r\n            </AccordionTrigger>\r\n            <AccordionContent className=\"text-white pt-3 text-sm font-medium xl:text-base\">\r\n              {item.content}\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        ))}\r\n      </Accordion>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FAQContainer;\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;;;;AACA,MAAM,eAAe;IACnB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2E;;;;;;0BAGzF,6LAAC,wIAAA,CAAA,YAAS;gBACR,MAAK,SAAS,yCAAyC;;gBACvD,WAAW;gBACX,WAAU;0BAET,8HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC,wIAAA,CAAA,gBAAa;wBAEZ,OAAO,CAAC,KAAK,EAAE,OAAO;wBACtB,WAAU;;0CAEV,6LAAC,wIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CACzB,KAAK,KAAK;;;;;;0CAEb,6LAAC,wIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CACzB,KAAK,OAAO;;;;;;;uBARV;;;;;;;;;;;;;;;;AAejB;KA5BM;uCA8BS"}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/OurWorldContainer.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { FaPlay, FaPause, FaVolumeUp, FaVolumeMute, FaExpand } from \"react-icons/fa\";\r\n\r\nconst OurWorldContainer = () => {\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [isMuted, setIsMuted] = useState(true);\r\n  const [player, setPlayer] = useState(null);\r\n  const [isControlsVisible, setIsControlsVisible] = useState(false);\r\n  const [currentTime, setCurrentTime] = useState(0);\r\n  const [duration, setDuration] = useState(0);\r\n  const videoContainerRef = useRef(null);\r\n  const progressRef = useRef(null);\r\n  const videoId = \"PWk1ZdCGgjA\";\r\n\r\n  // Load YouTube API\r\n  useEffect(() => {\r\n    // Create script element for YouTube API\r\n    const tag = document.createElement(\"script\");\r\n    tag.src = \"https://www.youtube.com/iframe_api\";\r\n    const firstScriptTag = document.getElementsByTagName(\"script\")[0];\r\n    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);\r\n\r\n    // Initialize player when API is ready\r\n    window.onYouTubeIframeAPIReady = () => {\r\n      new window.YT.Player(\"youtube-player\", {\r\n        videoId: videoId,\r\n        playerVars: {\r\n          autoplay: 0,\r\n          controls: 0,\r\n          modestbranding: 1,\r\n          rel: 0,\r\n          showinfo: 0,\r\n          fs: 0,\r\n          iv_load_policy: 3,\r\n          mute: 1\r\n        },\r\n        events: {\r\n          onReady: (event) => {\r\n            const ytPlayer = event.target;\r\n            setPlayer(ytPlayer);\r\n            setIsMuted(ytPlayer.isMuted());\r\n            setDuration(ytPlayer.getDuration());\r\n          },\r\n          onStateChange: (event) => {\r\n            setIsPlaying(event.data === window.YT.PlayerState.PLAYING);\r\n\r\n            // Start progress tracking when video is playing\r\n            if (event.data === window.YT.PlayerState.PLAYING) {\r\n              startProgressTracking(event.target);\r\n            }\r\n          }\r\n        }\r\n      });\r\n    };\r\n\r\n    // Cleanup\r\n    return () => {\r\n      window.onYouTubeIframeAPIReady = null;\r\n    };\r\n  }, []);\r\n\r\n  // Track video progress\r\n  const startProgressTracking = (ytPlayer) => {\r\n    const updateInterval = setInterval(() => {\r\n      if (ytPlayer && typeof ytPlayer.getCurrentTime === 'function') {\r\n        const current = ytPlayer.getCurrentTime();\r\n        const total = ytPlayer.getDuration();\r\n        setCurrentTime(current);\r\n        setDuration(total);\r\n\r\n        // Clear interval if video ended\r\n        if (ytPlayer.getPlayerState() !== window.YT.PlayerState.PLAYING) {\r\n          clearInterval(updateInterval);\r\n        }\r\n      }\r\n    }, 1000);\r\n\r\n    // Return cleanup function\r\n    return () => clearInterval(updateInterval);\r\n  };\r\n\r\n  // Handle play/pause\r\n  const handlePlayPause = () => {\r\n    if (!player) return;\r\n\r\n    if (isPlaying) {\r\n      player.pauseVideo();\r\n    } else {\r\n      player.playVideo();\r\n    }\r\n    setIsPlaying(!isPlaying);\r\n  };\r\n\r\n  // Handle mute/unmute\r\n  const handleMuteToggle = () => {\r\n    if (!player) return;\r\n\r\n    if (isMuted) {\r\n      player.unMute();\r\n    } else {\r\n      player.mute();\r\n    }\r\n    setIsMuted(!isMuted);\r\n  };\r\n\r\n  // Handle fullscreen\r\n  const handleFullscreen = () => {\r\n    if (!videoContainerRef.current) return;\r\n\r\n    if (document.fullscreenElement) {\r\n      document.exitFullscreen();\r\n    } else {\r\n      videoContainerRef.current.requestFullscreen();\r\n    }\r\n  };\r\n\r\n  // Handle seeking\r\n  const handleSeek = (e) => {\r\n    if (!player || !progressRef.current) return;\r\n\r\n    const progressBar = progressRef.current;\r\n    const rect = progressBar.getBoundingClientRect();\r\n    const clickPosition = (e.clientX - rect.left) / rect.width;\r\n    const seekToTime = duration * clickPosition;\r\n\r\n    player.seekTo(seekToTime, true);\r\n    setCurrentTime(seekToTime);\r\n  };\r\n\r\n  // Format time (seconds to MM:SS)\r\n  const formatTime = (timeInSeconds) => {\r\n    const minutes = Math.floor(timeInSeconds / 60);\r\n    const seconds = Math.floor(timeInSeconds % 60);\r\n    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;\r\n  };\r\n\r\n  // Show/hide controls on hover\r\n  const showControls = () => setIsControlsVisible(true);\r\n  const hideControls = () => setIsControlsVisible(false);\r\n\r\n  return (\r\n    <div className=\"mx-5 py-4 text-center\">\r\n      <h3 className=\"font-bold text-xl mb-3 md:text-2xl lg:text-3xl xl:text-3xl text-textBluePrimary\">\r\n        A Glimpse Into Our World\r\n      </h3>\r\n      <p className=\"font-bold text-sm mb-6 md:text-sm lg:text-base xl:text-base text-customGrey100\">\r\n        Take a closer look at the world of\r\n        The Skyline Aviation Club. \r\n        Experience our facilities, dedicated instructors, and the journey of\r\n        shaping future aviation professionals.\r\n      </p>\r\n\r\n      {/* Video Container with Custom Controls */}\r\n      <div\r\n        ref={videoContainerRef}\r\n        className=\"relative md:w-4/5 w-full mx-auto rounded-lg overflow-hidden shadow-lg\"\r\n        onMouseEnter={showControls}\r\n        onMouseLeave={hideControls}\r\n        onFocus={showControls}\r\n        onBlur={hideControls}\r\n        onTouchStart={showControls}\r\n        role=\"region\"\r\n        aria-label=\"Video player\"\r\n      >\r\n        {/* YouTube Player */}\r\n        <div className=\"aspect-video bg-black\">\r\n          <div id=\"youtube-player\" className=\"w-full h-full\" aria-hidden=\"true\"></div>\r\n        </div>\r\n\r\n        {/* Loading indicator */}\r\n        {!player && (\r\n          <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n            <div className=\"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin\"></div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Custom Controls */}\r\n        <div\r\n          className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 transition-opacity duration-300 ${isControlsVisible ? 'opacity-100' : 'opacity-0'}`}\r\n          aria-hidden={!isControlsVisible}\r\n        >\r\n          {/* Progress Bar */}\r\n          <div\r\n            ref={progressRef}\r\n            className=\"w-full h-1 bg-gray-600 rounded-full mb-3 cursor-pointer\"\r\n            onClick={handleSeek}\r\n            onKeyDown={(e) => {\r\n              if (e.key === 'Enter' || e.key === ' ') {\r\n                handleSeek(e);\r\n              }\r\n            }}\r\n            role=\"slider\"\r\n            aria-label=\"Video progress\"\r\n            aria-valuemin=\"0\"\r\n            aria-valuemax={duration}\r\n            aria-valuenow={currentTime}\r\n            tabIndex=\"0\"\r\n          >\r\n            <div\r\n              className=\"h-full bg-blue-500 rounded-full\"\r\n              style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}\r\n            ></div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Play/Pause Button */}\r\n              <button\r\n                onClick={handlePlayPause}\r\n                className=\"text-white hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1\"\r\n                aria-label={isPlaying ? \"Pause video\" : \"Play video\"}\r\n                title={isPlaying ? \"Pause\" : \"Play\"}\r\n              >\r\n                {isPlaying ? <FaPause /> : <FaPlay />}\r\n              </button>\r\n\r\n              {/* Mute/Unmute Button */}\r\n              <button\r\n                onClick={handleMuteToggle}\r\n                className=\"text-white hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1\"\r\n                aria-label={isMuted ? \"Unmute video\" : \"Mute video\"}\r\n                title={isMuted ? \"Unmute\" : \"Mute\"}\r\n              >\r\n                {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}\r\n              </button>\r\n\r\n              {/* Time Display */}\r\n              <span className=\"text-white text-xs\">\r\n                {formatTime(currentTime)} / {formatTime(duration)}\r\n              </span>\r\n            </div>\r\n\r\n            {/* Fullscreen Button */}\r\n            <button\r\n              onClick={handleFullscreen}\r\n              className=\"text-white hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1\"\r\n              aria-label=\"Toggle fullscreen\"\r\n              title=\"Fullscreen\"\r\n            >\r\n              <FaExpand />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default OurWorldContainer"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,oBAAoB;;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,UAAU;IAEhB,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,wCAAwC;YACxC,MAAM,MAAM,SAAS,aAAa,CAAC;YACnC,IAAI,GAAG,GAAG;YACV,MAAM,iBAAiB,SAAS,oBAAoB,CAAC,SAAS,CAAC,EAAE;YACjE,eAAe,UAAU,CAAC,YAAY,CAAC,KAAK;YAE5C,sCAAsC;YACtC,OAAO,uBAAuB;+CAAG;oBAC/B,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,kBAAkB;wBACrC,SAAS;wBACT,YAAY;4BACV,UAAU;4BACV,UAAU;4BACV,gBAAgB;4BAChB,KAAK;4BACL,UAAU;4BACV,IAAI;4BACJ,gBAAgB;4BAChB,MAAM;wBACR;wBACA,QAAQ;4BACN,OAAO;+DAAE,CAAC;oCACR,MAAM,WAAW,MAAM,MAAM;oCAC7B,UAAU;oCACV,WAAW,SAAS,OAAO;oCAC3B,YAAY,SAAS,WAAW;gCAClC;;4BACA,aAAa;+DAAE,CAAC;oCACd,aAAa,MAAM,IAAI,KAAK,OAAO,EAAE,CAAC,WAAW,CAAC,OAAO;oCAEzD,gDAAgD;oCAChD,IAAI,MAAM,IAAI,KAAK,OAAO,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE;wCAChD,sBAAsB,MAAM,MAAM;oCACpC;gCACF;;wBACF;oBACF;gBACF;;YAEA,UAAU;YACV;+CAAO;oBACL,OAAO,uBAAuB,GAAG;gBACnC;;QACF;sCAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,wBAAwB,CAAC;QAC7B,MAAM,iBAAiB,YAAY;YACjC,IAAI,YAAY,OAAO,SAAS,cAAc,KAAK,YAAY;gBAC7D,MAAM,UAAU,SAAS,cAAc;gBACvC,MAAM,QAAQ,SAAS,WAAW;gBAClC,eAAe;gBACf,YAAY;gBAEZ,gCAAgC;gBAChC,IAAI,SAAS,cAAc,OAAO,OAAO,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE;oBAC/D,cAAc;gBAChB;YACF;QACF,GAAG;QAEH,0BAA0B;QAC1B,OAAO,IAAM,cAAc;IAC7B;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ;QAEb,IAAI,WAAW;YACb,OAAO,UAAU;QACnB,OAAO;YACL,OAAO,SAAS;QAClB;QACA,aAAa,CAAC;IAChB;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ;QAEb,IAAI,SAAS;YACX,OAAO,MAAM;QACf,OAAO;YACL,OAAO,IAAI;QACb;QACA,WAAW,CAAC;IACd;IAEA,oBAAoB;IACpB,MAAM,mBAAmB;QACvB,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAEhC,IAAI,SAAS,iBAAiB,EAAE;YAC9B,SAAS,cAAc;QACzB,OAAO;YACL,kBAAkB,OAAO,CAAC,iBAAiB;QAC7C;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,UAAU,CAAC,YAAY,OAAO,EAAE;QAErC,MAAM,cAAc,YAAY,OAAO;QACvC,MAAM,OAAO,YAAY,qBAAqB;QAC9C,MAAM,gBAAgB,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;QAC1D,MAAM,aAAa,WAAW;QAE9B,OAAO,MAAM,CAAC,YAAY;QAC1B,eAAe;IACjB;IAEA,iCAAiC;IACjC,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;QAC3C,OAAO,GAAG,QAAQ,CAAC,EAAE,UAAU,KAAK,MAAM,KAAK,SAAS;IAC1D;IAEA,8BAA8B;IAC9B,MAAM,eAAe,IAAM,qBAAqB;IAChD,MAAM,eAAe,IAAM,qBAAqB;IAEhD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAkF;;;;;;0BAGhG,6LAAC;gBAAE,WAAU;0BAAiF;;;;;;0BAQ9F,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,cAAc;gBACd,cAAc;gBACd,SAAS;gBACT,QAAQ;gBACR,cAAc;gBACd,MAAK;gBACL,cAAW;;kCAGX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,IAAG;4BAAiB,WAAU;4BAAgB,eAAY;;;;;;;;;;;oBAIhE,CAAC,wBACA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAKnB,6LAAC;wBACC,WAAW,CAAC,mHAAmH,EAAE,oBAAoB,gBAAgB,aAAa;wBAClL,eAAa,CAAC;;0CAGd,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,SAAS;gCACT,WAAW,CAAC;oCACV,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,GAAG,KAAK,KAAK;wCACtC,WAAW;oCACb;gCACF;gCACA,MAAK;gCACL,cAAW;gCACX,iBAAc;gCACd,iBAAe;gCACf,iBAAe;gCACf,UAAS;0CAET,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,WAAW,IAAI,AAAC,cAAc,WAAY,MAAM,EAAE,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAI5E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,cAAY,YAAY,gBAAgB;gDACxC,OAAO,YAAY,UAAU;0DAE5B,0BAAY,6LAAC,iJAAA,CAAA,UAAO;;;;yEAAM,6LAAC,iJAAA,CAAA,SAAM;;;;;;;;;;0DAIpC,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,cAAY,UAAU,iBAAiB;gDACvC,OAAO,UAAU,WAAW;0DAE3B,wBAAU,6LAAC,iJAAA,CAAA,eAAY;;;;yEAAM,6LAAC,iJAAA,CAAA,aAAU;;;;;;;;;;0DAI3C,6LAAC;gDAAK,WAAU;;oDACb,WAAW;oDAAa;oDAAI,WAAW;;;;;;;;;;;;;kDAK5C,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;wCACX,OAAM;kDAEN,cAAA,6LAAC,iJAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;GAnPM;KAAA;uCAqPS"}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/card.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"rounded-xl bg-card\", className)}\r\n    {...props} />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props} />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props} />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtD,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAEb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAEb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAEb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2\",\r\n        sm: \"h-8 rounded-md px-3 text-xs\",\r\n        lg: \"h-10 rounded-md px-8\",\r\n        icon: \"h-9 w-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Button = React.forwardRef(({ className, variant, size, asChild = false, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  return (\r\n    (<Comp\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      ref={ref}\r\n      {...props} />)\r\n  );\r\n})\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,uBAAS,8JAAM,UAAU,MAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxF,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACG,6LAAC;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAEf;;AACA,OAAO,WAAW,GAAG"}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/carousel.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport useEmblaCarousel from \"embla-carousel-react\";\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\n\r\nconst CarouselContext = React.createContext(null)\r\n\r\nfunction useCarousel() {\r\n  const context = React.useContext(CarouselContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nconst Carousel = React.forwardRef((\r\n  {\r\n    orientation = \"horizontal\",\r\n    opts,\r\n    setApi,\r\n    plugins,\r\n    className,\r\n    children,\r\n    ...props\r\n  },\r\n  ref\r\n) => {\r\n  const [carouselRef, api] = useEmblaCarousel({\r\n    ...opts,\r\n    axis: orientation === \"horizontal\" ? \"x\" : \"y\",\r\n  }, plugins)\r\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false)\r\n  const [canScrollNext, setCanScrollNext] = React.useState(false)\r\n\r\n  const onSelect = React.useCallback((api) => {\r\n    if (!api) {\r\n      return\r\n    }\r\n\r\n    setCanScrollPrev(api.canScrollPrev())\r\n    setCanScrollNext(api.canScrollNext())\r\n  }, [])\r\n\r\n  const scrollPrev = React.useCallback(() => {\r\n    api?.scrollPrev()\r\n  }, [api])\r\n\r\n  const scrollNext = React.useCallback(() => {\r\n    api?.scrollNext()\r\n  }, [api])\r\n\r\n  const handleKeyDown = React.useCallback((event) => {\r\n    if (event.key === \"ArrowLeft\") {\r\n      event.preventDefault()\r\n      scrollPrev()\r\n    } else if (event.key === \"ArrowRight\") {\r\n      event.preventDefault()\r\n      scrollNext()\r\n    }\r\n  }, [scrollPrev, scrollNext])\r\n\r\n  React.useEffect(() => {\r\n    if (!api || !setApi) {\r\n      return\r\n    }\r\n\r\n    setApi(api)\r\n  }, [api, setApi])\r\n\r\n  React.useEffect(() => {\r\n    if (!api) {\r\n      return\r\n    }\r\n\r\n    onSelect(api)\r\n    api.on(\"reInit\", onSelect)\r\n    api.on(\"select\", onSelect)\r\n\r\n    return () => {\r\n      api?.off(\"select\", onSelect)\r\n    };\r\n  }, [api, onSelect])\r\n\r\n  return (\r\n    (<CarouselContext.Provider\r\n      value={{\r\n        carouselRef,\r\n        api: api,\r\n        opts,\r\n        orientation:\r\n          orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\r\n        scrollPrev,\r\n        scrollNext,\r\n        canScrollPrev,\r\n        canScrollNext,\r\n      }}>\r\n      <div\r\n        ref={ref}\r\n        onKeyDownCapture={handleKeyDown}\r\n        className={cn(\"relative\", className)}\r\n        role=\"region\"\r\n        aria-roledescription=\"carousel\"\r\n        {...props}>\r\n        {children}\r\n      </div>\r\n    </CarouselContext.Provider>)\r\n  );\r\n})\r\nCarousel.displayName = \"Carousel\"\r\n\r\nconst CarouselContent = React.forwardRef(({ className, ...props }, ref) => {\r\n  const { carouselRef, orientation } = useCarousel()\r\n\r\n  return (\r\n    (<div ref={carouselRef} className=\"overflow-hidden\">\r\n      <div\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex\",\r\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\r\n          className\r\n        )}\r\n        {...props} />\r\n    </div>)\r\n  );\r\n})\r\nCarouselContent.displayName = \"CarouselContent\"\r\n\r\nconst CarouselItem = React.forwardRef(({ className, ...props }, ref) => {\r\n  const { orientation } = useCarousel()\r\n\r\n  return (\r\n    (<div\r\n      ref={ref}\r\n      role=\"group\"\r\n      aria-roledescription=\"slide\"\r\n      className={cn(\r\n        \"min-w-0 shrink-0 grow-0 basis-full\",\r\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\r\n        className\r\n      )}\r\n      {...props} />)\r\n  );\r\n})\r\nCarouselItem.displayName = \"CarouselItem\"\r\n\r\nconst CarouselPrevious = React.forwardRef(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\r\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\r\n\r\n  return (\r\n    (<Button\r\n      ref={ref}\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\"absolute rounded-full\", orientation === \"horizontal\"\r\n        ? \"-left-12 top-1/2 -translate-y-1/2\"\r\n        : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\", className)}\r\n      disabled={!canScrollPrev}\r\n      onClick={scrollPrev}\r\n      {...props}>\r\n      <ChevronLeft className=\"h-6 w-6 text-textBluePrimary\" />\r\n      <span className=\"sr-only\">Previous slide</span>\r\n    </Button>)\r\n  );\r\n})\r\nCarouselPrevious.displayName = \"CarouselPrevious\"\r\n\r\nconst CarouselNext = React.forwardRef(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\r\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\r\n\r\n  return (\r\n    (<Button\r\n      ref={ref}\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\"absolute rounded-full\", orientation === \"horizontal\"\r\n        ? \"-right-12 top-1/2 -translate-y-1/2\"\r\n        : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\", className)}\r\n      disabled={!canScrollNext}\r\n      onClick={scrollNext}\r\n      {...props}>\r\n      <ChevronRight className=\"h-6 w-6 text-textBluePrimary\" />\r\n      <span className=\"sr-only\">Next slide</span>\r\n    </Button>)\r\n  );\r\n})\r\nCarouselNext.displayName = \"CarouselNext\"\r\n\r\nexport { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext };\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAIA;AACA;AAJA;AACA;AAAA;;;AAHA;;;;;;AAQA,MAAM,gCAAkB,8JAAM,aAAa,CAAC;AAE5C,SAAS;;IACP,MAAM,UAAU,8JAAM,UAAU,CAAC;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,MAAM,yBAAW,IAAA,8JAAM,UAAU,UAAC,CAChC,EACE,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;;IAEA,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,yLAAA,CAAA,UAAgB,AAAD,EAAE;QAC1C,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GAAG;IACH,MAAM,CAAC,eAAe,iBAAiB,GAAG,8JAAM,QAAQ,CAAC;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,8JAAM,QAAQ,CAAC;IAEzD,MAAM,WAAW,8JAAM,WAAW;0CAAC,CAAC;YAClC,IAAI,CAAC,KAAK;gBACR;YACF;YAEA,iBAAiB,IAAI,aAAa;YAClC,iBAAiB,IAAI,aAAa;QACpC;yCAAG,EAAE;IAEL,MAAM,aAAa,8JAAM,WAAW;4CAAC;YACnC,KAAK;QACP;2CAAG;QAAC;KAAI;IAER,MAAM,aAAa,8JAAM,WAAW;4CAAC;YACnC,KAAK;QACP;2CAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,8JAAM,WAAW;+CAAC,CAAC;YACvC,IAAI,MAAM,GAAG,KAAK,aAAa;gBAC7B,MAAM,cAAc;gBACpB;YACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;gBACrC,MAAM,cAAc;gBACpB;YACF;QACF;8CAAG;QAAC;QAAY;KAAW;IAE3B,8JAAM,SAAS;8BAAC;YACd,IAAI,CAAC,OAAO,CAAC,QAAQ;gBACnB;YACF;YAEA,OAAO;QACT;6BAAG;QAAC;QAAK;KAAO;IAEhB,8JAAM,SAAS;8BAAC;YACd,IAAI,CAAC,KAAK;gBACR;YACF;YAEA,SAAS;YACT,IAAI,EAAE,CAAC,UAAU;YACjB,IAAI,EAAE,CAAC,UAAU;YAEjB;sCAAO;oBACL,KAAK,IAAI,UAAU;gBACrB;;QACF;6BAAG;QAAC;QAAK;KAAS;IAElB,qBACG,6LAAC,gBAAgB,QAAQ;QACxB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBACA,cAAA,6LAAC;YACC,KAAK;YACL,kBAAkB;YAClB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACpB,GAAG,KAAK;sBACR;;;;;;;;;;;AAIT;;QAhF6B,yLAAA,CAAA,UAAgB;;;;QAAhB,yLAAA,CAAA,UAAgB;;;;AAiF7C,SAAS,WAAW,GAAG;AAEvB,MAAM,gCAAkB,IAAA,8JAAM,UAAU,WAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IACjE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACG,6LAAC;QAAI,KAAK;QAAa,WAAU;kBAChC,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAGjB;;QAduC;;;;QAAA;;;;AAevC,gBAAgB,WAAW,GAAG;AAE9B,MAAM,6BAAe,IAAA,8JAAM,UAAU,WAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC9D,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACG,6LAAC;QACA,KAAK;QACL,MAAK;QACL,wBAAqB;QACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAEf;;QAd0B;;;;QAAA;;;;AAe1B,aAAa,WAAW,GAAG;AAE3B,MAAM,iCAAmB,IAAA,8JAAM,UAAU,WAAC,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,MAAM,EAAE,GAAG,OAAO,EAAE;;IACtG,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACG,6LAAC,qIAAA,CAAA,SAAM;QACN,KAAK;QACL,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,gBAAgB,eACnD,sCACA,+CAA+C;QACnD,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BACT,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;;QAjBqD;;;;QAAA;;;;AAkBrD,iBAAiB,WAAW,GAAG;AAE/B,MAAM,6BAAe,IAAA,8JAAM,UAAU,WAAC,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,MAAM,EAAE,GAAG,OAAO,EAAE;;IAClG,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACG,6LAAC,qIAAA,CAAA,SAAM;QACN,KAAK;QACL,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,gBAAgB,eACnD,uCACA,kDAAkD;QACtD,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BACT,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;0BACxB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;;QAjBqD;;;;QAAA;;;;AAkBrD,aAAa,WAAW,GAAG"}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/landingPageData.jsx"], "sourcesContent": ["// Calculate age statically - will be updated when needed\r\nfunction calculateAge() {\r\n  const dob = new Date(\"1987-10-15\");\r\n  const now = new Date();\r\n  let age = now.getFullYear() - dob.getFullYear();\r\n\r\n  if (\r\n    now.getMonth() < dob.getMonth() ||\r\n    (now.getMonth() === dob.getMonth() && now.getDate() < dob.getDate())\r\n  ) {\r\n    age--;\r\n  }\r\n  return age;\r\n}\r\n\r\nconst age = calculateAge();\r\n\r\nconst aboutUs = [\r\n  {\r\n    title: \"World-Class Training Facilities\",\r\n    desc: \"At The Skyline Aviation Club, we pride ourselves on providing cutting-edge facilities that mirror real-world aviation environments. Train with state-of-the-art flight simulators, a fleet of modern training aircraft, and classrooms equipped with the latest learning technologies. Our facilities are designed to ensure you are prepared for every challenge the skies may bring.\",\r\n    image: \"/assets/about3.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n  {\r\n    title: \"Proven Track Records\",\r\n    desc: `Since last ${age} years, The Skyline Aviation Club has maintained an outstanding track record of graduate success rate, with alumni now flying for leading global airlines. Our hands-on training approach, experienced instructors, and tailored curriculum ensure you not only pass your certifications but also excel in competitive aviation careers.`,\r\n    image: \"/About_Us.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n  {\r\n    title: \"Comprehensive Student Support\",\r\n    desc: \"From the moment you enroll to the day you land your dream job, we're here for you. Our dedicated student support team helps with admissions, financial planning, and career placement, ensuring your journey is as smooth as your takeoff. With The Skyline Aviation Club, you are mentored throughout your aviation and airline career.\",\r\n    image: \"/assets/about1.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n  {\r\n    title: \"Globally Recognised Certifications\",\r\n    desc: \"The Skyline Aviation Club offers certifications and licences that are recognised under International Civil Aviation Organization (ICAO) and respected worldwide. From Private Pilot licences (PPL) to Instrument Rating (IR) to Commercial Pilot licences (CPL) and more, our programs meet the highest international standards, opening doors to a future in aviation, no matter where you want to fly.\",\r\n    image: \"/assets/about2.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n];\r\n\r\nconst tableData = [\r\n  {\r\n    startDate: \"15th April\",\r\n    endDate: \"5th April\",\r\n  },\r\n  {\r\n    startDate: \"1st May\",\r\n    endDate: \"20th April\",\r\n  },\r\n  {\r\n    startDate: \"15th June\",\r\n    endDate: \"5th June\",\r\n  },\r\n  {\r\n    startDate: \"15th August\",\r\n    endDate: \"5th August\",\r\n  },\r\n  {\r\n    startDate: \"15th October\",\r\n    endDate: \"5th October\",\r\n  },\r\n  {\r\n    startDate: \"15th December\",\r\n    endDate: \"5th December\",\r\n  },\r\n];\r\n\r\nconst coursesData = [\r\n  {\r\n    id: 1,\r\n    slug: \"indian-commercial-pilot-licence-course\",\r\n    title: \"DGCA\",\r\n    subTitle: \"Indian Commercial Pilot Licence Course\",\r\n    desc: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aircraft & Engine (Technical) General & Specific\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Radio Telephonic\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/cptManek2.jpg\",\r\n    bgImage: \"/assets/courses/IndianCommercialPilot.jpg\",\r\n  },\r\n  {\r\n    id: 2,\r\n    slug: \"american-commercial-pilots-licence\",\r\n    title: \"FAA USA\",\r\n    subTitle: \"American Commercial Pilot Licence Course\",\r\n    desc: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/courseTwoImage.png\",\r\n    bgImage: \"/assets/courses/AmericanCommercialPilot.jpg\",\r\n  },\r\n  {\r\n    id: 3,\r\n    slug: \"aircraft-flight-dispatcher-licence-course\",\r\n    title: \"FAA USA\",\r\n    subTitle: \"Aircraft/Flight Dispatcher Licence Course\",\r\n    desc: \"An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Flight Training\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/dispatch.jpg\",\r\n    bgImage: \"/assets/courses/flightDispatcher.jpg\",\r\n  },\r\n  {\r\n    id: 4,\r\n    slug: \"radio-telephony-r-aeromobile-frtol-licence\",\r\n    title: \"DGCA/WPC\",\r\n    subTitle: \"Radio Telephony (R) Aeromobile/FRTOL Licence Course\",\r\n    desc: \"This is a professional course of international standards as per the general guidelines prescribed under international radio regulations applicable to the aeronautical mobile service.\",\r\n    topics: (\r\n      <>\r\n        <h3 className=\"mb-1\">Part 1: Practical Test in Regulation and Procedure</h3>\r\n        <h3 className=\"mb-1\">Part 2: Oral Examination</h3>\r\n        <ul className=\"flex flex-wrap gap-2\">\r\n          <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n            Regulation & Procedure\r\n          </li>\r\n          <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n            Radio Principles & Practice\r\n          </li>\r\n        </ul>\r\n      </>\r\n    ),\r\n    image: \"/assets/courseFourImage.png\",\r\n    bgImage: \"/assets/courses/RadioTelephony.jpg\",\r\n  },\r\n  {\r\n    id: 5,\r\n    slug: \"commercial-pilot-licence-package-program\",\r\n    title: \"Inclusive of Indian CPL + American CPL or Canadian CPL\",\r\n    subTitle: \"Commercial Pilot Licence Package Program\",\r\n    desc: \"The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/cplProgram.jpg\",\r\n    bgImage: \"/assets/courses/CPLProgram.jpg\",\r\n  },\r\n  {\r\n    id: 6,\r\n    slug: \"airhostess-flight-purser-training-course\",\r\n    title: \"Cabin Crew Training Program\",\r\n    subTitle: \"Airhostess/Flight Purser Training Course\",\r\n    desc: \"Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.\",\r\n    topics: null,\r\n    image: \"/assets/courseSixImage.png\",\r\n    bgImage: \"/assets/courses/airhostess.jpg\",\r\n  },\r\n  {\r\n    id: 7,\r\n    slug: \"airport-ground-staff-course\",\r\n    title: \"Airport Ground Services\",\r\n    subTitle: \"Airport Ground Staff Course\",\r\n    desc: \"If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/groundStaffHome.jpg\",\r\n    bgImage: \"/assets/courses/AirportGroundStaff.jpeg\",\r\n  }, \r\n  {\r\n    id: 8,\r\n    slug: \"aviation-foundation-course\",\r\n    title: \"Summer Vacation Training Program\",\r\n    subTitle:\r\n      \"Aviation Foundation Course\",\r\n    desc: \"This course builds a strong foundation for students aspiring to airline careers like Pilot, Dispatcher, Air Hostess, Ground Staff, AME, ATC Officer, and more.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/foundationCourse.jpg\",\r\n    bgImage: \"/assets/courses/AviationFoundation.jpg\",\r\n  },\r\n  {\r\n    id: 9,\r\n    slug: \"two-day-aeroplane-or-helicopter-training-workshop\",\r\n    title: \"Aeroplane/Helicopter Orientation Training\",\r\n    subTitle: \"Two Day Aeroplane/Helicopter Training Workshop\",\r\n    desc: \"Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/courseDetailBanner.png\",\r\n    bgImage: \"/assets/courses/HelicopterWorkshop.jpg\",\r\n  },\r\n  {\r\n    id: 10,\r\n    slug: \"foreign-commercial-pilot-licence-conversion-course\",\r\n    title: \"Indian Commercial Pilot Licence (DGCA)\",\r\n    subTitle: \"Foreign Commercial Pilot Licence Conversion Course\",\r\n    desc: \"The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/courseThreeImage.png\",\r\n    bgImage: \"/assets/courses/ForeignCplConversion.jpg\",\r\n  },\r\n  {\r\n    id: 11,\r\n    slug: \"helicopter-commercial-pilot-licence-course\",\r\n    title: \"HCPL\",\r\n    subTitle: \"Helicopter Commercial Pilot Licence Course\",\r\n    desc: \"The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Helicopter Aerodynamics\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Helicopter Flight Rules & Regulations\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical General (Helicopter)\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/helicopterImage.webp\",\r\n    bgImage: \"/assets/courses/HelicopterCourses.jpg\",\r\n  },\r\n];\r\n\r\nconst alumniData = [\r\n  {\r\n    title: \"Captain Monalisa Parmar\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I am very happy to say that I am selected as FLIGHT DISPATCHER by\r\n          SPICE JET Airline. Currently I am posted at Surat Airport. Your extra\r\n          ordinary training in Flight Dispatch has really helped me in getting\r\n          job. Thank you very much.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Monalisa.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Himanshu Patel\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Capt. A. D. Manek Sir, It was my pleasure to be a part of The Skyline\r\n          Aviation Club, Mumbai. It played a role of a stepping stone for me to\r\n          be a part of Aviation Industry today. I am glad that we have such\r\n          clubs in India that encourages aviation enthusiasts to rise & fly high\r\n          in their professions. An initiative to begin with something new &\r\n          efficient is where you begin you way to Skyline. I am pleased to be\r\n          the part of your team Capt. A. D. Manek. I wish a very good luck to\r\n          the future generation of trainees at The Skyline Aviation Club.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Himanshu.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Mukarram Electricwala\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I am grateful to Sir and The Skyline Aviation Club for giving me the\r\n          wonderful opportunity to teach at Skyline under Manek sirs guidance.\r\n          Lastly I would like to thank Manek Sir, The Skyline Aviation Club and its\r\n          support staff for just being there whenever I required help I wish Sir\r\n          and The Skyline Aviation Club all the happiness and best of luck for the\r\n          future!”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Mukarram.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Jignesh Devadhia\",\r\n    subTitle: \" F.A.A. Commercial Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I have completed my training and qualified as F.A.A. Commercial Pilot\r\n          and I am about to get my Indian Commercial Pilot Licence. With your\r\n          prefect training I was able to complete my Flight Training and passing\r\n          written exams in shortest possible time and hence I was able to break\r\n          record of my Flight School. Thank you very much for all.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Jignesh.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Niraj Patel\",\r\n    subTitle: \"First Indian Women Helicopter Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I have completed my training and qualified as F.A.A. Commercial Pilot\r\n          and I am about to get my Indian Commercial Pilot Licence. With your\r\n          prefect training I was able to complete my Flight Training and passing\r\n          written exams in shortest possible time and hence I was able to break\r\n          record of my Flight School. Thank you very much for all.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Niraj.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Aruna Kandarpa\",\r\n    subTitle: \"First Indian Women Helicopter Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I have completed my training and qualified as F.A.A. Commercial Pilot\r\n          and I am about to get my Indian Commercial Pilot Licence. With your\r\n          prefect training I was able to complete my Flight Training and passing\r\n          written exams in shortest possible time and hence I was able to break\r\n          record of my Flight School. Thank you very much for all.”\r\n        </p>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “The club and it's faculty have always been dear to my heart and I\r\n          congratulate them on their 20th year of success and achievement.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Aruna.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Kritika Parmar\",\r\n    subTitle: \"First Indian Women Helicopter Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I am proud to be student of The Skyline Aviation Club. With right\r\n          information and guidance and training I am able to complete 130 hrs of\r\n          flight training at Fresno, CA, U.S.A. I am really thankful to Capt. A.\r\n          D. Manek for personally guiding me to obtain education loan of\r\n          Rs.20,00,000/- from Government of Gujarat.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Kritika.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Bradley Verughese Matthew\",\r\n    subTitle: \"FAA Flight Dispatcher Licence\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Respected Sir, I am glad to tell you that my studies and trip to USA\r\n          was a success with your guidance and inspiration ! As you know I have\r\n          received my FAA Flight Dispatcher Licence. So right now I am hungry to\r\n          get into job and prove and serve myself for Aviation”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Bradley.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Mervyn Mascarenhas\",\r\n    subTitle: \"FAA Aire Line Transport Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I began studying at The Skyline Aviation Club detailed way of\r\n          instructions and the ease with which he explained my quries is\r\n          remarkable. His extensive knowledge of the FAA instructions and\r\n          examinations is admirable. A great passion for what he do is visible\r\n          all around and his teaching standards are excellent. I am now\r\n          confident I will do very well in my FAA Aire Line Transport Pilot\r\n          Licence exams”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Mervyn.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Mihir Mistry\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Dear Capt.A.D.Manek, It's great to be a part of The Skyline Aviation Club\r\n          family and would specially like to thank you for being a father figure\r\n          and blend my life professionally. My success is only because of the\r\n          hardest efforts put in by you.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Mihir.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Captain Sagar K Karnik\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Dear Capt.A.D.Manek, It's great to be a part of The Skyline Aviation Club\r\n          family and would specially like to thank you for being a father figure\r\n          and blend my life professionally. My success is only because of the\r\n          hardest efforts put in by you.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Sagar.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n  {\r\n    title: \"Neelam Patel\",\r\n    subTitle: \"Flight Management\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I, Miss Neelam M.Patel, after graduation joined The Skyline Aviation Club\r\n          for In Flight Management Course, and completed the same within the\r\n          stipulated time. During the tenure of the course, I received precious\r\n          informative global knowledge, instruction resulting in my selection as\r\n          an Air Hostess of Air India Ltd. I would like o thank Capt.A.D.Manek\r\n          and all the instructors for their co-operation and valuable\r\n          informative knowledge passed on to me for which I am grateful to The Skyline Aviation Club.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Neelam.png\",\r\n    rating: 5.0,\r\n    reviewLink: \"\",\r\n  },\r\n];\r\n\r\nconst companyImagesArray = [\r\n  \"/assets/air_asia_logo.png\",\r\n  \"/assets/Air_India.svg\",\r\n  \"/assets/spicejet-logo.png\",\r\n  \"/assets/indigo.png\",\r\n  \"/assets/qatar.png\",\r\n  \"/assets/emirates.png\",\r\n];\r\n\r\nexport { aboutUs, tableData, coursesData, alumniData, companyImagesArray };\r\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;;;;;AACzD,SAAS;IACP,MAAM,MAAM,IAAI,KAAK;IACrB,MAAM,MAAM,IAAI;IAChB,IAAI,MAAM,IAAI,WAAW,KAAK,IAAI,WAAW;IAE7C,IACE,IAAI,QAAQ,KAAK,IAAI,QAAQ,MAC5B,IAAI,QAAQ,OAAO,IAAI,QAAQ,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO,IACjE;QACA;IACF;IACA,OAAO;AACT;AAEA,MAAM,MAAM;AAEZ,MAAM,UAAU;IACd;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM,CAAC,WAAW,EAAE,IAAI,wUAAwU,CAAC;QACjW,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;IACV;CACD;AAED,MAAM,YAAY;IAChB;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;CACD;AAED,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE;;8BACE,6LAAC;oBAAG,WAAU;8BAAO;;;;;;8BACrB,6LAAC;oBAAG,WAAU;8BAAO;;;;;;8BACrB,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC;4BAAG,WAAU;sCAA6I;;;;;;sCAG3J,6LAAC;4BAAG,WAAU;sCAA6I;;;;;;;;;;;;;;QAMjK,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UACE;QACF,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,6LAAC;YAAG,WAAU;;8BACZ,6LAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,6LAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,6LAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,6LAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,6LAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;QACP,SAAS;IACX;CACD;AAED,MAAM,aAAa;IACjB;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAY5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAU5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAS5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAS5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;;8BACE,6LAAC;oBAAE,WAAU;8BAA2D;;;;;;8BAOxE,6LAAC;oBAAE,WAAU;8BAA2D;;;;;;;;QAM5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAS5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAW5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,6LAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAW5E,OAAO;QACP,QAAQ;QACR,YAAY;IACd;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;CACD"}}, {"offset": {"line": 2062, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2068, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/CourseCarousel.jsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport {\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselNext,\r\n  CarouselPrevious,\r\n} from \"@/components/ui/carousel\";\r\nimport Image from \"next/image\";\r\nimport { coursesData } from \"@/constant/landingPageData\";\r\nimport Link from \"next/link\";\r\n\r\nconst CourseCarousel = () => {\r\n  const [api, setApi] = useState();\r\n  const [selectedIndex, setSelectedIndex] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (!api) {\r\n      return;\r\n    }\r\n\r\n    setSelectedIndex(api.selectedScrollSnap() + 1);\r\n\r\n    api.on(\"select\", () => {\r\n      setSelectedIndex(api.selectedScrollSnap() + 1);\r\n    });\r\n  }, [api]);\r\n\r\n  const scrollTo = (index) => {\r\n    if (api) {\r\n      api.scrollTo(index);\r\n    }\r\n  };\r\n  return (\r\n    <div className=\"md:w-11/12 lg:w-4/5 mx-auto\">\r\n      <Carousel\r\n        setApi={setApi}\r\n        className=\"shadow-cardsBoxShadow bg-white\"\r\n        opts={{\r\n          loop: true,\r\n        }}\r\n      >\r\n        <CarouselContent >\r\n          {coursesData.map((item, index) => (\r\n            <CarouselItem className=\"h-fit\" key={index}>\r\n              <Card>\r\n                <CardContent className=\"flex flex-col md:flex-row items-center gap-3 md:gap-6 p-3 md:p-5 rounded-lg\">\r\n                  <Image\r\n                    className=\"rounded-lg w-full h-[330px] md:w-[208px] md:h-[400px] md:basis-[40%] object-cover\"\r\n                    src={item.image}\r\n                    width={360}\r\n                    height={438}\r\n                    alt=\"cardImage\"\r\n                  />\r\n                  <div className=\"flex flex-col md:gap-2 md:basis-[60%]\">\r\n                    <h3 className=\"text-textBluePrimary font-bold mb-1 md:mb-0 text-base lg:text-xl\">\r\n                      {(item.subTitle).toUpperCase()}\r\n                    </h3>\r\n                    <h4 className=\"text-customBlack font-semibold mb-2 md:mb-0 text-sm leading-[20px] desktop:text-sm lg:text-lg\">\r\n                      {item.title}\r\n                    </h4>\r\n                    <p className=\"font-medium text-customBlack mb-2 md:mb-0 text-xs desktop:text-sm lg:text-base\">\r\n                      {item.desc}\r\n                    </p>\r\n                    {\r\n                      item.topics && (\r\n                        <div>\r\n                          <span className=\"text-sm text-customBlack text-light mb-1 block\">\r\n                            You&apos;ll Learn:\r\n                          </span>\r\n                          {item.topics}\r\n                        </div>\r\n                      )\r\n                    }\r\n                    <Link\r\n                      href={`/course/${item.slug}`}\r\n                      className=\"flex  gap-2 bg-buttonBGPrimary font-bold text-sm text-white py-2 px-6 w-fit  justify-between rounded-full border border-transparent mt-4\"\r\n                    >\r\n                      Learn More\r\n                    </Link>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </CarouselItem>\r\n          ))}\r\n        </CarouselContent>\r\n        <CarouselPrevious className=\" hidden md:flex border bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6 [&_svg]:mx-auto\" />\r\n        <CarouselNext className=\"hidden md:flex border bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6 [&_svg]:mx-auto\" />\r\n      </Carousel>\r\n      <div className=\"flex justify-center items-center mt-4 space-x-2\">\r\n        {coursesData.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => scrollTo(index)}\r\n            className={`${selectedIndex === index + 1\r\n              ? \"h-3 w-8 rounded-full bg-textBluePrimary\" // Active dot\r\n              : \"h-3 w-3 rounded-full bg-carouselDotsBG\" // Inactive dots\r\n              }`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CourseCarousel;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAOA;AACA;AACA;;;AAZA;;;;;;;AAcA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,KAAK;gBACR;YACF;YAEA,iBAAiB,IAAI,kBAAkB,KAAK;YAE5C,IAAI,EAAE,CAAC;4CAAU;oBACf,iBAAiB,IAAI,kBAAkB,KAAK;gBAC9C;;QACF;mCAAG;QAAC;KAAI;IAER,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK;YACP,IAAI,QAAQ,CAAC;QACf;IACF;IACA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,uIAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,WAAU;gBACV,MAAM;oBACJ,MAAM;gBACR;;kCAEA,6LAAC,uIAAA,CAAA,kBAAe;kCACb,sIAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,uIAAA,CAAA,eAAY;gCAAC,WAAU;0CACtB,cAAA,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,gIAAA,CAAA,UAAK;gDACJ,WAAU;gDACV,KAAK,KAAK,KAAK;gDACf,OAAO;gDACP,QAAQ;gDACR,KAAI;;;;;;0DAEN,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,AAAC,KAAK,QAAQ,CAAE,WAAW;;;;;;kEAE9B,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,IAAI;;;;;;oDAGV,KAAK,MAAM,kBACT,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAiD;;;;;;4DAGhE,KAAK,MAAM;;;;;;;kEAIlB,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;wDAC5B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;+BAjC4B;;;;;;;;;;kCA0CzC,6LAAC,uIAAA,CAAA,mBAAgB;wBAAC,WAAU;;;;;;kCAC5B,6LAAC,uIAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;0BAE1B,6LAAC;gBAAI,WAAU;0BACZ,sIAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,GAAG,sBACnB,6LAAC;wBAEC,SAAS,IAAM,SAAS;wBACxB,WAAW,GAAG,kBAAkB,QAAQ,IACpC,0CAA0C,aAAa;2BACvD,yCAAyC,gBAAgB;0BACzD;uBALC;;;;;;;;;;;;;;;;AAWjB;GA3FM;KAAA;uCA6FS"}}, {"offset": {"line": 2271, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/CoursesOffer.jsx"], "sourcesContent": ["import { ArrowRight } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport CourseCarousel from \"./CourseCarousel\";\r\n\r\nconst CoursesOffer = () => {\r\n  return (\r\n    <div className=\" max-w-7xl mx-auto px-4 py-8 md:p-8\">\r\n      <h3 className=\"font-bold text-xl md:text-2xl lg:text-3xl text-textBluePrimary text-center mb-1 md:mb-2\">\r\n        Courses we offer\r\n      </h3>\r\n      <h4 className=\"font-light text-customBlack text-center mb-4 text-base md:text-lg md:font-medium lg:text-3xl lg:font-light\">\r\n        Courses Designed to Help You Soar <br />\r\n        Take Off with The Skyline Aviation Club !\r\n      </h4>\r\n      <CourseCarousel />\r\n      <div className=\"justify-center flex\">\r\n        <Link\r\n          href='/course'\r\n          className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}\r\n        >\r\n          <span className=\"font-bold\">View Course Details</span>\r\n          <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CoursesOffer;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;;AAIA,MAAM,eAAe;IACnB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0F;;;;;;0BAGxG,6LAAC;gBAAG,WAAU;;oBAA6G;kCACvF,6LAAC;;;;;oBAAK;;;;;;;0BAG1C,6LAAC,uIAAA,CAAA,UAAc;;;;;0BACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAW,CAAC,kFAAkF,CAAC;;sCAE/F,6LAAC;4BAAK,WAAU;sCAAY;;;;;;sCAC5B,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKhC;KAtBM;uCAwBS"}}, {"offset": {"line": 2367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2373, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/AlumniCarousel.jsx"], "sourcesContent": ["\"use client\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport {\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselNext,\r\n  CarouselPrevious,\r\n} from \"@/components/ui/carousel\";\r\nimport Image from \"next/image\";\r\nimport { alumniData } from \"@/constant/landingPageData\";\r\nimport { Star } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nconst AlumniCarousel = () => {\r\n  const [api, setApi] = useState();\r\n  const [selectedIndex, setSelectedIndex] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (!api) {\r\n      return;\r\n    }\r\n\r\n    setSelectedIndex(api.selectedScrollSnap() + 1);\r\n\r\n    api.on(\"select\", () => {\r\n      setSelectedIndex(api.selectedScrollSnap() + 1);\r\n    });\r\n  }, [api]);\r\n\r\n  const scrollTo = (index) => {\r\n    if (api) {\r\n      api.scrollTo(index);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"p-3 md:bg-footerBG lg:p-8 md:bg-alumniCurve bg-cover bg-center\">\r\n      <h3 className=\"text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl md:mb-4 mb-2 text-center\">\r\n        5000+ Strong Alumnis\r\n      </h3>\r\n      <p className=\"text-customGrey100 font-medium lg:text-base md:text-sm text-base mb-8 text-center\">\r\n        Some testimonials by our alumnis!\r\n      </p>\r\n      <div className=\"w-full max-w-3xl mx-auto\">\r\n        <Carousel\r\n          setApi={setApi}\r\n          className=\"shadow-cardsBoxShadow\"\r\n          opts={{\r\n            loop: true,\r\n          }}\r\n        >\r\n          <CarouselContent className=\"flex justify-start\">\r\n            {alumniData.map((item, index) => (\r\n              <CarouselItem key={index} className=\"w-full md:w-1/2 flex-shrink-0 snap-start\">\r\n                <Card className=\"bg-white h-full\">\r\n                  <CardContent className=\"p-6 md:p-12 lg:p-12 flex flex-col items-center gap-6\">\r\n                    <div>\r\n                      <div className=\"flex items-center justify-center space-x-1 mb-3\">\r\n                        {Array.from({ length: 5 }).map((_, index) => (\r\n                          <Star\r\n                            className={`${index + 1 <= item.rating\r\n                              ? \"fill-fullStarBG text-fullStarBG\"\r\n                              : index + 0.5 < item.rating\r\n                                ? \"fill-halfStarBG text-halfStarBG\"\r\n                                : \"fill-emptyStarBG text-emptyStarBG\"\r\n                              } w-4 h-4`}\r\n                            key={index}\r\n                          />\r\n                        ))}\r\n                      </div>\r\n                      {item.desc}\r\n                    </div>\r\n                    <div>\r\n                      <Image\r\n                        src={item.image}\r\n                        width={100}\r\n                        height={100}\r\n                        alt=\"avatar\"\r\n                        className=\"rounded-full w-[60px] h-[60px] md:w-[100px] md:h-[100px] mx-auto mb-4\"\r\n                      />\r\n                      <h3 className=\"font-bold text-textBluePrimary text-center capitalize text-base lg:text-xl\">\r\n                        {item.title}\r\n                      </h3>\r\n                      <h4 className=\"font-medium text-customBlack text-center text-sm md:text-base lg:text-lg\">\r\n                        {item.subTitle}\r\n                      </h4>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              </CarouselItem>\r\n            ))}\r\n          </CarouselContent>\r\n          <CarouselPrevious className=\"hidden md:flex border-0 bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6\" />\r\n          <CarouselNext className=\"hidden md:flex border-0 bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6\" />\r\n        </Carousel>\r\n        <div className=\"flex justify-center items-center mt-4 space-x-2\">\r\n          {alumniData.map((_, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => scrollTo(index)}\r\n              aria-label={`Go to slide ${index + 1}`}    \r\n              className={`${selectedIndex === index + 1\r\n                  ? \"h-3 w-8 rounded-full bg-textBluePrimary\"\r\n                  : \"h-3 w-3 rounded-full bg-carouselDotsBG\"\r\n                } cursor-pointer transition-all duration-300 ease-in-out`}\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AlumniCarousel;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAOA;AACA;AAEA;AADA;;;AAXA;;;;;;;AAcA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,KAAK;gBACR;YACF;YAEA,iBAAiB,IAAI,kBAAkB,KAAK;YAE5C,IAAI,EAAE,CAAC;4CAAU;oBACf,iBAAiB,IAAI,kBAAkB,KAAK;gBAC9C;;QACF;mCAAG;QAAC;KAAI;IAER,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK;YACP,IAAI,QAAQ,CAAC;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0F;;;;;;0BAGxG,6LAAC;gBAAE,WAAU;0BAAoF;;;;;;0BAGjG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uIAAA,CAAA,WAAQ;wBACP,QAAQ;wBACR,WAAU;wBACV,MAAM;4BACJ,MAAM;wBACR;;0CAEA,6LAAC,uIAAA,CAAA,kBAAe;gCAAC,WAAU;0CACxB,sIAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,uIAAA,CAAA,eAAY;wCAAa,WAAU;kDAClC,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EACZ,MAAM,IAAI,CAAC;oEAAE,QAAQ;gEAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,qMAAA,CAAA,OAAI;wEACH,WAAW,GAAG,QAAQ,KAAK,KAAK,MAAM,GAClC,oCACA,QAAQ,MAAM,KAAK,MAAM,GACvB,oCACA,oCACH,QAAQ,CAAC;uEACP;;;;;;;;;;4DAIV,KAAK,IAAI;;;;;;;kEAEZ,6LAAC;;0EACC,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK,KAAK,KAAK;gEACf,OAAO;gEACP,QAAQ;gEACR,KAAI;gEACJ,WAAU;;;;;;0EAEZ,6LAAC;gEAAG,WAAU;0EACX,KAAK,KAAK;;;;;;0EAEb,6LAAC;gEAAG,WAAU;0EACX,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;uCA/BL;;;;;;;;;;0CAuCvB,6LAAC,uIAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,6LAAC,uIAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;kCACZ,sIAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,GAAG,sBAClB,6LAAC;gCAEC,SAAS,IAAM,SAAS;gCACxB,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;gCACtC,WAAW,GAAG,kBAAkB,QAAQ,IAClC,4CACA,yCACH,uDAAuD,CAAC;+BANtD;;;;;;;;;;;;;;;;;;;;;;AAanB;GAlGM;KAAA;uCAoGS"}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2598, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/BannerCompanySection.jsx"], "sourcesContent": ["import Image from 'next/image';\r\nimport { companyImagesArray } from \"@/constant/landingPageData\";\r\nconst BannerCompanySection = () => {\r\n  return (\r\n    <div className=\"bg-whiteOne p-[10px] md:p-8 rounded-[20px] shadow-md text-center mx-4 md:w-4/5 md:mx-auto absolute top-[-5rem] right-0 left-0\">\r\n      <p className=\"text-sm font-bold md:text-lg capitalize px-5 py-2\">\r\n        Helping aspiring aviators take off with top-notch training and career support.\r\n      </p>\r\n      <p className=\"text-md font-bold md:text-xl capitalize text-[#1420cd]\">\r\n        Our graduates hired by\r\n      </p>\r\n      <div className=\"grid grid-cols-3 md:grid-cols-3 lg:grid-cols-6 mt-2 gap-4 items-center justify-center\">\r\n        {companyImagesArray.map((item, index) => (\r\n          <div\r\n            className=\"p-2 md:py-3 md:px-4 h-full border rounded flex items-center\"\r\n            key={index}\r\n          >\r\n            <Image\r\n              src={item}\r\n              width={160}\r\n              height={30}\r\n              className=\"mx-auto h-7 md:h-10\"\r\n              alt=\"company\"\r\n            />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default BannerCompanySection"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AACA,MAAM,uBAAuB;IAC3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAoD;;;;;;0BAGjE,6LAAC;gBAAE,WAAU;0BAAyD;;;;;;0BAGtE,6LAAC;gBAAI,WAAU;0BACZ,sIAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;wBACC,WAAU;kCAGV,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,KAAI;;;;;;uBAPD;;;;;;;;;;;;;;;;AAcjB;KA3BM;uCA6BS"}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2672, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/table.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props} />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props} />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\", className)}\r\n    {...props} />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\r\n      \"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACvD,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAGf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;QACxE,GAAG,KAAK;;;;;;;AAEb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAEb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA;QAED,GAAG,KAAK;;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC9D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAEb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 2804, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2810, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/hooks/useYear.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\nexport function useYear(dobString) {\r\n  const dob = new Date(dobString); // Pass dob as \"1987-10-15\"\r\n  const [years, setYears] = useState(0);\r\n\r\n  useEffect(() => {\r\n    function calculateYears() {\r\n      const now = new Date();\r\n      let age = now.getFullYear() - dob.getFullYear();\r\n\r\n      if (\r\n        now.getMonth() < dob.getMonth() ||\r\n        (now.getMonth() === dob.getMonth() && now.getDate() < dob.getDate())\r\n      ) {\r\n        age--;\r\n      }\r\n      return age;\r\n    }\r\n\r\n    function getNextBirthday() {\r\n      const now = new Date();\r\n      let next = new Date(now.getFullYear(), dob.getMonth(), dob.getDate());\r\n      if (next <= now) {\r\n        next.setFullYear(next.getFullYear() + 1);\r\n      }\r\n      return next;\r\n    }\r\n\r\n    // Set initial years\r\n    setYears(calculateYears());\r\n\r\n    // Schedule update only at next birthday\r\n    const msUntilNextBirthday = getNextBirthday() - new Date();\r\n    const timer = setTimeout(() => {\r\n      setYears(calculateYears());\r\n    }, msUntilNextBirthday);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [dob]);\r\n\r\n  return years;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIO,SAAS,QAAQ,SAAS;;IAC/B,MAAM,MAAM,IAAI,KAAK,YAAY,2BAA2B;IAC5D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,SAAS;gBACP,MAAM,MAAM,IAAI;gBAChB,IAAI,MAAM,IAAI,WAAW,KAAK,IAAI,WAAW;gBAE7C,IACE,IAAI,QAAQ,KAAK,IAAI,QAAQ,MAC5B,IAAI,QAAQ,OAAO,IAAI,QAAQ,MAAM,IAAI,OAAO,KAAK,IAAI,OAAO,IACjE;oBACA;gBACF;gBACA,OAAO;YACT;YAEA,SAAS;gBACP,MAAM,MAAM,IAAI;gBAChB,IAAI,OAAO,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI,IAAI,OAAO;gBAClE,IAAI,QAAQ,KAAK;oBACf,KAAK,WAAW,CAAC,KAAK,WAAW,KAAK;gBACxC;gBACA,OAAO;YACT;YAEA,oBAAoB;YACpB,SAAS;YAET,wCAAwC;YACxC,MAAM,sBAAsB,oBAAoB,IAAI;YACpD,MAAM,QAAQ;2CAAW;oBACvB,SAAS;gBACX;0CAAG;YAEH;qCAAO,IAAM,aAAa;;QAC5B;4BAAG;QAAC;KAAI;IAER,OAAO;AACT;GAxCgB"}}, {"offset": {"line": 2861, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2867, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/WhyChooseContainer.jsx"], "sourcesContent": ["\r\nimport Image from \"next/image\";\r\nimport BannerCompanySection from \"./BannerCompanySection\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport { aboutUs, tableData } from \"@/constant/landingPageData\";\r\nimport Link from \"next/link\";\r\nimport { useYear } from \"@/hooks/useYear\";\r\n\r\nconst WhyChooseContainer = () => {\r\n  const age = useYear(\"1987-10-15\");\r\n\r\n  return (\r\n    <div className=\"p-4 md:p-8 lg:py-12 bg-whiteOne relative z-0\">\r\n      <div className=\"z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-[700px] md:bottom-[300px] lg:bottom-[300px] left-0 right-0 w-full h-[500px]\"></div>\r\n      <div className=\"z-[-1] lg:bg-dotsImage bg-no-repeat bg-cover bg-center absolute bottom-[500px] right-0 w-10 h-36\"></div>\r\n      <BannerCompanySection />\r\n      <div className=\"flex flex-col md:flex-row items-center justify-center text-center mt-36 sm:mt-48 lg:mt-24 mb-5\">\r\n        <Image\r\n          src=\"/skyline_logo.png\"\r\n          alt=\"Skyline Aviation Club Logo\"\r\n          width={150}\r\n          height={150}\r\n          className=\"w-24 h-24 md:w-36 md:h-36 lg:w-48 lg:h-48 object-contain\"\r\n        />\r\n        <div className=\"flex flex-col justify-center\">\r\n          <h3 className=\"text-footerPrimaryText mb-2 text-xl md:text-2xl lg:text-3xl font-bold text-center\">\r\n            Why Choose The Skyline Aviation Club?\r\n          </h3>\r\n          <p className=\"text-customBlack font-light text-base md:text-2xl lg:text-3xl text-center\">\r\n            The Clear Choice for Future Pilots !\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div className=\"flex flex-col gap-4 mb-8 md:mb-20 lg:mx-20\">\r\n        {aboutUs.map((item, index) => (\r\n          <div\r\n            className={`flex flex-col md:flex-row items-center gap-3 md:gap-8 ${index % 2 !== 0 ? \"md:flex-row-reverse\" : \"\"\r\n              }`}\r\n            key={index}\r\n          >\r\n            <Image src={item.image} alt=\"aboutImage\" width={1000} height={1000} className=\"rounded-lg w-[356px] h-[300px] md:w-[456px] md:h-[239px] \" />\r\n            <div className=\"flex gap-1 lg:gap-3 flex-col\">\r\n              <h3 className=\"font-bold text-base md:text-xl text-textBluePrimary\">\r\n                {item.title}\r\n              </h3>\r\n              <p className=\"font-medium text-sm lg:text-base text-customBlack\">\r\n                {item.desc}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n      <div className=\"lg:mx-12\">\r\n        <div className=\"flex gap-4 flex-col tablet:flex-col md:flex-row\">\r\n          <div className=\"min-h-[475px] flex flex-col justify-between bg-white py-6 px-2 md:px-4 rounded-2xl shadow-courseCardShadow flex-1 relative\">\r\n            <div className=\"lg:px-6\">\r\n              <h3 className=\"text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-7 md:mb-4\">\r\n                Our Yearly Training Programs Time Table\r\n              </h3>\r\n              <Table>\r\n                <TableHeader>\r\n                  <TableRow className=\"border-bottom border-footerBottomText\">\r\n                    <TableHead className=\"font-bold text-descLightBlack text-sm lg:text-base\">\r\n                      Batch Starts on\r\n                    </TableHead>\r\n                    <TableHead className=\"font-bold text-descLightBlack text-sm lg:text-base\">\r\n                      Last Date for Admission\r\n                    </TableHead>\r\n                  </TableRow>\r\n                </TableHeader>\r\n                <TableBody>\r\n                  {tableData.map((item, index) => (\r\n                    <TableRow key={index}>\r\n                      <TableCell className=\"text-xs lg:text-sm text-black font-medium\">\r\n                        {item.endDate}\r\n                      </TableCell>\r\n                      <TableCell className=\"text-xs lg:text-sm text-black font-medium\">\r\n                        {item.startDate}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))}\r\n                </TableBody>\r\n              </Table>\r\n            </div>\r\n            <div className=\"text-center flex justify-center mt-5\">\r\n              <Link\r\n                href=\"/enquire\"\r\n                className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}\r\n              >\r\n                <span className=\"font-bold\">Enquire Now</span>\r\n                <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n              </Link>\r\n            </div>\r\n          </div>\r\n          <div className=\"min-h-[475px] bg-white p-6 rounded-2xl flex flex-col justify-between shadow-courseCardShadow flex-1 relative\">\r\n            <div>\r\n\r\n              <h3 className=\"text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-3 md:mb-4\">\r\n                Free Aviation/Airline Career Counselling\r\n              </h3>\r\n              <p className=\"font-medium text-sm\">\r\n                Unsure where to start your aviation career? Our expert career\r\n                counseling services are designed to guide you every step of the\r\n                way. Whether you are aiming to become a Commercial Pilot, Flight\r\n                Dispatcher, Air Hostess, Flight Purser, Aeronautical Radio\r\n                Officer, Airport Ground Staff or aviation management professional,\r\n                we help you identify the right path based on your goals,\r\n                interests, and qualifications. With {age} Years of experience in\r\n                aviation training, we offer personalized advice on courses,\r\n                licences and certifications, and career opportunities. Let us help\r\n                you take off toward a high-flying career with clarity and\r\n                confidence !\r\n              </p>\r\n            </div>\r\n            <div className=\"text-center flex justify-center\">\r\n              <Link\r\n                href=\"/enquire\"\r\n                className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}\r\n              >\r\n                <span className=\"font-bold\">Enquire Now</span>\r\n                <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n              </Link>\r\n            </div>\r\n          </div>\r\n          <div className=\"min-h-[475px] flex flex-col justify-between bg-white p-6 rounded-2xl shadow-courseCardShadow flex-1 relative\">\r\n            <div>\r\n              <h3 className=\"text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-3 md:mb-4\">\r\n                World Book Of Records London\r\n              </h3>\r\n              <Image src=\"/londonAward.jpg\" alt=\"whyChooseOne\" width={456} height={239} className=\"w-full h-auto object-contain mb-4 rounded-md\" />\r\n              <p className=\"font-medium text-sm\">\r\n                The Skyline Aviation Club has been included in World Book Of Records London for training over 5000 trainees of Commercial Pilot, Flight Dispatcher, Cabin Crew & Airport Ground Staff in the Last {age} years.\r\n              </p>\r\n            </div>\r\n            <div className=\"text-center flex justify-center \">\r\n              <Link\r\n                href=\"/enquire\"\r\n                className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}\r\n              >\r\n                <span className=\"font-bold\">Enquire Now</span>\r\n                <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WhyChooseContainer;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AASA;AACA;AACA;AAHA;;;;;;;;;;AAKA,MAAM,qBAAqB;;IACzB,MAAM,MAAM,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,6IAAA,CAAA,UAAoB;;;;;0BACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoF;;;;;;0CAGlG,6LAAC;gCAAE,WAAU;0CAA4E;;;;;;;;;;;;;;;;;;0BAK7F,6LAAC;gBAAI,WAAU;0BACZ,sIAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC;wBACC,WAAW,CAAC,sDAAsD,EAAE,QAAQ,MAAM,IAAI,wBAAwB,IAC1G;;0CAGJ,6LAAC,gIAAA,CAAA,UAAK;gCAAC,KAAK,KAAK,KAAK;gCAAE,KAAI;gCAAa,OAAO;gCAAM,QAAQ;gCAAM,WAAU;;;;;;0CAC9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAE,WAAU;kDACV,KAAK,IAAI;;;;;;;;;;;;;uBART;;;;;;;;;;0BAcX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0F;;;;;;sDAGxG,6LAAC,oIAAA,CAAA,QAAK;;8DACJ,6LAAC,oIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,6LAAC,oIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqD;;;;;;0EAG1E,6LAAC,oIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqD;;;;;;;;;;;;;;;;;8DAK9E,6LAAC,oIAAA,CAAA,YAAS;8DACP,sIAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,KAAK,OAAO;;;;;;8EAEf,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAClB,KAAK,SAAS;;;;;;;2DALJ;;;;;;;;;;;;;;;;;;;;;;8CAYvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAC,kFAAkF,CAAC;;0DAE/F,6LAAC;gDAAK,WAAU;0DAAY;;;;;;0DAC5B,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAI5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAEC,6LAAC;4CAAG,WAAU;sDAA0F;;;;;;sDAGxG,6LAAC;4CAAE,WAAU;;gDAAsB;gDAOI;gDAAI;;;;;;;;;;;;;8CAO7C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAC,kFAAkF,CAAC;;0DAE/F,6LAAC;gDAAK,WAAU;0DAAY;;;;;;0DAC5B,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAI5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA0F;;;;;;sDAGxG,6LAAC,gIAAA,CAAA,UAAK;4CAAC,KAAI;4CAAmB,KAAI;4CAAe,OAAO;4CAAK,QAAQ;4CAAK,WAAU;;;;;;sDACpF,6LAAC;4CAAE,WAAU;;gDAAsB;gDACkK;gDAAI;;;;;;;;;;;;;8CAG3M,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAW,CAAC,kFAAkF,CAAC;;0DAE/F,6LAAC;gDAAK,WAAU;0DAAY;;;;;;0DAC5B,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;GA5IM;;QACQ,0HAAA,CAAA,UAAO;;;KADf;uCA8IS"}}, {"offset": {"line": 3320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/hooks/useDebounce.js"], "sourcesContent": ["import { useRef, useEffect } from \"react\";\r\n\r\nexport default function useDebounce(callback, delay) {\r\n  const timeoutRef = useRef(null);\r\n\r\n  const debouncedCallback = (...args) => {\r\n    clearTimeout(timeoutRef.current);\r\n    timeoutRef.current = setTimeout(() => {\r\n      callback(...args);\r\n    }, delay);\r\n  };\r\n\r\n  useEffect(() => {\r\n    return () => clearTimeout(timeoutRef.current);\r\n  }, []);\r\n\r\n  return debouncedCallback;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEe,SAAS,YAAY,QAAQ,EAAE,KAAK;;IACjD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,MAAM,oBAAoB,CAAC,GAAG;QAC5B,aAAa,WAAW,OAAO;QAC/B,WAAW,OAAO,GAAG,WAAW;YAC9B,YAAY;QACd,GAAG;IACL;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;yCAAO,IAAM,aAAa,WAAW,OAAO;;QAC9C;gCAAG,EAAE;IAEL,OAAO;AACT;GAfwB"}}, {"offset": {"line": 3354, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3360, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/select.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n    {...props}>\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\r\n    {...props}>\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}>\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\"p-1\", position === \"popper\" &&\r\n          \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\")}>\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props} />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props} />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,MAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YACR;0BACD,6LAAC,sKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,8JAAM,UAAU,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtE,6LAAC,sKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MALnB;AAQN,qBAAqB,WAAW,GAAG,sKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,8JAAM,UAAU,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxE,6LAAC,sKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MALrB;AAQN,uBAAuB,WAAW,GAChC,sKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC9F,6LAAC,sKAAgB,MAAM;kBACrB,cAAA,6LAAC,sKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BACT,6LAAC;;;;;8BACD,6LAAC,sKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO,aAAa,YAChC;8BACD;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;;AAEb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACtE,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BACT,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sKAAgB,aAAa;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6LAAC,sKAAgB,QAAQ;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,6LAAC,sKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAEb,gBAAgB,WAAW,GAAG,sKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 3569, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/EnquireForm.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useRef, useState } from \"react\";\r\nimport { coursesLinks } from \"@/constant/navbarLinks\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport emailjs from \"@emailjs/browser\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport useDebounce from \"@/hooks/useDebounce\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\n\r\nconst EnquireForm = () => {\r\n  const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;\r\n  const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;\r\n  const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;\r\n\r\n  const form = useRef();\r\n  const { toast } = useToast();\r\n\r\n  const [formData, setFormData] = useState({\r\n    fullName: \"\",\r\n    course: \"\",\r\n    contactNumber: \"\",\r\n    city: \"\",\r\n    email: \"\",\r\n    helpText: \"\",\r\n  });\r\n\r\n  const [errors, setErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  const isFormFilled =\r\n    formData.fullName.trim() &&\r\n    formData.course.trim() &&\r\n    formData.contactNumber.trim() &&\r\n    formData.city.trim() &&\r\n    formData.email.trim();\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleValidation = () => {\r\n    const newErrors = {};\r\n\r\n    if (\r\n      !formData.fullName ||\r\n      formData.fullName.length > 50 ||\r\n      /[^a-zA-Z\\s]/.test(formData.fullName)\r\n    ) {\r\n      newErrors.fullName =\r\n        \"Full Name should be less than 50 characters and contain only letters.\";\r\n    }\r\n\r\n    if (\r\n      !formData.contactNumber ||\r\n      formData.contactNumber.length > 15 ||\r\n      /[^0-9]/.test(formData.contactNumber)\r\n    ) {\r\n      newErrors.contactNumber =\r\n        \"Contact Number should be less than 15 digits and contain only numbers.\";\r\n    }\r\n\r\n    if (\r\n      !formData.city ||\r\n      formData.city.length > 50 ||\r\n      /[^a-zA-Z\\s]/.test(formData.city)\r\n    ) {\r\n      newErrors.city =\r\n        \"City should be less than 50 characters and contain only letters.\";\r\n    }\r\n\r\n    if (!formData.email) {\r\n      newErrors.email = \"Email Address is required.\";\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      newErrors.email = \"Email Address is invalid.\";\r\n    }\r\n\r\n    if (!formData.course) {\r\n      newErrors.course = \"Please select a course.\";\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  // Debounced email sender\r\n  const sendDebouncedEmail = useDebounce((emailParams) => {\r\n    return emailjs.send(serviceId, templateId, emailParams, userId);\r\n  }, 1000);\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!isFormFilled) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Missing Fields!\",\r\n        description: \"Please fill out all required fields.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!handleValidation()) return;\r\n\r\n    const emailParams = {\r\n      full_name: formData.fullName,\r\n      course_name: formData.course,\r\n      contact_number: formData.contactNumber,\r\n      city: formData.city,\r\n      email_address: formData.email,\r\n      help_text: formData.helpText || \"Not provided\",\r\n    };\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n      await sendDebouncedEmail(emailParams);\r\n      toast({\r\n        variant: \"success\",\r\n        title: \"Success!\",\r\n        description: \"Your enquiry has been sent successfully!\",\r\n      });\r\n      setFormData({\r\n        fullName: \"\",\r\n        course: \"\",\r\n        contactNumber: \"\",\r\n        city: \"\",\r\n        email: \"\",\r\n        helpText: \"\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Error!\",\r\n        description: \"Failed to send your enquiry. Please try again later.\",\r\n      });\r\n      console.error(\"EmailJS Error:\", error.text);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full md:w-1/2 tablet:w-[85%] bg-white rounded-lg p-6 mb-8 relative\" style={{ boxShadow: \"0 0 10px 1px rgba(0,0,0,0.20)\" }}>\r\n      <form ref={form} className=\"space-y-7\" onSubmit={handleSubmit}>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <label className=\"block mb-1 font-medium text-formTextColor text-sm\">\r\n              Full Name<span className=\"text-formLabelSubColor\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"fullName\"\r\n              placeholder=\"Enter full name\"\r\n              value={formData.fullName}\r\n              onChange={handleChange}\r\n              className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg focus:outline-none ${\r\n                errors.fullName ? \"ring-2 ring-red-500\" : \"\"\r\n              }`}\r\n              maxLength=\"50\"\r\n              pattern=\"[a-zA-Z\\s]*\"\r\n              required\r\n            />\r\n            {errors.fullName && (\r\n              <p className=\"text-red-500 text-sm mt-1\">{errors.fullName}</p>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <label className=\"block mb-1 font-medium text-formTextColor text-sm\">\r\n              Select Course Interested In\r\n              <span className=\"text-formLabelSubColor\">*</span>\r\n            </label>\r\n            <Select\r\n              value={formData.course}\r\n              onValueChange={(value) =>\r\n                setFormData((prev) => ({ ...prev, course: value }))\r\n              }>\r\n              <SelectTrigger\r\n                className={`w-full p-[1.4rem] bg-formBG text-[15px] text-black rounded-lg ${\r\n                  errors.course ? \"ring-2 ring-red-500\" : \"\"\r\n                }`}>\r\n                <SelectValue placeholder=\"Select a Course\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {coursesLinks.map((course, index) => (\r\n                  <SelectItem key={index} value={course.text}>\r\n                    {course.text}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n            {errors.course && (\r\n              <p className=\"text-red-500 text-sm mt-1\">{errors.course}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          <div>\r\n            <label className=\"block mb-1 font-medium text-formTextColor text-sm\">\r\n              Contact Number<span className=\"text-formLabelSubColor\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"contactNumber\"\r\n              value={formData.contactNumber}\r\n              onChange={handleChange}\r\n              maxLength=\"15\"\r\n              pattern=\"[0-9]*\"\r\n              required\r\n              placeholder=\"Enter contact number\"\r\n              className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${\r\n                errors.contactNumber ? \"ring-2 ring-red-500\" : \"\"\r\n              }`}\r\n            />\r\n            {errors.contactNumber && (\r\n              <p className=\"text-red-500 text-sm mt-1\">\r\n                {errors.contactNumber}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div>\r\n            <label className=\"block mb-1 font-medium text-formTextColor text-sm\">\r\n              City<span className=\"text-formLabelSubColor\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              name=\"city\"\r\n              value={formData.city}\r\n              onChange={handleChange}\r\n              maxLength=\"50\"\r\n              pattern=\"[a-zA-Z\\s]*\"\r\n              required\r\n              placeholder=\"Enter city\"\r\n              className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${\r\n                errors.city ? \"ring-2 ring-red-500\" : \"\"\r\n              }`}\r\n            />\r\n            {errors.city && (\r\n              <p className=\"text-red-500 text-sm mt-1\">{errors.city}</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div>\r\n          <label className=\"block mb-1 font-medium text-formTextColor text-sm\">\r\n            Email Address<span className=\"text-formLabelSubColor\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"email\"\r\n            name=\"email\"\r\n            value={formData.email}\r\n            onChange={handleChange}\r\n            required\r\n            placeholder=\"Enter email\"\r\n            className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${\r\n              errors.email ? \"ring-2 ring-red-500\" : \"\"\r\n            }`}\r\n          />\r\n          {errors.email && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{errors.email}</p>\r\n          )}\r\n        </div>\r\n        <div>\r\n          <label className=\"block mb-1 font-medium text-formTextColor text-sm\">\r\n            Tell Us How We Can Help\r\n          </label>\r\n          <textarea\r\n            name=\"helpText\"\r\n            value={formData.helpText}\r\n            onChange={handleChange}\r\n            placeholder=\"Optional message\"\r\n            rows=\"3\"\r\n            className=\"w-full p-3 bg-formBG text-[15px] text-black rounded-lg\"\r\n          />\r\n        </div>\r\n        <div className=\"flex justify-center\">\r\n          <button\r\n            type=\"submit\"\r\n            className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5`}\r\n          >\r\n            <span className=\"font-bold\">Send Enquiry</span>\r\n            <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n          </button>\r\n        </div>\r\n      </form>\r\n\r\n      {/* Spinner Overlay */}\r\n      {isSubmitting && (\r\n        <div className=\"fixed inset-0 bg-[#0000004f] bg-opacity-40 z-50 flex justify-center items-center\">\r\n          <div className=\"w-12 h-12 border-4 border-[#2821ff] border-t-transparent rounded-full animate-spin\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EnquireForm;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AACA;AACA;AASoB;AAZpB;AADA;;;AAHA;;;;;;;;AAeA,MAAM,cAAc;;IAClB,MAAM;IACN,MAAM;IACN,MAAM;IAEN,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAClB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,QAAQ;QACR,eAAe;QACf,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eACJ,SAAS,QAAQ,CAAC,IAAI,MACtB,SAAS,MAAM,CAAC,IAAI,MACpB,SAAS,aAAa,CAAC,IAAI,MAC3B,SAAS,IAAI,CAAC,IAAI,MAClB,SAAS,KAAK,CAAC,IAAI;IAErB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACnD;IAEA,MAAM,mBAAmB;QACvB,MAAM,YAAY,CAAC;QAEnB,IACE,CAAC,SAAS,QAAQ,IAClB,SAAS,QAAQ,CAAC,MAAM,GAAG,MAC3B,cAAc,IAAI,CAAC,SAAS,QAAQ,GACpC;YACA,UAAU,QAAQ,GAChB;QACJ;QAEA,IACE,CAAC,SAAS,aAAa,IACvB,SAAS,aAAa,CAAC,MAAM,GAAG,MAChC,SAAS,IAAI,CAAC,SAAS,aAAa,GACpC;YACA,UAAU,aAAa,GACrB;QACJ;QAEA,IACE,CAAC,SAAS,IAAI,IACd,SAAS,IAAI,CAAC,MAAM,GAAG,MACvB,cAAc,IAAI,CAAC,SAAS,IAAI,GAChC;YACA,UAAU,IAAI,GACZ;QACJ;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,UAAU,MAAM,GAAG;QACrB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,yBAAyB;IACzB,MAAM,qBAAqB,CAAA,GAAA,8HAAA,CAAA,UAAW,AAAD;uDAAE,CAAC;YACtC,OAAO,sKAAA,CAAA,UAAO,CAAC,IAAI,CAAC,WAAW,YAAY,aAAa;QAC1D;sDAAG;IAEH,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc;YACjB,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,IAAI,CAAC,oBAAoB;QAEzB,MAAM,cAAc;YAClB,WAAW,SAAS,QAAQ;YAC5B,aAAa,SAAS,MAAM;YAC5B,gBAAgB,SAAS,aAAa;YACtC,MAAM,SAAS,IAAI;YACnB,eAAe,SAAS,KAAK;YAC7B,WAAW,SAAS,QAAQ,IAAI;QAClC;QAEA,IAAI;YACF,gBAAgB;YAChB,MAAM,mBAAmB;YACzB,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,eAAe;gBACf,MAAM;gBACN,OAAO;gBACP,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA,QAAQ,KAAK,CAAC,kBAAkB,MAAM,IAAI;QAC5C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAuE,OAAO;YAAE,WAAW;QAAgC;;0BACxI,6LAAC;gBAAK,KAAK;gBAAM,WAAU;gBAAY,UAAU;;kCAC/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAoD;0DAC1D,6LAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAEpD,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,WAAW,CAAC,0EAA0E,EACpF,OAAO,QAAQ,GAAG,wBAAwB,IAC1C;wCACF,WAAU;wCACV,SAAQ;wCACR,QAAQ;;;;;;oCAET,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDAA6B,OAAO,QAAQ;;;;;;;;;;;;0CAG7D,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAoD;0DAEnE,6LAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,MAAM;wCACtB,eAAe,CAAC,QACd,YAAY,CAAC,OAAS,CAAC;oDAAE,GAAG,IAAI;oDAAE,QAAQ;gDAAM,CAAC;;0DAEnD,6LAAC,qIAAA,CAAA,gBAAa;gDACZ,WAAW,CAAC,8DAA8D,EACxE,OAAO,MAAM,GAAG,wBAAwB,IACxC;0DACF,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,kIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC,qIAAA,CAAA,aAAU;wDAAa,OAAO,OAAO,IAAI;kEACvC,OAAO,IAAI;uDADG;;;;;;;;;;;;;;;;oCAMtB,OAAO,MAAM,kBACZ,6LAAC;wCAAE,WAAU;kDAA6B,OAAO,MAAM;;;;;;;;;;;;;;;;;;kCAI7D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAoD;0DACrD,6LAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAEzD,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,aAAa;wCAC7B,UAAU;wCACV,WAAU;wCACV,SAAQ;wCACR,QAAQ;wCACR,aAAY;wCACZ,WAAW,CAAC,uDAAuD,EACjE,OAAO,aAAa,GAAG,wBAAwB,IAC/C;;;;;;oCAEH,OAAO,aAAa,kBACnB,6LAAC;wCAAE,WAAU;kDACV,OAAO,aAAa;;;;;;;;;;;;0CAI3B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAAoD;0DAC/D,6LAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;kDAE/C,6LAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,IAAI;wCACpB,UAAU;wCACV,WAAU;wCACV,SAAQ;wCACR,QAAQ;wCACR,aAAY;wCACZ,WAAW,CAAC,uDAAuD,EACjE,OAAO,IAAI,GAAG,wBAAwB,IACtC;;;;;;oCAEH,OAAO,IAAI,kBACV,6LAAC;wCAAE,WAAU;kDAA6B,OAAO,IAAI;;;;;;;;;;;;;;;;;;kCAI3D,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAAoD;kDACtD,6LAAC;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;0CAExD,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,QAAQ;gCACR,aAAY;gCACZ,WAAW,CAAC,uDAAuD,EACjE,OAAO,KAAK,GAAG,wBAAwB,IACvC;;;;;;4BAEH,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAG1D,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAAoD;;;;;;0CAGrE,6LAAC;gCACC,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,aAAY;gCACZ,MAAK;gCACL,WAAU;;;;;;;;;;;;kCAGd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAW,CAAC,2EAA2E,CAAC;;8CAExF,6LAAC;oCAAK,WAAU;8CAAY;;;;;;8CAC5B,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM3B,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;GA3RM;;QAMc,+HAAA,CAAA,WAAQ;QAuEC,8HAAA,CAAA,UAAW;;;KA7ElC;uCA6RS"}}, {"offset": {"line": 4095, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/eventsData.jsx"], "sourcesContent": ["const eventsData = [\r\n  {\r\n    id: 1,\r\n    title: \"Event Name | Venue | 27th Nov 2024\",\r\n    content:\r\n      \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis placerat dui sed fringilla cursus. Donec vitae lacus non lorem aliquam porttitor. Pellentesque arcu libero, lacinia sed odio vitae, ullamcorper ornare odio. Integer nec tempus arcu. Aenean ultricies eros sed ex pellentesque, vel vestibulum ante bibendum. Curabitur maximus lacus neque, ut tempor leo tristique malesuada. Mauris metus dui, eleifend eget vehicula sed, auctor ut nunc. Sed elementum elit dolor, suscipit semper metus malesuada ac. Morbi a lorem justo. Maecenas in orci quis mauris dapibus pulvinar\",\r\n  },\r\n];\r\n\r\nconst eventsPageData = [\r\n  {\r\n    title: \"May 2015: Jet Airways Entrance Exam\",\r\n    image: \"/assets/events/1.jpg\",\r\n    desc: \"Jet Airways holds regular Job placement exams for candidate providing direct entry into one of India's oldest airlines.\",\r\n  },\r\n  {\r\n    title: \"August 2015: English Language proficiency Exam\",\r\n    image: \"/assets/events/2.jpg\",\r\n    desc: \"Chief Pilot - Helicopters at Essar, DGCA Examiner Sirorsky S76C++, Bell 430 English Language Proficiency Examiner Capt J.K Rao conducted examination for students and gave them insightful and motivational talks about Aviation Career and life.\",\r\n  },\r\n  {\r\n    title: \"Latest Batch of 2015\",\r\n    image: \"/assets/events/3.jpg\",\r\n    desc: \"Students standing for their batch photo after long hard months of rigorous training.\",\r\n  },\r\n  {\r\n    title: \"June 2015: Fly in the Sky with Capt A.D Manek\",\r\n    image: \"/assets/events/4.webp\",\r\n    desc: \"Special programme organised by YUMA Telivision Vadodara, Gujarat at Sir Sayajirao Nagar Gruh, Akota Vadodara on 27th June 2015 where Capt Manek Addressed over 1000 students on Mind Power and Careers in airlines.\",\r\n  },\r\n  {\r\n    title: \"March 2015: Goverment Scholarship Examinations\",\r\n    image: \"/assets/events/5.jpg\",\r\n    desc: \"National Commision for Schedule Caste, Govt Of India Conducted written examinations for award of scholarships for CPL Course at The Skyline Aviation Club on 27th March 2015.\",\r\n  },\r\n  {\r\n    title: \"October 2013: HAM Radio Training at Mantralaya Govt. of Maharashtra\",\r\n    image: \"/assets/events/6.jpg\",\r\n    desc: \"Capt A.D Manek taking training cession for Disaster Management and HAM radio operations at Mantralaya Govt of Maharashtra, Mumbai October 2013.\",\r\n  },\r\n  {\r\n    title: \"March 2001: Jaipur AirShow\",\r\n    image: \"/assets/events/7.jpg\",\r\n    desc: \"Capt A.D Manek discussing Aviation with then Boeing India CEO Dr.Dinesh Keshkar at Jiapur AirShow for International Womans Day Celebration organized by The Skyline Aviation Club at Banshatali, Rajasthan on 8th Match 2001.\",\r\n  },\r\n  {\r\n    title: \"March 2001: National Geographic AirShow\",\r\n    image: \"/assets/events/8.jpg\",\r\n    desc: \"Adventure One a National Geopgraphic TV Channel sponsored Event for Aviation featuring Capt A.D Manek and his Team of Aero Modeling at Jamboree Grounds, Aarey Milk Colony, Goregaon(E).\",\r\n  },\r\n];\r\nexport { eventsData, eventsPageData };\r\n"], "names": [], "mappings": ";;;;AAAA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,OAAO;QACP,SACE;IACJ;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM;IACR;CACD"}}, {"offset": {"line": 4158, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4164, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/common/EventCard.jsx"], "sourcesContent": ["\"use client\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport Image from \"next/image\";\r\nimport { useState } from \"react\";\r\n\r\nconst EventCard = ({ cardData, isLandingPage }) => {\r\n  const [showMore, setShowMore] = useState(null);\r\n  const maxLength = 70;\r\n\r\n  const toggleShowMore = (index) => {\r\n    setShowMore((prevIndex) => (prevIndex === index ? null : index));\r\n  };\r\n  return (\r\n    <>\r\n      {/* <div className=\"h-full bg-white rounded-md shadow-md overflow-hidden flex flex-col\"></div> */}\r\n      {cardData.map((item, index) => (\r\n        <Card key={index} className=\"w-full h-full flex flex-col bg-descLightBlack\">\r\n          <CardContent className=\"flex flex-col p-0 rounded-md h-full\">\r\n            {/* Image */}\r\n            <Image\r\n              src={item.image}\r\n              width={1900}\r\n              height={1600}\r\n              alt=\"avatar\"\r\n              className=\"w-full h-[300px] sm:h-[300px] md:h-[220px] rounded-t-xl object-cover\"\r\n              quality={100}\r\n            />\r\n\r\n            {/* Description + Button */}\r\n            <div className=\"bg-descLightBlack p-3 lg:p-6 rounded-b-lg flex flex-col flex-grow justify-between\">\r\n              <div>\r\n                <h3 className=\"text-sm md:text-base font-bold mb-1 text-white\">{item.title}</h3>\r\n                <p className=\"text-xs md:text-sm font-medium text-white break-words\">\r\n                  {showMore === index ? item.desc : `${item.desc.slice(0, maxLength)}...`}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"mt-4\">\r\n                <button\r\n                  onClick={() => toggleShowMore(index)}\r\n                  className=\"text-white text-xs md:text-sm px-2 py-1 bg-blue-900 rounded-md hover:bg-blue-800 transition-colors\"\r\n                >\r\n                  {showMore === index ? \"Show Less\" : \"Show More\"}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      ))}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default EventCard;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKA,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE;;IAC5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY;IAElB,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAC,YAAe,cAAc,QAAQ,OAAO;IAC3D;IACA,qBACE;kBAEG,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,mIAAA,CAAA,OAAI;gBAAa,WAAU;0BAC1B,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,KAAK;4BACf,OAAO;4BACP,QAAQ;4BA<PERSON>,KAAI;4BAC<PERSON>,WAA<PERSON>;4BAC<PERSON>,SAAS;;;;;;sCAIX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAkD,KAAK,KAAK;;;;;;sDAC1E,6LAAC;4CAAE,WAAU;sDACV,aAAa,QAAQ,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;;;;;;;8CAI3E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAET,aAAa,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;;eA1BnC;;;;;;AAmCnB;GA9CM;KAAA;uCAgDS"}}, {"offset": {"line": 4272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/SingleEventCard.jsx"], "sourcesContent": ["\"use client\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport Image from \"next/image\";\r\nimport { useState } from \"react\";\r\n\r\nconst SingleEventCard = ({ event, isLandingPage }) => {\r\n    const [showMore, setShowMore] = useState(false);\r\n    const maxLength = 70;\r\n\r\n    return (\r\n        <Card className=\"w-full h-full flex flex-col bg-descLightBlack\">\r\n            <CardContent className=\"flex flex-col p-0 rounded-md h-full\">\r\n                {/* Image */}\r\n                <Image\r\n                    src={event.image}\r\n                    width={1900}\r\n                    height={1600}\r\n                    alt=\"event\"\r\n                    className=\"w-full h-[300px] sm:h-[300px] md:h-[220px] rounded-t-xl object-cover\"\r\n                    quality={100}\r\n                />\r\n\r\n                {/* Description + Button */}\r\n                <div className=\"bg-descLightBlack p-3 lg:p-6 rounded-b-lg flex flex-col flex-grow justify-between\">\r\n                    <div>\r\n                        <h3 className=\"text-sm md:text-base font-bold mb-1 text-white\">{event.title}</h3>\r\n                        <p className=\"text-xs md:text-sm font-medium text-white break-words\">\r\n                            {showMore ? event.desc : `${event.desc.slice(0, maxLength)}...`}\r\n                        </p>\r\n                    </div>\r\n\r\n                    <div className=\"mt-4\">\r\n                        <button\r\n                            onClick={() => setShowMore(!showMore)}\r\n                            className=\"text-white text-xs md:text-sm px-2 py-1 bg-blue-900 rounded-md hover:bg-blue-800 transition-colors\"\r\n                        >\r\n                            {showMore ? \"Show Less\" : \"Show More\"}\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </CardContent>\r\n        </Card>\r\n    );\r\n};\r\n\r\nexport default SingleEventCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKA,MAAM,kBAAkB,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE;;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY;IAElB,qBACI,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BAEnB,6LAAC,gIAAA,CAAA,UAAK;oBACF,KAAK,MAAM,KAAK;oBAChB,OAAO;oBACP,QAAQ;oBACR,KAAI;oBACJ,WAAU;oBACV,SAAS;;;;;;8BAIb,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;;8CACG,6LAAC;oCAAG,WAAU;8CAAkD,MAAM,KAAK;;;;;;8CAC3E,6LAAC;oCAAE,WAAU;8CACR,WAAW,MAAM,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;;;;;;;sCAIvE,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCACG,SAAS,IAAM,YAAY,CAAC;gCAC5B,WAAU;0CAET,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD;GAtCM;KAAA;uCAwCS"}}, {"offset": {"line": 4381, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/app/page.jsx"], "sourcesContent": ["\"use client\";\r\nimport FAQContainer from \"@/components/FAQContainer\";\r\nimport OurWorldContainer from \"@/components/OurWorldContainer\";\r\nimport CoursesOffer from \"@/components/CoursesOffer\";\r\nimport AlumniCarousel from \"@/components/AlumniCarousel\";\r\nimport WhyChooseContainer from \"@/components/WhyChooseContainer\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport EnquireForm from \"@/components/EnquireForm\";\r\nimport { eventsPageData } from \"@/constant/eventsData\";\r\nimport EventCard from \"@/components/common/EventCard\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Carousel, CarouselContent, CarouselItem } from \"@/components/ui/carousel\";\r\nimport SingleEventCard from \"@/components/SingleEventCard\";\r\n\r\nexport default function Home() {\r\n  const [events, setEvents] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [selectedIndex, setSelectedIndex] = useState(0);\r\n  const [api, setApi] = useState();\r\n\r\n  useEffect(() => {\r\n    if (!api) {\r\n      return;\r\n    }\r\n\r\n    setSelectedIndex(api.selectedScrollSnap() + 1);\r\n\r\n    api.on(\"select\", () => {\r\n      setSelectedIndex(api.selectedScrollSnap() + 1);\r\n    });\r\n  }, [api]);\r\n\r\n  const scrollTo = (index) => {\r\n    if (api) {\r\n      api.scrollTo(index);\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    const fetchEvents = async () => {\r\n      try {\r\n        const response = await fetch(process.env.NEXT_PUBLIC_URL);\r\n        const data = await response.json();\r\n        // console.log(data.data[0].image);\r\n\r\n\r\n        // Transform the API data to match the expected format\r\n        const formattedEvents = data.data.map((event) => ({\r\n          title: event.title,\r\n          desc: event.description,\r\n          image: process.env.NEXT_PUBLIC_STRAPI_URL + event.image?.url,\r\n        }\r\n        ));\r\n\r\n        // console.log(formattedEvents[0]?.image)\r\n        // console.log(\"yooo\", formattedEvents);\r\n        // console.log(formattedEvents);\r\n\r\n\r\n        setEvents(formattedEvents);\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch events:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchEvents();\r\n    // setEvents(eventsPageData);\r\n    // setLoading(false);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {/* <div className=\"relative bg-landingBanner bg-cover bg-[-35rem] md:bg-center h-[750px] bg-no-repeat\"> */}\r\n      <div className=\"relative bg-landingBanner bg-cover bg-[90%_center] md:bg-[60%_center] h-[750px] bg-no-repeat\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent\"></div>\r\n        <div className=\"absolute bottom-24 left-0 right-0 mx-4 md:mx-12 lg:mx-32 pb-4 md:pb-6\">\r\n          <h1 className=\"w-[70%] md:w-[80%] text-[2.3rem] leading-[2.7rem] font-bold md:font-extrabold lg:text-5xl text-white mb-1 md:mb-3 lg:mb-4 lg:leading-tight\"   style={{ textShadow: \"2px 2px 4px black\" }}>\r\n            Reach New Heights with\r\n            The Skyline Aviation Club\r\n          </h1>\r\n          <h3 className=\"font-bold text-lg md:text-3xl text-white mb-1 md:mb-4\"   style={{ textShadow: \"2px 2px 4px black\" }}>\r\n            Your Pilot Journey Begins Here !\r\n          </h3>\r\n          <p className=\"text-[13px] font-medium md:font-bold md:text-xl text-white mb-4\"   style={{ textShadow: \"2px 2px 4px black\" }}>\r\n            Take the Pilot&apos;s Seat with Confidence - Start Your <br />\r\n            Training Now !\r\n          </p>\r\n          <div className=\"flex\">\r\n            <Link\r\n              href=\"/enquire\"\r\n              className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}\r\n            >\r\n              <span className=\"font-bold\">Enquire Now</span>\r\n              <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n            </Link>\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n      <WhyChooseContainer />\r\n      <CoursesOffer />\r\n      <div className=\"p-3 md:bg-footerBG lg:p-8 md:bg-alumniCurve bg-cover bg-center pb-7\">\r\n        <h3 className=\"text-center mb-1.5 lg:mb-2.5 font-bold text-xl md:text-2xl lg:text-4xl text-textBluePrimary\">\r\n          Recent Events\r\n        </h3>\r\n        <p className=\"text-center text-sm lg:text-base text-customGrey100 font-medium mb-3 lg:mb-4\">\r\n          Discover and explore upcoming events, workshops, and gatherings\r\n          tailored to your interests.\r\n        </p>\r\n\r\n        {/* Mobile Carousel */}\r\n        <div className=\"block sm:hidden\">\r\n          <Carousel\r\n            setApi={setApi}\r\n            className=\"w-full max-w-sm mx-auto relative\"\r\n            opts={{\r\n              loop: true,\r\n            }}\r\n          >\r\n            <CarouselContent>\r\n              {events.slice(0, 3).map((event, index) => (\r\n                <CarouselItem key={index} className=\"flex justify-center\">\r\n                  <SingleEventCard event={event} />\r\n                </CarouselItem>\r\n              ))}\r\n            </CarouselContent>\r\n            <div className=\"flex justify-center items-center mt-4 space-x-2\">\r\n              {events.slice(0, 3).map((_, index) => (\r\n                <button\r\n                  key={index}\r\n                  onClick={() => scrollTo(index)}\r\n                  className={`${selectedIndex === index + 1\r\n                    ? \"h-3 w-8 rounded-full bg-textBluePrimary\"\r\n                    : \"h-3 w-3 rounded-full bg-carouselDotsBG\"\r\n                    } cursor-pointer transition-all duration-300 ease-in-out`}\r\n                />\r\n              ))}\r\n            </div>\r\n          </Carousel>\r\n        </div>\r\n\r\n\r\n        {/* Tablet and Desktop Grid */}\r\n        <div className=\"hidden sm:grid mx-auto max-w-6xl w-full grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-fr\">\r\n          <EventCard cardData={events.slice(0, 3)} isLandingPage={loading} />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center\">\r\n          <Link\r\n            href=\"/events\"\r\n            className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10`}\r\n          >\r\n            <span className=\"font-bold\">View All Events</span>\r\n            <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n          </Link>\r\n        </div>\r\n        {/* \r\n        <Link\r\n          href=\"/events\"\r\n          className=\"mt-7 lg:mt-3 mx-auto text-white text-sm md:text-base py-2 pr-4 pl-5 md:pl-7 w-[180px] md:w-[220px] justify-between font-bold rounded-full border border-transparent bg-buttonBGPrimary flex items-center gap-1 \">\r\n          View All Events\r\n          <ArrowRight className=\"w-7 h-7 md:w-9 md:h-9 p-1 bg-white rounded-full text-arrowIconColor\" />\r\n        </Link> */}\r\n      </div>\r\n\r\n      <AlumniCarousel />\r\n      <OurWorldContainer />\r\n      <FAQContainer />\r\n      <div className=\"md:px-8 lg:px-12 z-0 relative\">\r\n        <div className=\"z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-10 lg:bottom-[0px] left-0 right-0 w-full h-[500px]\"></div>\r\n        <div className=\"px-4 py-6 md:p-8 lg:flex gap-8\">\r\n          <div className=\"flex flex-col mb-4 lg:basis-6/12\">\r\n            <h3 className=\"text-center text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl mb-2 md:mb-8\">\r\n              Take Off With The Skyline Aviation Club\r\n            </h3>\r\n            <p className=\"text-left md:text-center lg:text-left font-medium text-formPara text-xs md:text-sm lg:text-base\">\r\n              Ready to embark on your aviation career? Whether you are\r\n              curious about our courses, admission process, or career\r\n              opportunities, we&apos;re here to guide you every step of the way.\r\n              Fill out the form below, and our team will get in touch with you\r\n              shortly. Let&apos;s make your aviation aspirations a reality !\r\n            </p>\r\n          </div>\r\n          <EnquireForm />\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAgCqC;AAvCrC;;;AANA;;;;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,KAAK;gBACR;YACF;YAEA,iBAAiB,IAAI,kBAAkB,KAAK;YAE5C,IAAI,EAAE,CAAC;kCAAU;oBACf,iBAAiB,IAAI,kBAAkB,KAAK;gBAC9C;;QACF;yBAAG;QAAC;KAAI;IAER,MAAM,WAAW,CAAC;QAChB,IAAI,KAAK;YACP,IAAI,QAAQ,CAAC;QACf;IACF;IAKA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;8CAAc;oBAClB,IAAI;wBACF,MAAM,WAAW,MAAM;wBACvB,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,mCAAmC;wBAGnC,sDAAsD;wBACtD,MAAM,kBAAkB,KAAK,IAAI,CAAC,GAAG;0EAAC,CAAC,QAAU,CAAC;oCAChD,OAAO,MAAM,KAAK;oCAClB,MAAM,MAAM,WAAW;oCACvB,OAAO,2EAAqC,MAAM,KAAK,EAAE;gCAC3D,CACA;;wBAEA,yCAAyC;wBACzC,wCAAwC;wBACxC,gCAAgC;wBAGhC,UAAU;oBACZ,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACA,6BAA6B;QAC7B,qBAAqB;QACvB;yBAAG,EAAE;IAEL,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;gCAA+I,OAAO;oCAAE,YAAY;gCAAoB;0CAAG;;;;;;0CAIzM,6LAAC;gCAAG,WAAU;gCAA0D,OAAO;oCAAE,YAAY;gCAAoB;0CAAG;;;;;;0CAGpH,6LAAC;gCAAE,WAAU;gCAAoE,OAAO;oCAAE,YAAY;gCAAoB;;oCAAG;kDACnE,6LAAC;;;;;oCAAK;;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAC,kFAAkF,CAAC;;sDAE/F,6LAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC,2IAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC,qIAAA,CAAA,UAAY;;;;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA8F;;;;;;kCAG5G,6LAAC;wBAAE,WAAU;kCAA+E;;;;;;kCAM5F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4BACP,QAAQ;4BACR,WAAU;4BACV,MAAM;gCACJ,MAAM;4BACR;;8CAEA,6LAAC,uIAAA,CAAA,kBAAe;8CACb,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC,uIAAA,CAAA,eAAY;4CAAa,WAAU;sDAClC,cAAA,6LAAC,wIAAA,CAAA,UAAe;gDAAC,OAAO;;;;;;2CADP;;;;;;;;;;8CAKvB,6LAAC;oCAAI,WAAU;8CACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC1B,6LAAC;4CAEC,SAAS,IAAM,SAAS;4CACxB,WAAW,GAAG,kBAAkB,QAAQ,IACpC,4CACA,yCACD,uDAAuD,CAAC;2CALtD;;;;;;;;;;;;;;;;;;;;;kCAcf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;4BAAC,UAAU,OAAO,KAAK,CAAC,GAAG;4BAAI,eAAe;;;;;;;;;;;kCAG1D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,iFAAiF,CAAC;;8CAE9F,6LAAC;oCAAK,WAAU;8CAAY;;;;;;8CAC5B,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAY5B,6LAAC,uIAAA,CAAA,UAAc;;;;;0BACf,6LAAC,0IAAA,CAAA,UAAiB;;;;;0BAClB,6LAAC,qIAAA,CAAA,UAAY;;;;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0F;;;;;;kDAGxG,6LAAC;wCAAE,WAAU;kDAAkG;;;;;;;;;;;;0CAQjH,6LAAC,oIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;AAKtB;GAnLwB;KAAA"}}, {"offset": {"line": 4796, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}