"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[310],{767:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6046:(e,t,n)=>{var r=n(6658);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},6160:(e,t,n)=>{n.d(t,{bm:()=>eP,UC:()=>eR,VY:()=>eO,hJ:()=>eD,ZL:()=>eC,bL:()=>ew,hE:()=>ex,l9:()=>eN});var r,o=n(2115),i=n.t(o,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function u(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return o.useCallback(function(...e){return t=>{let n=!1,r=e.map(e=>{let r=u(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():u(e[t],null)}}}}(...e),e)}var s=n(5155),d=globalThis?.document?o.useLayoutEffect:()=>{},c=i[" useId ".trim().toString()]||(()=>void 0),f=0;function p(e){let[t,n]=o.useState(c());return d(()=>{e||n(e=>e??String(f++))},[e]),e||(t?`radix-${t}`:"")}var v=i[" useInsertionEffect ".trim().toString()]||d,m=(Symbol("RADIX:SYNC_STATE"),n(7650));function y(e){let t=function(e){let t=o.forwardRef((e,t)=>{var n,r;let i,l;let{children:u,...s}=e,d=a(o.isValidElement(u)?(i=null===(n=Object.getOwnPropertyDescriptor(u.props,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in i&&i.isReactWarning?u.ref:(i=null===(r=Object.getOwnPropertyDescriptor(u,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in i&&i.isReactWarning?u.props.ref:u.props.ref||u.ref:void 0,t);if(o.isValidElement(u)){let e=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=i(...t);return o(...t),r}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(s,u.props);return u.type!==o.Fragment&&(e.ref=d),o.cloneElement(u,e)}return o.Children.count(u)>1?o.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,l=o.Children.toArray(r),u=l.find(g);if(u){let e=u.props.children,r=l.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,s.jsx)(t,{...i,ref:n,children:r})});return n.displayName="".concat(e,".Slot"),n}var E=Symbol("radix.slottable");function g(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===E}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=y(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e,l=o?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(l,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function b(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var w="dismissableLayer.update",N=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),C=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:v,...m}=e,y=o.useContext(N),[E,g]=o.useState(null),C=null!==(i=null==E?void 0:E.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,x]=o.useState({}),O=a(t,e=>g(e)),P=Array.from(y.layers),[T]=[...y.layersWithOutsidePointerEventsDisabled].slice(-1),I=P.indexOf(T),S=E?P.indexOf(E):-1,j=y.layersWithOutsidePointerEventsDisabled.size>0,L=S>=I,_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=b(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){R("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...y.branches].some(e=>e.contains(t));!L||n||(null==c||c(e),null==p||p(e),e.defaultPrevented||null==v||v())},C),A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=b(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&R("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...y.branches].some(e=>e.contains(t))||(null==f||f(e),null==p||p(e),e.defaultPrevented||null==v||v())},C);return!function(e,t=globalThis?.document){let n=b(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{S!==y.layers.size-1||(null==d||d(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},C),o.useEffect(()=>{if(E)return u&&(0===y.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),y.layersWithOutsidePointerEventsDisabled.add(E)),y.layers.add(E),D(),()=>{u&&1===y.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[E,C,u,y]),o.useEffect(()=>()=>{E&&(y.layers.delete(E),y.layersWithOutsidePointerEventsDisabled.delete(E),D())},[E,y]),o.useEffect(()=>{let e=()=>x({});return document.addEventListener(w,e),()=>document.removeEventListener(w,e)},[]),(0,s.jsx)(h.div,{...m,ref:O,style:{pointerEvents:j?L?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,A.onFocusCapture),onBlurCapture:l(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,_.onPointerDownCapture)})});function D(){let e=new CustomEvent(w);document.dispatchEvent(e)}function R(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&i.addEventListener(e,t,{once:!0}),o)?i&&m.flushSync(()=>i.dispatchEvent(l)):i.dispatchEvent(l)}C.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(N),r=o.useRef(null),i=a(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(h.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var x="focusScope.autoFocusOnMount",O="focusScope.autoFocusOnUnmount",P={bubbles:!1,cancelable:!0},T=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...u}=e,[d,c]=o.useState(null),f=b(i),p=b(l),v=o.useRef(null),m=a(t,e=>c(e)),y=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(y.paused||!d)return;let t=e.target;d.contains(t)?v.current=t:j(v.current,{select:!0})},t=function(e){if(y.paused||!d)return;let t=e.relatedTarget;null===t||d.contains(t)||j(v.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&j(d)});return d&&n.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,d,y.paused]),o.useEffect(()=>{if(d){L.add(y);let e=document.activeElement;if(!d.contains(e)){let t=new CustomEvent(x,P);d.addEventListener(x,f),d.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(j(r,{select:t}),document.activeElement!==n)return}(I(d).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&j(d))}return()=>{d.removeEventListener(x,f),setTimeout(()=>{let t=new CustomEvent(O,P);d.addEventListener(O,p),d.dispatchEvent(t),t.defaultPrevented||j(null!=e?e:document.body,{select:!0}),d.removeEventListener(O,p),L.remove(y)},0)}}},[d,f,p,y]);let E=o.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=I(e);return[S(t,e),S(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&j(i,{select:!0})):(e.preventDefault(),n&&j(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,s.jsx)(h.div,{tabIndex:-1,...u,ref:m,onKeyDown:E})});function I(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function S(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function j(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}T.displayName="FocusScope";var L=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=_(e,t)).unshift(t)},remove(t){var n;null===(n=(e=_(e,t))[0])||void 0===n||n.resume()}}}();function _(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var A=o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[u,a]=o.useState(!1);d(()=>a(!0),[]);let c=i||u&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return c?m.createPortal((0,s.jsx)(h.div,{...l,ref:t}),c):null});A.displayName="Portal";var F=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef(null),u=o.useRef(e),a=o.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=M(l.current);a.current="mounted"===s?e:"none"},[s]),d(()=>{let t=l.current,n=u.current;if(n!==e){let r=a.current,o=M(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),d(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=M(l.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!u.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(a.current=M(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=a(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function M(e){return(null==e?void 0:e.animationName)||"none"}F.displayName="Presence";var k=0;function W(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var U=n(4065),$=n(5587),K="Dialog",[B,V]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let u=t=>{let{scope:n,children:r,...u}=t,a=n?.[e]?.[l]||i,d=o.useMemo(()=>u,Object.values(u));return(0,s.jsx)(a.Provider,{value:d,children:r})};return u.displayName=t+"Provider",[u,function(n,u){let a=u?.[e]?.[l]||i,s=o.useContext(a);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(K),[q,z]=B(K),H=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:l,modal:u=!0}=e,a=o.useRef(null),d=o.useRef(null),[c,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return v(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),a=void 0!==e,s=a?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,r])}return[s,o.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[a,e,l,u])]}({prop:r,defaultProp:null!=i&&i,onChange:l,caller:K});return(0,s.jsx)(q,{scope:t,triggerRef:a,contentRef:d,contentId:p(),titleId:p(),descriptionId:p(),open:c,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};H.displayName=K;var Z="DialogTrigger",X=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=z(Z,n),i=a(t,o.triggerRef);return(0,s.jsx)(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":em(o.open),...r,ref:i,onClick:l(e.onClick,o.onOpenToggle)})});X.displayName=Z;var Y="DialogPortal",[J,G]=B(Y,{forceMount:void 0}),Q=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,l=z(Y,t);return(0,s.jsx)(J,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,s.jsx)(F,{present:n||l.open,children:(0,s.jsx)(A,{asChild:!0,container:i,children:e})}))})};Q.displayName=Y;var ee="DialogOverlay",et=o.forwardRef((e,t)=>{let n=G(ee,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=z(ee,e.__scopeDialog);return i.modal?(0,s.jsx)(F,{present:r||i.open,children:(0,s.jsx)(er,{...o,ref:t})}):null});et.displayName=ee;var en=y("DialogOverlay.RemoveScroll"),er=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=z(ee,n);return(0,s.jsx)(U.A,{as:en,allowPinchZoom:!0,shards:[o.contentRef],children:(0,s.jsx)(h.div,{"data-state":em(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eo="DialogContent",ei=o.forwardRef((e,t)=>{let n=G(eo,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=z(eo,e.__scopeDialog);return(0,s.jsx)(F,{present:r||i.open,children:i.modal?(0,s.jsx)(el,{...o,ref:t}):(0,s.jsx)(eu,{...o,ref:t})})});ei.displayName=eo;var el=o.forwardRef((e,t)=>{let n=z(eo,e.__scopeDialog),r=o.useRef(null),i=a(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,$.Eq)(e)},[]),(0,s.jsx)(ea,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),eu=o.forwardRef((e,t)=>{let n=z(eo,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,s.jsx)(ea,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,l;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let u=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),ea=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,d=z(eo,n),c=o.useRef(null),f=a(t,c);return o.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:W()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:W()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(T,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,s.jsx)(C,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":em(d.open),...u,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eh,{titleId:d.titleId}),(0,s.jsx)(eb,{contentRef:c,descriptionId:d.descriptionId})]})]})}),es="DialogTitle",ed=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=z(es,n);return(0,s.jsx)(h.h2,{id:o.titleId,...r,ref:t})});ed.displayName=es;var ec="DialogDescription",ef=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=z(ec,n);return(0,s.jsx)(h.p,{id:o.descriptionId,...r,ref:t})});ef.displayName=ec;var ep="DialogClose",ev=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=z(ep,n);return(0,s.jsx)(h.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>o.onOpenChange(!1))})});function em(e){return e?"open":"closed"}ev.displayName=ep;var ey="DialogTitleWarning",[eE,eg]=function(e,t){let n=o.createContext(t),r=e=>{let{children:t,...r}=e,i=o.useMemo(()=>r,Object.values(r));return(0,s.jsx)(n.Provider,{value:i,children:t})};return r.displayName=e+"Provider",[r,function(r){let i=o.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(ey,{contentName:eo,titleName:es,docsSlug:"dialog"}),eh=e=>{let{titleId:t}=e,n=eg(ey),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},eb=e=>{let{contentRef:t,descriptionId:n}=e,r=eg("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},ew=H,eN=X,eC=Q,eD=et,eR=ei,ex=ed,eO=ef,eP=ev}}]);