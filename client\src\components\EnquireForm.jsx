"use client";
import React, { useRef, useState } from "react";
import { coursesLinks } from "@/constant/navbarLinks";
import { ArrowRight } from "lucide-react";
import emailjs from "@emailjs/browser";
import { useToast } from "@/hooks/use-toast";
import useDebounce from "@/hooks/useDebounce";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const EnquireForm = () => {
  const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
  const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
  const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;

  const form = useRef();
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    fullName: "",
    course: "",
    contactNumber: "",
    city: "",
    email: "",
    helpText: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isFormFilled =
    formData.fullName.trim() &&
    formData.course.trim() &&
    formData.contactNumber.trim() &&
    formData.city.trim() &&
    formData.email.trim();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleValidation = () => {
    const newErrors = {};

    if (
      !formData.fullName ||
      formData.fullName.length > 50 ||
      /[^a-zA-Z\s]/.test(formData.fullName)
    ) {
      newErrors.fullName =
        "Full Name should be less than 50 characters and contain only letters.";
    }

    if (
      !formData.contactNumber ||
      formData.contactNumber.length > 15 ||
      /[^0-9]/.test(formData.contactNumber)
    ) {
      newErrors.contactNumber =
        "Contact Number should be less than 15 digits and contain only numbers.";
    }

    if (
      !formData.city ||
      formData.city.length > 50 ||
      /[^a-zA-Z\s]/.test(formData.city)
    ) {
      newErrors.city =
        "City should be less than 50 characters and contain only letters.";
    }

    if (!formData.email) {
      newErrors.email = "Email Address is required.";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email Address is invalid.";
    }

    if (!formData.course) {
      newErrors.course = "Please select a course.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Debounced email sender
  const sendDebouncedEmail = useDebounce((emailParams) => {
    return emailjs.send(serviceId, templateId, emailParams, userId);
  }, 1000);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!isFormFilled) {
      toast({
        variant: "destructive",
        title: "Missing Fields!",
        description: "Please fill out all required fields.",
      });
      return;
    }

    if (!handleValidation()) return;

    const emailParams = {
      full_name: formData.fullName,
      course_name: formData.course,
      contact_number: formData.contactNumber,
      city: formData.city,
      email_address: formData.email,
      help_text: formData.helpText || "Not provided",
    };

    try {
      setIsSubmitting(true);
      await sendDebouncedEmail(emailParams);
      toast({
        variant: "success",
        title: "Success!",
        description: "Your enquiry has been sent successfully!",
      });
      setFormData({
        fullName: "",
        course: "",
        contactNumber: "",
        city: "",
        email: "",
        helpText: "",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error!",
        description: "Failed to send your enquiry. Please try again later.",
      });
      console.error("EmailJS Error:", error.text);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full md:w-1/2 tablet:w-[85%] bg-white rounded-lg p-6 mb-8 relative" style={{ boxShadow: "0 0 10px 1px rgba(0,0,0,0.20)" }}>
      <form ref={form} className="space-y-7" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block mb-1 font-medium text-formTextColor text-sm">
              Full Name<span className="text-formLabelSubColor">*</span>
            </label>
            <input
              type="text"
              name="fullName"
              placeholder="Enter full name"
              value={formData.fullName}
              onChange={handleChange}
              className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg focus:outline-none ${
                errors.fullName ? "ring-2 ring-red-500" : ""
              }`}
              maxLength="50"
              pattern="[a-zA-Z\s]*"
              required
            />
            {errors.fullName && (
              <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>
            )}
          </div>
          <div>
            <label className="block mb-1 font-medium text-formTextColor text-sm">
              Select Course Interested In
              <span className="text-formLabelSubColor">*</span>
            </label>
            <Select
              value={formData.course}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, course: value }))
              }>
              <SelectTrigger
                className={`w-full p-[1.4rem] bg-formBG text-[15px] text-black rounded-lg ${
                  errors.course ? "ring-2 ring-red-500" : ""
                }`}>
                <SelectValue placeholder="Select a Course" />
              </SelectTrigger>
              <SelectContent>
                {coursesLinks.map((course, index) => (
                  <SelectItem key={index} value={course.text}>
                    {course.text}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.course && (
              <p className="text-red-500 text-sm mt-1">{errors.course}</p>
            )}
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block mb-1 font-medium text-formTextColor text-sm">
              Contact Number<span className="text-formLabelSubColor">*</span>
            </label>
            <input
              type="text"
              name="contactNumber"
              value={formData.contactNumber}
              onChange={handleChange}
              maxLength="15"
              pattern="[0-9]*"
              required
              placeholder="Enter contact number"
              className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${
                errors.contactNumber ? "ring-2 ring-red-500" : ""
              }`}
            />
            {errors.contactNumber && (
              <p className="text-red-500 text-sm mt-1">
                {errors.contactNumber}
              </p>
            )}
          </div>
          <div>
            <label className="block mb-1 font-medium text-formTextColor text-sm">
              City<span className="text-formLabelSubColor">*</span>
            </label>
            <input
              type="text"
              name="city"
              value={formData.city}
              onChange={handleChange}
              maxLength="50"
              pattern="[a-zA-Z\s]*"
              required
              placeholder="Enter city"
              className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${
                errors.city ? "ring-2 ring-red-500" : ""
              }`}
            />
            {errors.city && (
              <p className="text-red-500 text-sm mt-1">{errors.city}</p>
            )}
          </div>
        </div>
        <div>
          <label className="block mb-1 font-medium text-formTextColor text-sm">
            Email Address<span className="text-formLabelSubColor">*</span>
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            required
            placeholder="Enter email"
            className={`w-full p-3 bg-formBG text-[15px] text-black rounded-lg ${
              errors.email ? "ring-2 ring-red-500" : ""
            }`}
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>
        <div>
          <label className="block mb-1 font-medium text-formTextColor text-sm">
            Tell Us How We Can Help
          </label>
          <textarea
            name="helpText"
            value={formData.helpText}
            onChange={handleChange}
            placeholder="Optional message"
            rows="3"
            className="w-full p-3 bg-formBG text-[15px] text-black rounded-lg"
          />
        </div>
        <div className="flex justify-center">
          <button
            type="submit"
            className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5`}
          >
            <span className="font-bold">Send Enquiry</span>
            <ArrowRight className="w-6 h-6 bg-white text-arrowIconColor rounded-full p-1" />
          </button>
        </div>
      </form>

      {/* Spinner Overlay */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-[#0000004f] bg-opacity-40 z-50 flex justify-center items-center">
          <div className="w-12 h-12 border-4 border-[#2821ff] border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
};

export default EnquireForm;
