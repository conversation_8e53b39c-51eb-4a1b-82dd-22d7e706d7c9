"use strict";exports.id=528,exports.ids=[528],exports.modules={9526:(e,t,r)=>{r.d(t,{default:()=>u});var a=r(5512),s=r(8009),o=r(9576),l=r(6422),n=r(5103),i=r(7667),d=r(8531),c=r.n(d);let u=()=>{let[e,t]=(0,s.useState)(),[r,d]=(0,s.useState)(0);(0,s.useEffect)(()=>{e&&(d(e.selectedScrollSnap()+1),e.on("select",()=>{d(e.selectedScrollSnap()+1)}))},[e]);let u=t=>{e&&e.scrollTo(t)};return(0,a.jsxs)("div",{className:"md:w-11/12 lg:w-4/5 mx-auto",children:[(0,a.jsxs)(l.<PERSON><PERSON>,{setApi:t,className:"shadow-cardsBoxShadow bg-white",opts:{loop:!0},children:[(0,a.jsx)(l.Wk,{children:i.pU.map((e,t)=>(0,a.jsx)(l.A7,{className:"h-fit",children:(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(o.Wu,{className:"flex flex-col md:flex-row items-center gap-3 md:gap-6 p-3 md:p-5 rounded-lg",children:[(0,a.jsx)(n.default,{className:"rounded-lg w-full h-[330px] md:w-[208px] md:h-[400px] md:basis-[40%] object-cover",src:e.image,width:360,height:438,alt:"cardImage"}),(0,a.jsxs)("div",{className:"flex flex-col md:gap-2 md:basis-[60%]",children:[(0,a.jsx)("h3",{className:"text-textBluePrimary font-bold mb-1 md:mb-0 text-base lg:text-xl",children:e.subTitle.toUpperCase()}),(0,a.jsx)("h4",{className:"text-customBlack font-semibold mb-2 md:mb-0 text-sm leading-[20px] desktop:text-sm lg:text-lg",children:e.title}),(0,a.jsx)("p",{className:"font-medium text-customBlack mb-2 md:mb-0 text-xs desktop:text-sm lg:text-base",children:e.desc}),e.topics&&(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm text-customBlack text-light mb-1 block",children:"You'll Learn:"}),e.topics]}),(0,a.jsx)(c(),{href:`/course/${e.slug}`,className:"flex  gap-2 bg-buttonBGPrimary font-bold text-sm text-white py-2 px-6 w-fit  justify-between rounded-full border border-transparent mt-4",children:"Learn More"})]})]})})},t))}),(0,a.jsx)(l.Q8,{className:" hidden md:flex border bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6 [&_svg]:mx-auto"}),(0,a.jsx)(l.Oj,{className:"hidden md:flex border bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6 [&_svg]:mx-auto"})]}),(0,a.jsx)("div",{className:"flex justify-center items-center mt-4 space-x-2",children:i.pU.map((e,t)=>(0,a.jsx)("button",{onClick:()=>u(t),className:`${r===t+1?"h-3 w-8 rounded-full bg-textBluePrimary":"h-3 w-3 rounded-full bg-carouselDotsBG"}`},t))})]})}},2595:(e,t,r)=>{r.d(t,{Accordion:()=>es,AccordionContent:()=>en,AccordionItem:()=>eo,AccordionTrigger:()=>el});var a=r(5512),s=r(8009),o=r(6004),l=r(9217),n=r(9952),i=r(1412),d=r(3024),c=r(830),u=r(9397),f=r(8060),m=r(96),p="Collapsible",[x,h]=(0,o.A)(p),[b,v]=x(p),g=s.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:l,disabled:n,onOpenChange:i,...u}=e,[f=!1,p]=(0,d.i)({prop:o,defaultProp:l,onChange:i});return(0,a.jsx)(b,{scope:r,disabled:n,contentId:(0,m.B)(),open:f,onOpenToggle:s.useCallback(()=>p(e=>!e),[p]),children:(0,a.jsx)(c.sG.div,{"data-state":R(f),"data-disabled":n?"":void 0,...u,ref:t})})});g.displayName=p;var w="CollapsibleTrigger",N=s.forwardRef((e,t)=>{let{__scopeCollapsible:r,...s}=e,o=v(w,r);return(0,a.jsx)(c.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":R(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...s,ref:t,onClick:(0,i.m)(e.onClick,o.onOpenToggle)})});N.displayName=w;var j="CollapsibleContent",y=s.forwardRef((e,t)=>{let{forceMount:r,...s}=e,o=v(j,e.__scopeCollapsible);return(0,a.jsx)(f.C,{present:r||o.open,children:({present:e})=>(0,a.jsx)(C,{...s,ref:t,present:e})})});y.displayName=j;var C=s.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:l,...i}=e,d=v(j,r),[f,m]=s.useState(o),p=s.useRef(null),x=(0,n.s)(t,p),h=s.useRef(0),b=h.current,g=s.useRef(0),w=g.current,N=d.open||f,y=s.useRef(N),C=s.useRef(void 0);return s.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.N)(()=>{let e=p.current;if(e){C.current=C.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();h.current=t.height,g.current=t.width,y.current||(e.style.transitionDuration=C.current.transitionDuration,e.style.animationName=C.current.animationName),m(o)}},[d.open,o]),(0,a.jsx)(c.sG.div,{"data-state":R(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!N,...i,ref:x,style:{"--radix-collapsible-content-height":b?`${b}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:N&&l})});function R(e){return e?"open":"closed"}var k=r(9018),A="Accordion",I=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[_,z,B]=(0,l.N)(A),[D,S]=(0,o.A)(A,[B,h]),P=h(),O=s.forwardRef((e,t)=>{let{type:r,...s}=e;return(0,a.jsx)(_.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,a.jsx)(L,{...s,ref:t}):(0,a.jsx)(H,{...s,ref:t})})});O.displayName=A;var[T,E]=D(A),[G,F]=D(A,{collapsible:!1}),H=s.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:l=()=>{},collapsible:n=!1,...i}=e,[c,u]=(0,d.i)({prop:r,defaultProp:o,onChange:l});return(0,a.jsx)(T,{scope:e.__scopeAccordion,value:c?[c]:[],onItemOpen:u,onItemClose:s.useCallback(()=>n&&u(""),[n,u]),children:(0,a.jsx)(G,{scope:e.__scopeAccordion,collapsible:n,children:(0,a.jsx)($,{...i,ref:t})})})}),L=s.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:l=()=>{},...n}=e,[i=[],c]=(0,d.i)({prop:r,defaultProp:o,onChange:l}),u=s.useCallback(e=>c((t=[])=>[...t,e]),[c]),f=s.useCallback(e=>c((t=[])=>t.filter(t=>t!==e)),[c]);return(0,a.jsx)(T,{scope:e.__scopeAccordion,value:i,onItemOpen:u,onItemClose:f,children:(0,a.jsx)(G,{scope:e.__scopeAccordion,collapsible:!0,children:(0,a.jsx)($,{...n,ref:t})})})}),[U,W]=D(A),$=s.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:o,dir:l,orientation:d="vertical",...u}=e,f=s.useRef(null),m=(0,n.s)(f,t),p=z(r),x="ltr"===(0,k.jH)(l),h=(0,i.m)(e.onKeyDown,e=>{if(!I.includes(e.key))return;let t=e.target,r=p().filter(e=>!e.ref.current?.disabled),a=r.findIndex(e=>e.ref.current===t),s=r.length;if(-1===a)return;e.preventDefault();let o=a,l=s-1,n=()=>{(o=a+1)>l&&(o=0)},i=()=>{(o=a-1)<0&&(o=l)};switch(e.key){case"Home":o=0;break;case"End":o=l;break;case"ArrowRight":"horizontal"===d&&(x?n():i());break;case"ArrowDown":"vertical"===d&&n();break;case"ArrowLeft":"horizontal"===d&&(x?i():n());break;case"ArrowUp":"vertical"===d&&i()}let c=o%s;r[c].ref.current?.focus()});return(0,a.jsx)(U,{scope:r,disabled:o,direction:l,orientation:d,children:(0,a.jsx)(_.Slot,{scope:r,children:(0,a.jsx)(c.sG.div,{...u,"data-orientation":d,ref:m,onKeyDown:o?void 0:h})})})}),K="AccordionItem",[Q,Z]=D(K),q=s.forwardRef((e,t)=>{let{__scopeAccordion:r,value:s,...o}=e,l=W(K,r),n=E(K,r),i=P(r),d=(0,m.B)(),c=s&&n.value.includes(s)||!1,u=l.disabled||e.disabled;return(0,a.jsx)(Q,{scope:r,open:c,disabled:u,triggerId:d,children:(0,a.jsx)(g,{"data-orientation":l.orientation,"data-state":et(c),...i,...o,ref:t,disabled:u,open:c,onOpenChange:e=>{e?n.onItemOpen(s):n.onItemClose(s)}})})});q.displayName=K;var M="AccordionHeader",V=s.forwardRef((e,t)=>{let{__scopeAccordion:r,...s}=e,o=W(A,r),l=Z(M,r);return(0,a.jsx)(c.sG.h3,{"data-orientation":o.orientation,"data-state":et(l.open),"data-disabled":l.disabled?"":void 0,...s,ref:t})});V.displayName=M;var X="AccordionTrigger",Y=s.forwardRef((e,t)=>{let{__scopeAccordion:r,...s}=e,o=W(A,r),l=Z(X,r),n=F(X,r),i=P(r);return(0,a.jsx)(_.ItemSlot,{scope:r,children:(0,a.jsx)(N,{"aria-disabled":l.open&&!n.collapsible||void 0,"data-orientation":o.orientation,id:l.triggerId,...i,...s,ref:t})})});Y.displayName=X;var J="AccordionContent",ee=s.forwardRef((e,t)=>{let{__scopeAccordion:r,...s}=e,o=W(A,r),l=Z(J,r),n=P(r);return(0,a.jsx)(y,{role:"region","aria-labelledby":l.triggerId,"data-orientation":o.orientation,...n,...s,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=J;var er=r(8755),ea=r(6645);let es=O,eo=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(q,{ref:r,className:(0,ea.cn)(e),...t}));eo.displayName="AccordionItem";let el=s.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsx)(V,{className:"flex",children:(0,a.jsxs)(Y,{ref:s,className:(0,ea.cn)("flex flex-1 items-center justify-between transition-all text-left [&[data-state=open]>svg]:rotate-180",e),...r,children:[t,(0,a.jsx)(er.A,{className:"h-7 w-7 shrink-0 text-white transition-transform duration-200"})]})}));el.displayName=Y.displayName;let en=s.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsx)(ee,{ref:s,className:"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:(0,a.jsx)("div",{className:(0,ea.cn)("pb-4 pt-0",e),children:t})}));en.displayName=ee.displayName},9576:(e,t,r)=>{r.d(t,{Wu:()=>n,Zp:()=>l});var a=r(5512),s=r(8009),o=r(6645);let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("rounded-xl bg-card",e),...t}));l.displayName="Card",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let n=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));n.displayName="CardContent",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},6422:(e,t,r)=>{r.d(t,{FN:()=>p,Wk:()=>x,A7:()=>h,Oj:()=>v,Q8:()=>b});var a=r(5512),s=r(8009),o=r(7203),l=r(2706),n=r(9905),i=r(6645),d=r(2705);let c=(0,r(1643).F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef(({className:e,variant:t,size:r,asChild:s=!1,...o},l)=>{let n=s?d.DX:"button";return(0,a.jsx)(n,{className:(0,i.cn)(c({variant:t,size:r,className:e})),ref:l,...o})});u.displayName="Button";let f=s.createContext(null);function m(){let e=s.useContext(f);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let p=s.forwardRef(({orientation:e="horizontal",opts:t,setApi:r,plugins:l,className:n,children:d,...c},u)=>{let[m,p]=(0,o.A)({...t,axis:"horizontal"===e?"x":"y"},l),[x,h]=s.useState(!1),[b,v]=s.useState(!1),g=s.useCallback(e=>{e&&(h(e.canScrollPrev()),v(e.canScrollNext()))},[]),w=s.useCallback(()=>{p?.scrollPrev()},[p]),N=s.useCallback(()=>{p?.scrollNext()},[p]),j=s.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),w()):"ArrowRight"===e.key&&(e.preventDefault(),N())},[w,N]);return s.useEffect(()=>{p&&r&&r(p)},[p,r]),s.useEffect(()=>{if(p)return g(p),p.on("reInit",g),p.on("select",g),()=>{p?.off("select",g)}},[p,g]),(0,a.jsx)(f.Provider,{value:{carouselRef:m,api:p,opts:t,orientation:e||(t?.axis==="y"?"vertical":"horizontal"),scrollPrev:w,scrollNext:N,canScrollPrev:x,canScrollNext:b},children:(0,a.jsx)("div",{ref:u,onKeyDownCapture:j,className:(0,i.cn)("relative",n),role:"region","aria-roledescription":"carousel",...c,children:d})})});p.displayName="Carousel";let x=s.forwardRef(({className:e,...t},r)=>{let{carouselRef:s,orientation:o}=m();return(0,a.jsx)("div",{ref:s,className:"overflow-hidden",children:(0,a.jsx)("div",{ref:r,className:(0,i.cn)("flex","horizontal"===o?"-ml-4":"-mt-4 flex-col",e),...t})})});x.displayName="CarouselContent";let h=s.forwardRef(({className:e,...t},r)=>{let{orientation:s}=m();return(0,a.jsx)("div",{ref:r,role:"group","aria-roledescription":"slide",className:(0,i.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===s?"pl-4":"pt-4",e),...t})});h.displayName="CarouselItem";let b=s.forwardRef(({className:e,variant:t="outline",size:r="icon",...s},o)=>{let{orientation:n,scrollPrev:d,canScrollPrev:c}=m();return(0,a.jsxs)(u,{ref:o,variant:t,size:r,className:(0,i.cn)("absolute rounded-full","horizontal"===n?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:d,...s,children:[(0,a.jsx)(l.A,{className:"h-6 w-6 text-textBluePrimary"}),(0,a.jsx)("span",{className:"sr-only",children:"Previous slide"})]})});b.displayName="CarouselPrevious";let v=s.forwardRef(({className:e,variant:t="outline",size:r="icon",...s},o)=>{let{orientation:l,scrollNext:d,canScrollNext:c}=m();return(0,a.jsxs)(u,{ref:o,variant:t,size:r,className:(0,i.cn)("absolute rounded-full","horizontal"===l?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:d,...s,children:[(0,a.jsx)(n.A,{className:"h-6 w-6 text-textBluePrimary"}),(0,a.jsx)("span",{className:"sr-only",children:"Next slide"})]})});v.displayName="CarouselNext"}};