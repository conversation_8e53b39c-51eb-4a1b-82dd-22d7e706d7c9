import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import accordionData from "@/constant/faqData";
const FAQContainer = () => {
  return (
    <div className="bg-[url('/assets/faqImage.webp')] bg-cover bg-center bg-local px-4 py-8 md:p-12 lg:py-28 xl:py-28">
      <h3 className="text-white font-extrabold text-2xl md:text-4xl lg:text-6xl mb-4 lg:mb-16">
        Frequently Asked Questions.
      </h3>
      <Accordion
        type="single" // Ensures only one can be open at a time
        collapsible
        className="grid gap-4 items-start justify-center pt-4 grid-cols-1"
      >
        {accordionData.map((item, index) => (
          <AccordionItem
            key={index}
            value={`item-${index}`}
            className="p-3 xl:p-4 rounded bg-accordionBG"
          >
            <AccordionTrigger className="text-white text-sm font-medium xl:text-base">
              {item.title}
            </AccordionTrigger>
            <AccordionContent className="text-white pt-3 text-sm font-medium xl:text-base">
              {item.content}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default FAQContainer;
