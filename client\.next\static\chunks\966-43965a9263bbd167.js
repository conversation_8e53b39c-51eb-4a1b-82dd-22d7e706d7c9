"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[966],{9053:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},8867:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},1719:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},1902:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(7401).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3610:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},9741:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(2115),i=n(8166),o=n(8068),l=n(2317),a=n(5155);function u(e){let t=e+"CollectionProvider",[n,u]=(0,i.A)(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,i=r.useRef(null),o=r.useRef(new Map).current;return(0,a.jsx)(s,{scope:t,itemMap:o,collectionRef:i,children:n})};f.displayName=t;let d=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=c(d,n),u=(0,o.s)(t,i.collectionRef);return(0,a.jsx)(l.DX,{ref:u,children:r})});p.displayName=d;let m=e+"CollectionItemSlot",h="data-radix-collection-item",v=r.forwardRef((e,t)=>{let{scope:n,children:i,...u}=e,s=r.useRef(null),f=(0,o.s)(t,s),d=c(m,n);return r.useEffect(()=>(d.itemMap.set(s,{ref:s,...u}),()=>void d.itemMap.delete(s))),(0,a.jsx)(l.DX,{[h]:"",ref:f,children:i})});return v.displayName=m,[{Provider:f,Slot:p,ItemSlot:v},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},8068:(e,t,n)=>{n.d(t,{s:()=>l,t:()=>o});var r=n(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function l(...e){return r.useCallback(o(...e),e)}},8166:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(2115),i=n(5155);function o(e,t=[]){let n=[],l=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return l.scopeName=e,[function(t,o){let l=r.createContext(o),a=n.length;n=[...n,o];let u=t=>{let{scope:n,children:o,...u}=t,s=n?.[e]?.[a]||l,c=r.useMemo(()=>u,Object.values(u));return(0,i.jsx)(s.Provider,{value:c,children:o})};return u.displayName=t+"Provider",[u,function(n,i){let u=i?.[e]?.[a]||l,s=r.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(l,...t)]}},4256:(e,t,n)=>{n.d(t,{jH:()=>o});var r=n(2115);n(5155);var i=r.createContext(void 0);function o(e){let t=r.useContext(i);return e||t||"ltr"}},9674:(e,t,n)=>{n.d(t,{lg:()=>y,qW:()=>d,bL:()=>v});var r,i=n(2115),o=n(3610),l=n(3360),a=n(8068),u=n(1524),s=n(5155),c="dismissableLayer.update",f=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:v,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:w,onDismiss:x,...b}=e,E=i.useContext(f),[R,A]=i.useState(null),C=null!==(d=null==R?void 0:R.ownerDocument)&&void 0!==d?d:null===(n=globalThis)||void 0===n?void 0:n.document,[,L]=i.useState({}),S=(0,a.s)(t,e=>A(e)),P=Array.from(E.layers),[O]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=P.indexOf(O),N=R?P.indexOf(R):-1,D=E.layersWithOutsidePointerEventsDisabled.size>0,k=N>=T,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!k||n||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},C),F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},C);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N!==E.layers.size-1||(null==v||v(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},C),i.useEffect(()=>{if(R)return p&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),m(),()=>{p&&1===E.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[R,C,p,E]),i.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),m())},[R,E]),i.useEffect(()=>{let e=()=>L({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(l.sG.div,{...b,ref:S,style:{pointerEvents:D?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,F.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,F.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});d.displayName="DismissableLayer";var p=i.forwardRef((e,t)=>{let n=i.useContext(f),r=i.useRef(null),o=(0,a.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.sG.div,{...e,ref:o})});function m(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,l.hO)(o,a):o.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var v=d,y=p},2292:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(2115),i=0;function o(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:l()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:l()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},196:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(2115),i=n(8068),o=n(3360),l=n(1524),a=n(5155),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:y,...g}=e,[w,x]=r.useState(null),b=(0,l.c)(v),E=(0,l.c)(y),R=r.useRef(null),A=(0,i.s)(t,e=>x(e)),C=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:m(R.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||m(R.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,C.paused]),r.useEffect(()=>{if(w){h.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,c);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(s,c);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),w.removeEventListener(s,E),h.remove(C)},0)}}},[w,b,E,C]);let L=r.useCallback(e=>{if(!n&&!f||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[i,o]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);i&&o?e.shiftKey||r!==o?e.shiftKey&&r===i&&(e.preventDefault(),n&&m(o,{select:!0})):(e.preventDefault(),n&&m(i,{select:!0})):r===t&&e.preventDefault()}},[n,f,C.paused]);return(0,a.jsx)(o.sG.div,{tabIndex:-1,...g,ref:A,onKeyDown:L})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null===(n=(e=v(e,t))[0])||void 0===n||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},7668:(e,t,n)=>{n.d(t,{B:()=>u});var r,i=n(2115),o=n(6611),l=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),a=0;function u(e){let[t,n]=i.useState(l());return(0,o.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},905:(e,t,n)=>{n.d(t,{Mz:()=>eU,i3:()=>eZ,UC:()=>eY,bL:()=>eX,Bk:()=>eD});var r=n(2115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function g(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=h(y(t)),u=v(a),s=p(t),c="y"===l,f=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,g=i[u]/2-o[u]/2;switch(s){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(m(t)){case"start":r[a]-=g*(n&&c?-1:1);break;case"end":r[a]+=g*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:f}=E(s,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:o,fn:h}=a[n],{x:v,y:y,data:g,reset:w}=await h({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,f=null!=y?y:f,p={...p,[o]:{...p[o],...g}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:f}=E(s,d,u)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=x(m),v=a[p?"floating"===f?"reference":"floating":f],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),g="floating"===f?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),E=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},R=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:w,strategy:u}):g);return{top:(y.top-R.top+h.top)/E.y,bottom:(R.bottom-y.bottom+h.bottom)/E.y,left:(y.left-R.left+h.left)/E.x,right:(R.right-y.right+h.right)/E.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function L(e){return i.some(t=>e[t]>=0)}async function S(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=m(n),u="y"===y(n),s=["left","top"].includes(l)?-1:1,c=o&&u?-1:1,f=d(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:g}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof g&&(v="end"===a?-1*g:g),u?{x:v*c,y:h*s}:{x:h*s,y:v*c}}function P(){return"undefined"!=typeof window}function O(e){return D(e)?(e.nodeName||"").toLowerCase():"#document"}function T(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function N(e){var t;return null==(t=(D(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function D(e){return!!P()&&(e instanceof Node||e instanceof T(e).Node)}function k(e){return!!P()&&(e instanceof Element||e instanceof T(e).Element)}function j(e){return!!P()&&(e instanceof HTMLElement||e instanceof T(e).HTMLElement)}function F(e){return!!P()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof T(e).ShadowRoot)}function M(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=I(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function W(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function H(e){let t=z(),n=k(e)?I(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(O(e))}function I(e){return T(e).getComputedStyle(e)}function _(e){return k(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||N(e);return F(t)?t.host:t}function $(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=V(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:j(n)&&M(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=T(i);if(o){let e=G(l);return t.concat(l,l.visualViewport||[],M(i)?i:[],e&&n?$(e):[])}return t.concat(i,$(i,[],n))}function G(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=I(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=j(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,u=a(n)!==o||a(r)!==l;return u&&(n=o,r=l),{width:n,height:r,$:u}}function K(e){return k(e)?e:e.contextElement}function X(e){let t=K(e);if(!j(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=q(t),l=(o?a(n.width):n.width)/r,u=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),u&&Number.isFinite(u)||(u=1),{x:l,y:u}}let U=s(0);function Y(e){let t=T(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function Z(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=K(e),a=s(1);t&&(r?k(r)&&(a=X(r)):a=X(e));let u=(void 0===(i=n)&&(i=!1),r&&(!i||r===T(l))&&i)?Y(l):s(0),c=(o.left+u.x)/a.x,f=(o.top+u.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=T(l),t=r&&k(r)?T(r):r,n=e,i=G(n);for(;i&&r&&t!==n;){let e=X(i),t=i.getBoundingClientRect(),r=I(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,p*=e.y,c+=o,f+=l,i=G(n=T(i))}}return b({width:d,height:p,x:c,y:f})}function J(e,t){let n=_(e).scrollLeft;return t?t.left+n:Z(N(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=T(e),r=N(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,u=0;if(i){o=i.width,l=i.height;let e=z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=N(e),n=_(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===I(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:u}}(N(e));else if(k(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=j(e)?X(e):s(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=Y(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===I(e).position}function en(e,t){if(!j(e)||"fixed"===I(e).position)return null;if(t)return t(e);let n=e.offsetParent;return N(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=T(e);if(W(e))return n;if(!j(e)){let t=V(e);for(;t&&!B(t);){if(k(t)&&!et(t))return t;t=V(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(O(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!H(r)?n:r||function(e){let t=V(e);for(;j(t)&&!B(t);){if(H(t))return t;if(W(t))break;t=V(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=j(t),i=N(t),o="fixed"===n,l=Z(e,!0,o,t),a={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!o){if(("body"!==O(t)||M(i))&&(a=_(t)),r){let e=Z(t,!0,o,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else i&&(u.x=J(i))}let c=!i||r||o?s(0):Q(i,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=N(r),a=!!t&&W(t.floating);if(r===l||a&&o)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),f=s(0),d=j(r);if((d||!d&&!o)&&(("body"!==O(r)||M(l))&&(u=_(r)),j(r))){let e=Z(r);c=X(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||o?s(0):Q(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+f.x+p.x,y:n.y*c.y-u.scrollTop*c.y+f.y+p.y}},getDocumentElement:N,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?W(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=$(e,[],!1).filter(e=>k(e)&&"body"!==O(e)),i=null,o="fixed"===I(e).position,l=o?V(e):e;for(;k(l)&&!B(l);){let t=I(l),n=H(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||M(l)&&!n&&function e(t,n){let r=V(t);return!(r===n||!k(r)||B(r))&&("fixed"===I(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=V(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],s=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,u,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:X,isElement:k,isRTL:function(e){return"rtl"===I(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:u,elements:s,middlewareData:c}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let g=x(p),w={x:n,y:r},b=h(y(i)),E=v(b),R=await u.getDimensions(f),A="y"===b,C=A?"clientHeight":"clientWidth",L=a.reference[E]+a.reference[b]-w[b]-a.floating[E],S=w[b]-a.reference[b],P=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),O=P?P[C]:0;O&&await (null==u.isElement?void 0:u.isElement(P))||(O=s.floating[C]||a.floating[E]);let T=O/2-R[E]/2-1,N=o(g[A?"top":"left"],T),D=o(g[A?"bottom":"right"],T),k=O-R[E]-D,j=O/2-R[E]/2+(L/2-S/2),F=l(N,o(j,k)),M=!c.arrow&&null!=m(i)&&j!==F&&a.reference[E]/2-(j<N?N:D)-R[E]/2<0,W=M?j<N?j-N:j-k:0;return{[b]:w[b]+W,data:{[b]:F,centerOffset:j-F-W,...M&&{alignmentOffset:W}},reset:M}}}),eu=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return R(e,t,{...i,platform:o})};var es=n(7650),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ef(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function em(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,u=await S(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),f={x:n,y:r},m=await A(t,c),v=y(p(i)),g=h(v),w=f[g],x=f[v];if(a){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+m[e],r=w-m[t];w=l(n,o(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+m[e],r=x-m[t];x=l(n,o(x,r))}let b=s.fn({...t,[g]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:a,[v]:u}}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=d(e,t),c={x:n,y:r},f=y(i),m=h(f),v=c[m],g=c[f],w=d(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=o.reference[m]-o.floating[e]+x.mainAxis,n=o.reference[m]+o.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(s){var b,E;let e="y"===m?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[f]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[f])||0)+(t?0:x.crossAxis),r=o.reference[f]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[m]:v,[f]:g}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:f,elements:x}=t,{mainAxis:b=!0,crossAxis:E=!0,fallbackPlacements:R,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:S=!0,...P}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let O=p(a),T=y(c),N=p(c)===c,D=await (null==f.isRTL?void 0:f.isRTL(x.floating)),k=R||(N||!S?[w(c)]:function(e){let t=w(e);return[g(e),t,g(t)]}(c)),j="none"!==L;!R&&j&&k.push(...function(e,t,n,r){let i=m(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(g)))),o}(c,S,L,D));let F=[c,...k],M=await A(t,P),W=[],H=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&W.push(M[O]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),i=h(y(e)),o=v(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=w(l)),[l,w(l)]}(a,s,D);W.push(M[e[0]],M[e[1]])}if(H=[...H,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=F[e];if(t)return{data:{index:e,overflows:H},reset:{placement:t}};let n=null==(o=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let e=null==(l=H.filter(e=>{if(j){let t=y(e.placement);return t===T||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a;let{placement:u,rects:s,platform:c,elements:f}=t,{apply:h=()=>{},...v}=d(e,t),g=await A(t,v),w=p(u),x=m(u),b="y"===y(u),{width:E,height:R}=s.floating;"top"===w||"bottom"===w?(i=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,i="end"===x?"top":"bottom");let C=R-g.top-g.bottom,L=E-g.left-g.right,S=o(R-g[i],C),P=o(E-g[a],L),O=!t.middlewareData.shift,T=S,N=P;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=L),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(T=C),O&&!x){let e=l(g.left,0),t=l(g.right,0),n=l(g.top,0),r=l(g.bottom,0);b?N=E-2*(0!==e||0!==t?e+t:l(g.left,g.right)):T=R-2*(0!==n||0!==r?n+r:l(g.top,g.bottom))}await h({...t,availableWidth:N,availableHeight:T});let D=await c.getDimensions(f.floating);return E!==D.width||R!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=C(await A(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:L(e)}}}case"escaped":{let e=C(await A(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:L(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...eh(e),options:[e,t]});var eR=n(3360),eA=n(5155),eC=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eA.jsx)(eR.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eC.displayName="Arrow";var eL=n(8068),eS=n(8166),eP=n(1524),eO=n(6611),eT="Popper",[eN,eD]=(0,eS.A)(eT),[ek,ej]=eN(eT),eF=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eA.jsx)(ek,{scope:t,anchor:i,onAnchorChange:o,children:n})};eF.displayName=eT;var eM="PopperAnchor",eW=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=ej(eM,n),a=r.useRef(null),u=(0,eL.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eA.jsx)(eR.sG.div,{...o,ref:u})});eW.displayName=eM;var eH="PopperContent",[ez,eB]=eN(eH),eI=r.forwardRef((e,t)=>{var n,i,a,s,c,f,d,p;let{__scopePopper:m,side:h="bottom",sideOffset:v=0,align:y="center",alignOffset:g=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:C="optimized",onPlaced:L,...S}=e,P=ej(eH,m),[O,T]=r.useState(null),D=(0,eL.s)(t,e=>T(e)),[k,j]=r.useState(null),F=function(e){let[t,n]=r.useState(void 0);return(0,eO.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(k),M=null!==(d=null==F?void 0:F.width)&&void 0!==d?d:0,W=null!==(p=null==F?void 0:F.height)&&void 0!==p?p:0,H="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},z=Array.isArray(b)?b:[b],B=z.length>0,I={padding:H,boundary:z.filter(eG),altBoundary:B},{refs:_,floatingStyles:V,placement:G,isPositioned:q,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(i);ef(p,i)||m(i);let[h,v]=r.useState(null),[y,g]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=r.useCallback(e=>{e!==A.current&&(A.current=e,g(e))},[]),b=l||h,E=a||y,R=r.useRef(null),A=r.useRef(null),C=r.useRef(f),L=null!=s,S=em(s),P=em(o),O=em(c),T=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};P.current&&(e.platform=P.current),eu(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};N.current&&!ef(C.current,t)&&(C.current=t,es.flushSync(()=>{d(t)}))})},[p,t,n,P,O]);ec(()=>{!1===c&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let N=r.useRef(!1);ec(()=>(N.current=!0,()=>{N.current=!1}),[]),ec(()=>{if(b&&(R.current=b),E&&(A.current=E),b&&E){if(S.current)return S.current(b,E,T);T()}},[b,E,T,S,L]);let D=r.useMemo(()=>({reference:R,floating:A,setReference:w,setFloating:x}),[w,x]),k=r.useMemo(()=>({reference:b,floating:E}),[b,E]),j=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!k.floating)return e;let t=ep(k.floating,f.x),r=ep(k.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,k.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:T,refs:D,elements:k,floatingStyles:j}),[f,T,D,k,j])}({strategy:"fixed",placement:h+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=K(e),m=a||s?[...p?$(p):[],...$(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,i=N(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=d;if(c||t(),!h||!v)return;let y=u(m),g=u(i.clientWidth-(p+h)),w={rootMargin:-y+"px "+-g+"px "+-u(i.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:l(0,o(1,f))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==f){if(!x)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||s(),x=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let g=d?Z(e):null;return d&&function t(){let r=Z(e);g&&!el(g,r)&&n(),g=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===C})},elements:{reference:P.anchor},middleware:[ev({mainAxis:v+W,alignmentAxis:g}),x&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eg():void 0,...I}),x&&ew({...I}),ex({...I,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),k&&eE({element:k,padding:w}),eq({arrowWidth:M,arrowHeight:W}),A&&eb({strategy:"referenceHidden",...I})]}),[U,Y]=eK(G),J=(0,eP.c)(L);(0,eO.N)(()=>{q&&(null==J||J())},[q,J]);let Q=null===(n=X.arrow)||void 0===n?void 0:n.x,ee=null===(i=X.arrow)||void 0===i?void 0:i.y,et=(null===(a=X.arrow)||void 0===a?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eO.N)(()=>{O&&er(window.getComputedStyle(O).zIndex)},[O]),(0,eA.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:q?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(s=X.transformOrigin)||void 0===s?void 0:s.x,null===(c=X.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(f=X.hide)||void 0===f?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(ez,{scope:m,placedSide:U,onArrowChange:j,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eA.jsx)(eR.sG.div,{"data-side":U,"data-align":Y,...S,ref:D,style:{...S.style,animation:q?void 0:"none"}})})})});eI.displayName=eH;var e_="PopperArrow",eV={top:"bottom",right:"left",bottom:"top",left:"right"},e$=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eB(e_,n),o=eV[i.placedSide];return(0,eA.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eC,{...r,ref:t,style:{...r.style,display:"block"}})})});function eG(e){return null!==e}e$.displayName=e_;var eq=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,m]=eK(a),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!==(o=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+f/2,y=(null!==(l=null===(i=s.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,g="",w="";return"bottom"===p?(g=c?h:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(g=c?h:"".concat(v,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(g="".concat(-d,"px"),w=c?h:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+d,"px"),w=c?h:"".concat(y,"px")),{data:{x:g,y:w}}}});function eK(e){let[t,n="center"]=e.split("-");return[t,n]}var eX=eF,eU=eW,eY=eI,eZ=e$},7323:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(2115),i=n(7650),o=n(3360),l=n(6611),a=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:s,...c}=e,[f,d]=r.useState(!1);(0,l.N)(()=>d(!0),[]);let p=s||f&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return p?i.createPortal((0,a.jsx)(o.sG.div,{...c,ref:t}),p):null});u.displayName="Portal"},3360:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>a});var r=n(2115),i=n(7650),o=n(2317),l=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...i}=e,a=r?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function u(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},2317:(e,t,n)=>{n.d(t,{DX:()=>l});var r=n(2115),i=n(8068),o=n(5155),l=r.forwardRef((e,t)=>{let{children:n,...i}=e,l=r.Children.toArray(n),u=l.find(s);if(u){let e=u.props.children,n=l.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(a,{...i,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,o.jsx)(a,{...i,ref:t,children:n})});l.displayName="Slot";var a=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{o(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,i.t)(t,e):e})}return r.Children.count(n)>1?r.Children.only(null):null});a.displayName="SlotClone";var u=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function s(e){return r.isValidElement(e)&&e.type===u}},1524:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(2115);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},1488:(e,t,n)=>{n.d(t,{i:()=>o});var r=n(2115),i=n(1524);function o({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,l]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[o]=n,l=r.useRef(o),a=(0,i.c)(t);return r.useEffect(()=>{l.current!==o&&(a(o),l.current=o)},[o,l,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,u=a?e:o,s=(0,i.c)(n);return[u,r.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else l(t)},[a,e,l,s])]}},6611:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(2115),i=globalThis?.document?r.useLayoutEffect:()=>{}},3543:(e,t,n)=>{n.d(t,{s:()=>l});var r=n(2115),i=n(3360),o=n(5155),l=r.forwardRef((e,t)=>(0,o.jsx)(i.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));l.displayName="VisuallyHidden"}}]);