(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[634],{2885:(e,t,s)=>{Promise.resolve().then(s.bind(s,3197))},3197:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(5155),l=s(2115),r=s(1489);s(3962);let i=()=>{let[e,t]=(0,l.useState)([]),[s,i]=(0,l.useState)(!0);return(0,l.useEffect)(()=>{(async()=>{try{let e=await fetch("https://www.skylineaviation.training/api/events?populate[image][fields]=url"),s=(await e.json()).data.map(e=>{var t;return{title:e.title,desc:e.description,image:"https://www.skylineaviation.training"+(null===(t=e.image)||void 0===t?void 0:t.url)}});t(s)}catch(e){console.error("Failed to fetch events:",e)}finally{i(!1)}})()},[]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"relative bg-eventBanner bg-cover bg-center h-[700px]",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]"}),(0,a.jsxs)("div",{className:"absolute bottom-12 left-0 right-0",children:[(0,a.jsx)("h3",{className:"text-center text-2xl md:text-3xl lg:text-5xl font-bold text-white mb-2 lg:mb-4",children:"Events"}),(0,a.jsx)("p",{className:"text-center text-base lg:text-xl font-bold text-white",children:"See what's going on at The Skyline Aviation Club. Stay up to date with the latest news and updates !"})]})]}),(0,a.jsx)("div",{className:"p-6 lg:py-8 lg:px-20",children:s?(0,a.jsx)("p",{className:"text-center text-white",children:"Loading events..."}):(0,a.jsx)("div",{className:"grid grid-cols-1 justify-items-center md:grid-cols-4 xl:grid-cols-4 gap-4",children:(0,a.jsx)(r.A,{cardData:e,isLandingPage:!1})})})]})}},1489:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(5155),l=s(728),r=s(5565),i=s(2115);let n=e=>{let{cardData:t,isLandingPage:s}=e,[n,d]=(0,i.useState)(null),c=e=>{d(t=>t===e?null:e)};return(0,a.jsx)(a.Fragment,{children:t.map((e,t)=>(0,a.jsx)(l.Zp,{className:"w-full h-full flex flex-col bg-descLightBlack",children:(0,a.jsxs)(l.Wu,{className:"flex flex-col p-0 rounded-md h-full",children:[(0,a.jsx)(r.default,{src:e.image,width:1900,height:1600,alt:"avatar",className:"w-full h-[300px] sm:h-[300px] md:h-[220px] rounded-t-xl object-cover",quality:100}),(0,a.jsxs)("div",{className:"bg-descLightBlack p-3 lg:p-6 rounded-b-lg flex flex-col flex-grow justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm md:text-base font-bold mb-1 text-white",children:e.title}),(0,a.jsx)("p",{className:"text-xs md:text-sm font-medium text-white break-words",children:n===t?e.desc:"".concat(e.desc.slice(0,70),"...")})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("button",{onClick:()=>c(t),className:"text-white text-xs md:text-sm px-2 py-1 bg-blue-900 rounded-md hover:bg-blue-800 transition-colors",children:n===t?"Show Less":"Show More"})})]})]})},t))})}},728:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>n,Zp:()=>i});var a=s(5155),l=s(2115),r=s(7849);let i=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("rounded-xl bg-card",s),...l})});i.displayName="Card",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...l})}).displayName="CardHeader",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("font-semibold leading-none tracking-tight",s),...l})}).displayName="CardTitle",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",s),...l})}).displayName="CardDescription";let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",s),...l})});n.displayName="CardContent",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"},3962:(e,t,s)=>{},7849:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var a=s(3463),l=s(9795);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[565,181,441,517,358],()=>t(2885)),_N_E=e.O()}]);