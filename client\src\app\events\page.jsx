"use client";
import { use, useEffect, useState } from "react";
import EventCard from "@/components/common/EventCard";
import { eventsPageData } from "@/constant/eventsData.jsx";

const EventsPage = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await fetch(process.env.NEXT_PUBLIC_URL);
        const data = await response.json();
        // console.log(data.data[0].image);


        // Transform the API data to match the expected format
        const formattedEvents = data.data.map((event) => ({
          title: event.title,
          desc: event.description,
          image: process.env.NEXT_PUBLIC_STRAPI_URL + event.image?.url,
        }
        ));
        // console.log(formattedEvents[0]?.image)

        // console.log("yooo", formattedEvents);
        // console.log(formattedEvents);


        setEvents(formattedEvents);
      } catch (error) {
        console.error("Failed to fetch events:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
    // setEvents(eventsPageData);
    // setLoading(false);
  }, []);

  return (
    <>
      <div className="relative bg-eventBanner bg-cover bg-center h-[700px]">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]"></div>
        <div className="absolute bottom-12 left-0 right-0">
          <h3 className="text-center text-2xl md:text-3xl lg:text-5xl font-bold text-white mb-2 lg:mb-4">
            Events
          </h3>
          <p className="text-center text-base lg:text-xl font-bold text-white">
            See what's going on at The Skyline Aviation Club. Stay up to date
            with the latest news and updates !
          </p>
        </div>
      </div>

      <div className="p-6 lg:py-8 lg:px-20">
        {loading ? (
          <p className="text-center text-white">Loading events...</p>
        ) : (
          <div className="grid grid-cols-1 justify-items-center md:grid-cols-4 xl:grid-cols-4 gap-4">
            <EventCard cardData={events} isLandingPage={false} />
          </div>
        )}
      </div>
    </>
  );
};

export default EventsPage;
