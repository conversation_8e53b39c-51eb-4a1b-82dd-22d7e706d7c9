import { coursesLinks } from "@/constant/navbarLinks";
import { Facebook, Instagram, Linkedin, Youtube } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { courseData } from "@/constant/courseDetailData";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-footerBG px-2 py-4 md:px-6 lg:px-12 xl:px-16">
      <div className="flex flex-col xl:flex-row gap-8">

        {/* Logo + Description + Socials */}
        <div className="flex flex-col items-start lg:items-start text-start lg:text-start w-full xl:w-1/4">
          <Image
            src="/skyline_logo.png"
            alt="The Skyline Aviation Club Logo"
            width={140}
            height={130}
            className="mb-4"
          />
          <h2 className="text-xl font-bold text-footerPrimaryText mb-2">
            The Skyline Aviation Club <span className="align-super text-sm">&reg;</span>
          </h2>
          <p className="text-xs font-bold text-footerPrimaryText">
            Since 1987, An Aviation Educational & Charitable Trust. <br />
            Registered By Government.
          </p>
          <div className="flex gap-3 mt-4">
            <Link href="https://www.instagram.com/skylineaviationofficial/profilecard/?igsh=MTNuZzZiODdjc3NrNQ==" className="bg-gray-300 p-3 rounded-full" aria-label="Instagram">
              <Image src="/instagram.png" alt="Instagram" width={20} height={20} />
            </Link>
            <Link href="https://youtube.com/@captadmanek?feature=shared" className="bg-gray-300 p-3 rounded-full" aria-label="YouTube">
              <Image src="/youtube.png" alt="YouTube" width={20} height={20} />
            </Link>
            <Link href="https://www.linkedin.com/in/capt-dr-ad-manek-547722134?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app" className="bg-gray-300 p-3 rounded-full" aria-label="LinkedIn">
              <Image src="/linkedin.png" alt="LinkedIn" width={20} height={20} />
            </Link>
            <Link href="https://www.facebook.com/share/1CJ9ntLvHa/?mibextid=wwXIfr" className="bg-gray-300 p-3 rounded-full" aria-label="Facebook">
              <Image src="/facebook.png" alt="Facebook" width={20} height={20} />
            </Link>
          </div>
        </div>

        {/* Course + Contact + Address Section (flex changes based on screen) */}
        <div className="flex flex-col md:flex-row lg:flex-row xl:flex-row gap-8 w-full xl:w-3/4">

          {/* Courses */}
          <div className="flex-1">
            <h3 className="text-lg font-bold mb-2 text-footerPrimaryText">Courses</h3>
            <ul className="space-y-1">
              {coursesLinks.map((item, index) => (
                <li key={index} className="capitalize">
                  <Link href={`/course/${item.link}`} className="text-black text-sm font-semibold capitalize">
                   {item.text}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="flex-1">
            <h3 className="text-lg font-bold mb-2 text-footerPrimaryText">Contact Info</h3>
            <ul className="space-y-1">
              <li>
                <Link href="tel:+919820891262" className="text-black text-sm font-semibold">
                  +91 9820891262
                </Link>
              </li>
              <li>
                <Link href="tel:+919820896262" className="text-black text-sm font-semibold">
                  +91 9820896262
                </Link>
              </li>
              <li>
                <Link href="tel:+912228983516" className="text-black text-sm font-semibold">
                  +91 2228983516
                </Link>
              </li>
              <li>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-black text-sm font-semibold whitespace-nowrap"
                >
                  Email: <EMAIL>
                </Link>
              </li>
              <li className="text-black text-sm font-semibold whitespace-nowrap">
                Amateur Radio Call Sign: VU2 ADO
              </li>
            </ul>
          </div>

          {/* Address - hidden on md, shown below instead */}
          <div className="flex-1 hidden md:block">
            <h3 className="text-lg font-bold mb-2 text-footerPrimaryText">Address</h3>
            <p className="text-black text-sm font-bold">
              The Skyline Aviation Club, <br />
              1st Floor, Shivam Apartment,<br />
              Diagonally Opp. Joggers Park,<br />
              Beside Simpoli Metro Station, Chikuwadi,<br />
              Borivali West, Mumbai - 400092.
            </p>
          </div>
        </div>

        {/* Address for md & below only */}
        <div className="md:hidden mt-6">
          <h3 className="text-lg font-bold mb-2 text-footerPrimaryText">Address</h3>
          <p className="text-black text-sm font-bold">
            The Skyline Aviation Club, <br />
            1st Floor, Shivam Apartment,<br />
            Diagonally Opp. Joggers Park,<br />
            Beside Simpoli Metro Station, Chikuwadi,<br />
            Borivali West, Mumbai - 400092.
          </p>
        </div>
      </div >

      {/* Bottom Copyright */}
      <div div className="mt-6 text-center" >
        <p className="text-footerBottomText text-xs font-medium">
          © {currentYear} The Skyline Aviation Club. All Rights Reserved.
        </p>
      </div >
    </footer >
  );
};

export default Footer;
