"use client";
import React, { useState } from "react";
import Image from "next/image";
import { ArrowRight, ChevronDown, ChevronUp, Menu, X } from "lucide-react";
import Link from "next/link";
import { mainLinks, coursesLinks } from "@/constant/navbarLinks";
import { courseData } from "@/constant/courseDetailData";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Navbar = () => {
  const [toggleMobileMenu, setToggleMobileMenu] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleToggleMobileMenu = () => {
    setToggleMobileMenu((prev) => !prev);
    setDropdownOpen(false); // Close dropdown when toggling menu
  };

  const toggleDropdown = () => setDropdownOpen((prev) => !prev);

  const closeMobileMenu = () => {
    setToggleMobileMenu(false);
    setDropdownOpen(false);
  };

  return (
    <div className="absolute left-0 right-0 top-8 md:w-4/5 mx-4 md:mx-auto lg:mx-auto z-20">
      <nav className="bg-transparent bg-navbarBGPrimary rounded-full backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary px-3 py-2.5 flex items-center justify-between">
        <div className="flex items-center justify-between w-full">
          {/* Logo */}
          <Link href={"/"}>
            <Image
              src="/skyline_logo.png"
              alt="The Skyline Aviation Club Logo"
              width={50}
              height={50}
              className="rounded-full"
            />
          </Link>

          {/* Desktop Menu */}
          <div className="hidden lg:flex items-center md:gap-3 lg:gap-5 xl:gap-16">
            {mainLinks.map((item, index) =>
              item.text === "Courses" ? (
                <DropdownMenu
                  onOpenChange={(open) => setDropdownOpen(open)}
                  key={index}
                >
                  <DropdownMenuTrigger className="text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2">
                    {item.text}
                    <ChevronDown
                      className={`w-5 h-5 transition-transform ${
                        dropdownOpen ? "rotate-180" : "rotate-0"
                      }`}
                    />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-navbarBGPrimary backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary flex flex-col w-42 gap-3 p-4 rounded-xl">
                    {coursesLinks.map((course, index) => (
                      <DropdownMenuItem
                        asChild
                        key={index}
                        className="p-0 focus:bg-transparent hover:text-textBluePrimary"
                      >
                        <Link
                          className="text-customBlack font-semibold hover:text-textBluePrimary transition block"
                          href={`/course/${course.link}`}
                        >
                          {course.text}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link
                  key={index}
                  href={item.link}
                  className="text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2"
                >
                  {item.text}
                </Link>
              )
            )}
          </div>

          {/* Enquire Now Button (Desktop) */}
          <Link
            href="/enquire"
            className="hidden lg:flex items-center gap-2 bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 md:py-2 md:pr-2 md:pl-6 rounded-full border border-transparent"
          >
            Enquire Now
            <ArrowRight
              size={18}
              className="ml-2 w-8 h-8 p-1 bg-white rounded-full text-arrowIconColor"
            />
          </Link>

          {/* Mobile Menu Button */}
          <div className="lg:hidden flex">
            <button onClick={handleToggleMobileMenu} type="button">
              {toggleMobileMenu ? <X size={28} /> : <Menu size={28} />}
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div
        className={`w-full flex flex-col absolute right-0 top-[4.5rem] bg-navbarBGPrimary border-navbarBGPrimary shadow-dropdownShadow backdrop-blur-[20px] rounded-lg transition-all duration-300 ease-in-out overflow-hidden transform ${
          toggleMobileMenu
            ? "scale-100 opacity-100 max-h-[600px]"
            : "scale-95 opacity-0 max-h-0 pointer-events-none"
        }`}
      >
        {mainLinks.map((item, index) =>
          item.text === "Courses" ? (
            <div className="relative" key={index}>
              <button
                onClick={toggleDropdown}
                className="p-3 text-customBlack font-semibold w-full flex items-center justify-between"
              >
                Courses
                {dropdownOpen ? <ChevronUp /> : <ChevronDown />}
              </button>
              <div
                className={`transition-all duration-300 ease-in-out overflow-hidden ${
                  dropdownOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                }`}
              >
                <ul className="ml-4">
                  {courseData.map((course, index) => (
                    <li key={index}>
                      <Link
                        href={`/course/${course.slug}`}
                        className="block text-customBlack font-semibold hover:text-textBluePrimary transition p-2"
                        onClick={closeMobileMenu}
                      >
                        {course.courseDesc}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ) : (
            <Link
              key={index}
              href={item.link}
              className="p-3 text-customBlack font-semibold hover:text-textBluePrimary transition"
              onClick={closeMobileMenu}
            >
              {item.text}
            </Link>
          )
        )}
      </div>
    </div>
  );
};

export default Navbar;
