{"name": "strapi-cms", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.13.0", "@strapi/plugin-users-permissions": "5.13.0", "@strapi/provider-upload-cloudinary": "^5.13.0", "@strapi/strapi": "5.13.0", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-cms": "file:", "styled-components": "^6.0.0"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "e895da565d63e279cd10bdf4266ab1c41c61693d515f5a9e429e8db62ae1bd78"}}