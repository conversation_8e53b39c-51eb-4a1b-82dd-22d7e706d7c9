import Image from "next/image";
import BannerCompanySection from "./BannerCompanySection";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ArrowRight } from "lucide-react";
import { aboutUs, tableData } from "@/constant/landingPageData";
import Link from "next/link";

const WhyChooseContainer = () => {
  return (
    <div className="p-4 md:p-8 lg:py-12 bg-whiteOne relative z-0">
      <div className="z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-[700px] md:bottom-[300px] lg:bottom-[300px] left-0 right-0 w-full h-[500px]"></div>
      <div className="z-[-1] lg:bg-dotsImage bg-no-repeat bg-cover bg-center absolute bottom-[500px] right-0 w-10 h-36"></div>
      <BannerCompanySection />
      <div className="flex flex-col md:flex-row items-center justify-center text-center mt-28 sm:mt-44 lg:mt-12 mb-10">
        <Image
          src="/skyline_logo.png"
          alt="Skyline Aviation Club Logo"
          width={150}
          height={150}
          className="w-24 h-24 md:w-36 md:h-36 lg:w-48 lg:h-48 object-contain mb-4"
        />
        <div className="flex flex-col justify-center">
          <h3 className="text-footerPrimaryText mb-2 text-xl md:text-2xl lg:text-3xl font-bold text-center">
            Why Choose The Skyline Aviation Club?
          </h3>
          <p className="text-customBlack font-light text-base md:text-2xl lg:text-3xl text-center">
            The Clear Choice for Future Pilots !
          </p>
        </div>
      </div>
      <div className="flex flex-col gap-4 mb-8 md:mb-20 lg:mx-20">
        {aboutUs.map((item, index) => (
          <div
            className={`flex flex-col md:flex-row items-center gap-3 md:gap-8 ${index % 2 !== 0 ? "md:flex-row-reverse" : ""
              }`}
            key={index}
          >
            <Image src={item.image} alt="aboutImage" width={item.width} height={item.height} className="rounded-lg w-[356px] h-[139px]" />
            <div className="flex gap-1 lg:gap-3 flex-col">
              <h3 className="font-bold text-base md:text-xl text-textBluePrimary">
                {item.title}
              </h3>
              <p className="font-medium text-sm lg:text-base text-customBlack">
                {item.desc}
              </p>
            </div>
          </div>
        ))}
      </div>
      <div className="lg:mx-20">
        <div className="flex gap-4 flex-col md:flex-row">
          <div className="min-h-[470px] bg-white py-6 px-2 md:px-4 rounded-2xl shadow-courseCardShadow flex-1 relative">
            <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-7 md:mb-4">
              Our Yearly Training Programs Time Table
            </h3>
            <div className="lg:px-6">
              <Table>
                <TableHeader>
                  <TableRow className="border-bottom border-footerBottomText">
                    <TableHead className="font-bold text-descLightBlack text-sm lg:text-base">
                      Batch Starts on
                    </TableHead>
                    <TableHead className="font-bold text-descLightBlack text-sm lg:text-base">
                      Last Date for Admission
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tableData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="text-xs lg:text-sm text-black font-medium">
                        {item.startDate}
                      </TableCell>
                      <TableCell className="text-xs lg:text-sm text-black font-medium">
                        {item.endDate}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className="mt-3 text-center flex justify-center">
              <Link
                href="/enquire"
                className="text-white text-sm md:text-base py-2 pr-2 pl-3 md:pl-4 w-[150px] md:w-[180px] justify-between font-bold rounded-full border border-transparent bg-buttonBGPrimary flex items-center gap-2"
              >
                Enquire Now
                <ArrowRight className="w-7 h-7 md:w-9 md:h-9 p-1 bg-white rounded-full text-arrowIconColor" />
              </Link>
            </div>
          </div>
          <div className="min-h-[470px] bg-white p-6 rounded-2xl shadow-courseCardShadow flex-1 relative">
            <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-3 md:mb-4">
              Free Aviation/Airline Career Counselling
            </h3>
            <p className="font-medium text-sm">
              Unsure where to start your aviation career? Our expert career
              counseling services are designed to guide you every step of the
              way. Whether you are aiming to become a Commercial Pilot, Flight
              Dispatcher, Air Hostess, Flight Purser, Aeronautical Radio
              Officer, Airport Ground Staff or aviation management professional,
              we help you identify the right path based on your goals,
              interests, and qualifications. With 38 Years of experience in
              aviation training, we offer personalized advice on courses,
              licences and certifications, and career opportunities. Let us help
              you take off toward a high-flying career with clarity and
              confidence !
            </p>
            <div className="md:absolute bottom-[20px] left-0 right-0 mt-3 md:mt-8 text-center flex justify-center">
              <Link
                href="/enquire"
                className="text-white text-sm md:text-base py-2 pr-2 pl-3 md:pl-4 w-[150px] md:w-[180px] justify-between font-bold rounded-full border border-transparent bg-buttonBGPrimary flex items-center gap-2"
              >
                Enquire Now
                <ArrowRight className="w-7 h-7 md:w-9 md:h-9 p-1 bg-white rounded-full text-arrowIconColor" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhyChooseContainer;
