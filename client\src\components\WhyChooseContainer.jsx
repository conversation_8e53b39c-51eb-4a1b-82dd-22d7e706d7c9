
import Image from "next/image";
import BannerCompanySection from "./BannerCompanySection";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ArrowRight } from "lucide-react";
import { aboutUs, tableData } from "@/constant/landingPageData";
import Link from "next/link";
import { useYear } from "@/hooks/useYear";

const WhyChooseContainer = () => {
  const age = useYear("1987-10-15");

  return (
    <div className="p-4 md:p-8 lg:py-12 bg-whiteOne relative z-0">
      <div className="z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-[700px] md:bottom-[300px] lg:bottom-[300px] left-0 right-0 w-full h-[500px]"></div>
      <div className="z-[-1] lg:bg-dotsImage bg-no-repeat bg-cover bg-center absolute bottom-[500px] right-0 w-10 h-36"></div>
      <BannerCompanySection />
      <div className="flex flex-col md:flex-row items-center justify-center text-center mt-28 sm:mt-44 lg:mt-12 mb-5">
        <Image
          src="/skyline_logo.png"
          alt="Skyline Aviation Club Logo"
          width={150}
          height={150}
          className="w-24 h-24 md:w-36 md:h-36 lg:w-48 lg:h-48 object-contain"
        />
        <div className="flex flex-col justify-center">
          <h3 className="text-footerPrimaryText mb-2 text-xl md:text-2xl lg:text-3xl font-bold text-center">
            Why Choose The Skyline Aviation Club?
          </h3>
          <p className="text-customBlack font-light text-base md:text-2xl lg:text-3xl text-center">
            The Clear Choice for Future Pilots !
          </p>
        </div>
      </div>
      <div className="flex flex-col gap-4 mb-8 md:mb-20 lg:mx-20">
        {aboutUs.map((item, index) => (
          <div
            className={`flex flex-col md:flex-row items-center gap-3 md:gap-8 ${index % 2 !== 0 ? "md:flex-row-reverse" : ""
              }`}
            key={index}
          >
            <Image src={item.image} alt="aboutImage" width={1000} height={1000} className="rounded-lg w-[356px] h-[300px] md:w-[456px] md:h-[239px] " />
            <div className="flex gap-1 lg:gap-3 flex-col">
              <h3 className="font-bold text-base md:text-xl text-textBluePrimary">
                {item.title}
              </h3>
              <p className="font-medium text-sm lg:text-base text-customBlack">
                {item.desc}
              </p>
            </div>
          </div>
        ))}
      </div>
      <div className="lg:mx-12">
        <div className="flex gap-4 flex-col tablet:flex-col md:flex-row">
          <div className="min-h-[475px] flex flex-col justify-between bg-white py-6 px-2 md:px-4 rounded-2xl shadow-courseCardShadow flex-1 relative">
            <div className="lg:px-6">
              <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-7 md:mb-4">
                Our Yearly Training Programs Time Table
              </h3>
              <Table>
                <TableHeader>
                  <TableRow className="border-bottom border-footerBottomText">
                    <TableHead className="font-bold text-descLightBlack text-sm lg:text-base">
                      Batch Starts on
                    </TableHead>
                    <TableHead className="font-bold text-descLightBlack text-sm lg:text-base">
                      Last Date for Admission
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tableData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="text-xs lg:text-sm text-black font-medium">
                        {item.endDate}
                      </TableCell>
                      <TableCell className="text-xs lg:text-sm text-black font-medium">
                        {item.startDate}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className="text-center flex justify-center mt-5">
              <Link
                href="/enquire"
                className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}
              >
                <span className="font-bold">Enquire Now</span>
                <ArrowRight className="w-6 h-6 bg-white text-arrowIconColor rounded-full p-1" />
              </Link>
            </div>
          </div>
          <div className="min-h-[475px] bg-white p-6 rounded-2xl flex flex-col justify-between shadow-courseCardShadow flex-1 relative">
            <div>

              <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-3 md:mb-4">
                Free Aviation/Airline Career Counselling
              </h3>
              <p className="font-medium text-sm">
                Unsure where to start your aviation career? Our expert career
                counseling services are designed to guide you every step of the
                way. Whether you are aiming to become a Commercial Pilot, Flight
                Dispatcher, Air Hostess, Flight Purser, Aeronautical Radio
                Officer, Airport Ground Staff or aviation management professional,
                we help you identify the right path based on your goals,
                interests, and qualifications. With {age} Years of experience in
                aviation training, we offer personalized advice on courses,
                licences and certifications, and career opportunities. Let us help
                you take off toward a high-flying career with clarity and
                confidence !
              </p>
            </div>
            <div className="text-center flex justify-center">
              <Link
                href="/enquire"
                className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}
              >
                <span className="font-bold">Enquire Now</span>
                <ArrowRight className="w-6 h-6 bg-white text-arrowIconColor rounded-full p-1" />
              </Link>
            </div>
          </div>
          <div className="min-h-[475px] flex flex-col justify-between bg-white p-6 rounded-2xl shadow-courseCardShadow flex-1 relative">
            <div>
              <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary text-center mb-3 md:mb-4">
                World Book Of Records London
              </h3>
              <Image src="/londonAward.jpg" alt="whyChooseOne" width={456} height={239} className="w-full h-auto object-contain mb-4 rounded-md" />
              <p className="font-medium text-sm">
                The Skyline Aviation Club has been included for training over 5000 trainees of Commercial Pilot, Flight Dispatcher, Cabin Crew & Airport Ground Staff in the Last {age} years.
              </p>
            </div>
            <div className="text-center flex justify-center ">
              <Link
                href="/enquire"
                className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}
              >
                <span className="font-bold">Enquire Now</span>
                <ArrowRight className="w-6 h-6 bg-white text-arrowIconColor rounded-full p-1" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhyChooseContainer;
