(()=>{var e={};e.id=788,e.ids=[788],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},2235:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>x,routeModule:()=>c,tree:()=>m});var s=i(260),r=i(8203),l=i(5155),a=i.n(l),o=i(7292),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);i.d(t,n);let m=["",{children:["enquire",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,170)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\enquire\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,1485,23)),"next/dist/client/components/unauthorized-error"]}],x=["C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\enquire\\page.jsx"],d={require:i,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/enquire/page",pathname:"/enquire",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},7731:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,9607,23)),Promise.resolve().then(i.bind(i,5068))},4683:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,8531,23)),Promise.resolve().then(i.bind(i,7868))},3238:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});var s=i(6301);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=(...e)=>e.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:o="",children:n,iconNode:m,...x},d)=>(0,s.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:r?24*Number(i)/Number(t):i,className:l("lucide",o),...x},[...m.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),n=(e,t)=>{let i=(0,s.forwardRef)(({className:i,...a},n)=>(0,s.createElement)(o,{ref:n,iconNode:t,className:l(`lucide-${r(e)}`,i),...a}));return i.displayName=`${e}`,i}},170:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c});var s=i(2740),r=i(3238);let l=(0,r.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),a=(0,r.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),o=(0,r.A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var n=i(5068),m=i(9607),x=i.n(m);let d=()=>(0,s.jsxs)("div",{className:"bg-gray-50 min-h-screen",children:[(0,s.jsx)("div",{className:"h-12 w-full bg-[#080704] "}),(0,s.jsx)("div",{className:"flex items-end bg-enquireBannerTwo justify-center min-h-[100vh] md:min-h-[110vh] bg-cover bg-top",children:(0,s.jsxs)("div",{className:"relative z-10 text-center mb-12 p-4 rounded-md",children:[(0,s.jsx)("h1",{className:"text-2xl lg:text-7xl font-extrabold text-white tablet:text-5xl mobile:text-[42px]",children:"Enquire"}),(0,s.jsxs)("p",{className:"text-[21px] leading-[25px] mobile:font-semibold text-white mt-5 tablet:text-[18px] tablet:leading-[21px]    mobile:text-[14px] mobile:leading-[16px]",children:["Ready to embark on your aviation career? Fill out the form below, and our team will get in touch with you shortly to address your queries and help you take the first step towards your dream of flying high. ",(0,s.jsx)("br",{})," Let's make your aviation aspirations a reality !"]})]})}),(0,s.jsxs)("div",{className:"min-h-screen flex items-center justify-center relative bg-white",children:[(0,s.jsx)("div",{className:"absolute inset-0 z-0 opacity-40 mt-24",children:(0,s.jsx)("img",{src:"/background.svg",alt:"Background",className:"object-cover w-[1500px] h-[411px]",layout:"fill"})}),(0,s.jsxs)("div",{className:"flex flex-row tablet:flex-col w-full max-w-8xl relative z-10 justify-center mt-5    gap-8 tablet:mx-12 tablet:items-center mobile:flex-col mobile:mx-4",children:[(0,s.jsx)(n.default,{}),(0,s.jsxs)("div",{className:"flex flex-col tablet:flex-row w-full md:w-1/3 tablet:w-[100%] gap-7 mobile:gap-4 items-center tablet:items-stretch tablet:justify-center tablet:gap-8 tablet:mb-8",children:[(0,s.jsxs)("div",{className:"w-full tablet:w-1/2 bg-white shadow-md rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-textBluePrimary font-bold text-[32px] mobile:text-[22px]",children:"For Admissions"}),(0,s.jsxs)("div",{className:"mt-4 text-black font-medium text-[14px]",children:[(0,s.jsxs)("div",{className:"flex mt-4",children:[(0,s.jsx)(l,{className:"w-6 h-6 text-textBluePrimary mobile:w-4 mobile:h-4 mobile:mt-1"}),(0,s.jsxs)("div",{className:"flex flex-col ml-2 leading-[24.2px] font-medium mobile:text-[13px]",children:[(0,s.jsx)(x(),{href:"tel:+919820891262",className:"text-black text-sm font-medium",children:"+91 9820891262"}),(0,s.jsx)(x(),{href:"tel:+912228983516",className:"text-black text-sm font-medium",children:"+91 2228983516"})]})]}),(0,s.jsxs)("div",{className:"flex mt-3",children:[(0,s.jsx)(a,{className:"w-6 h-6 text-textBluePrimary mobile:w-4 mobile:h-4 mobile:mt-1"}),(0,s.jsx)("div",{className:"flex flex-col ml-2 leading-[24.2px] font-medium mobile:text-[13px]",children:(0,s.jsx)(x(),{href:"mailto:<EMAIL>",className:"text-black text-sm font-medium whitespace-nowrap",children:"<EMAIL>"})})]}),(0,s.jsxs)("div",{className:"flex mt-3",children:[(0,s.jsx)(o,{className:"w-6 h-6 text-textBluePrimary"}),(0,s.jsx)("div",{className:"flex flex-col ml-2 leading-[24.2px] font-medium mobile:text-[13px] mobile:leading-[18px]",children:(0,s.jsxs)("p",{children:["The Skyline Aviation Club, ",(0,s.jsx)("br",{}),"1st Floor, Shivam Apartment,",(0,s.jsx)("br",{}),"Diagonally Opp. Joggers Park,",(0,s.jsx)("br",{}),"Beside Simpoli Metro Station, Chikuwadi,",(0,s.jsx)("br",{}),"Borivali West, Mumbai - 400092."]})})]})]})]}),(0,s.jsxs)("div",{className:"w-full tablet:w-1/2 bg-white shadow-md rounded-lg p-[1.7rem] mb-8 tablet:mb-0",children:[(0,s.jsx)("h2",{className:"text-textBluePrimary font-bold text-[32px] mobile:text-[22px]",children:"Corporate Office"}),(0,s.jsxs)("div",{className:"mt-4 text-black font-medium text-[14px]",children:[(0,s.jsxs)("div",{className:"flex mt-4",children:[(0,s.jsx)(l,{className:"w-6 h-6 text-textBluePrimary mobile:w-4 mobile:h-4 mobile:mt-1"}),(0,s.jsx)("div",{className:"flex flex-col ml-2 leading-[24.2px] font-medium mobile:text-[13px]",children:(0,s.jsx)(x(),{href:"tel:+919820896262",className:"text-black text-sm font-medium",children:"+91 9820896262"})})]}),(0,s.jsxs)("div",{className:"flex mt-3",children:[(0,s.jsx)(a,{className:"w-6 h-6 text-textBluePrimary mobile:w-4 mobile:h-4 mobile:mt-1"}),(0,s.jsx)("div",{className:"flex flex-col ml-2 leading-[24.2px] font-medium mobile:text-[13px]",children:(0,s.jsx)(x(),{href:"mailto:<EMAIL>",className:"text-black text-sm font-medium whitespace-nowrap",children:"<EMAIL>"})})]}),(0,s.jsxs)("div",{className:"flex mt-3",children:[(0,s.jsx)(o,{className:"w-6 h-6 text-textBluePrimary"}),(0,s.jsx)("div",{className:"flex flex-col ml-2 leading-[24.2px] font-medium mobile:text-[13px] mobile:leading-[18px]",children:(0,s.jsxs)("p",{children:["The Skyline Aviation Club, ",(0,s.jsx)("br",{}),"1st Floor, Shivam Apartment,",(0,s.jsx)("br",{}),"Diagonally Opp. Joggers Park,",(0,s.jsx)("br",{}),"Beside Simpoli Metro Station, Chikuwadi,",(0,s.jsx)("br",{}),"Borivali West, Mumbai - 400092."]})})]})]})]})]})]})]})]}),c=()=>(0,s.jsx)(d,{})},5068:(e,t,i)=>{"use strict";i.d(t,{default:()=>s});let s=(0,i(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Skyline-Aviation\\\\client\\\\src\\\\components\\\\EnquireForm.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\EnquireForm.jsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[756,531,393,868],()=>i(2235));module.exports=s})();