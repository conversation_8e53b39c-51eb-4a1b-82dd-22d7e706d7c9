(()=>{var e={};e.id=634,e.ids=[634],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},1382:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(260),a=s(8203),n=s(5155),i=s.n(n),l=s(7292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["events",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2819)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\events\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,1485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\events\\page.jsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/events/page",pathname:"/events",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3427:(e,t,s)=>{Promise.resolve().then(s.bind(s,2819))},7499:(e,t,s)=>{Promise.resolve().then(s.bind(s,2215))},2215:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(5512),a=s(8009),n=s(4835);s(4160);let i=()=>{let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(async()=>{try{let e=await fetch("https://www.skylineaviation.training/api/events?populate[image][fields]=url"),s=(await e.json()).data.map(e=>({title:e.title,desc:e.description,image:"https://www.skylineaviation.training"+e.image?.url}));t(s)}catch(e){console.error("Failed to fetch events:",e)}finally{i(!1)}})()},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"relative bg-eventBanner bg-cover bg-center h-[700px]",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]"}),(0,r.jsxs)("div",{className:"absolute bottom-12 left-0 right-0",children:[(0,r.jsx)("h3",{className:"text-center text-2xl md:text-3xl lg:text-5xl font-bold text-white mb-2 lg:mb-4",children:"Events"}),(0,r.jsx)("p",{className:"text-center text-base lg:text-xl font-bold text-white",children:"See what's going on at The Skyline Aviation Club. Stay up to date with the latest news and updates !"})]})]}),(0,r.jsx)("div",{className:"p-6 lg:py-8 lg:px-20",children:s?(0,r.jsx)("p",{className:"text-center text-white",children:"Loading events..."}):(0,r.jsx)("div",{className:"grid grid-cols-1 justify-items-center md:grid-cols-4 xl:grid-cols-4 gap-4",children:(0,r.jsx)(n.A,{cardData:e,isLandingPage:!1})})})]})}},4835:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(5512),a=s(9576),n=s(5103),i=s(8009);let l=({cardData:e,isLandingPage:t})=>{let[s,l]=(0,i.useState)(null),o=e=>{l(t=>t===e?null:e)};return(0,r.jsx)(r.Fragment,{children:e.map((e,t)=>(0,r.jsx)(a.Zp,{className:"w-full h-full flex flex-col bg-descLightBlack",children:(0,r.jsxs)(a.Wu,{className:"flex flex-col p-0 rounded-md h-full",children:[(0,r.jsx)(n.default,{src:e.image,width:1900,height:1600,alt:"avatar",className:"w-full h-[300px] sm:h-[300px] md:h-[220px] rounded-t-xl object-cover",quality:100}),(0,r.jsxs)("div",{className:"bg-descLightBlack p-3 lg:p-6 rounded-b-lg flex flex-col flex-grow justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm md:text-base font-bold mb-1 text-white",children:e.title}),(0,r.jsx)("p",{className:"text-xs md:text-sm font-medium text-white break-words",children:s===t?e.desc:`${e.desc.slice(0,70)}...`})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)("button",{onClick:()=>o(t),className:"text-white text-xs md:text-sm px-2 py-1 bg-blue-900 rounded-md hover:bg-blue-800 transition-colors",children:s===t?"Show Less":"Show More"})})]})]})},t))})}},9576:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>l,Zp:()=>i});var r=s(5512),a=s(8009),n=s(6645);let i=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("rounded-xl bg-card",e),...t}));i.displayName="Card",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},4160:(e,t,s)=>{},2819:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Skyline-Aviation\\\\client\\\\src\\\\app\\\\events\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\events\\page.jsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[756,393],()=>s(1382));module.exports=r})();