"use strict";exports.id=531,exports.ids=[531],exports.modules={4355:(e,t,r)=>{r.d(t,{Ay:()=>S});class o{constructor(e=0,t="Network Error"){this.status=e,this.text=t}}let n={origin:"https://api.emailjs.com",blockHeadless:!1,storageProvider:(()=>{if("undefined"!=typeof localStorage)return{get:e=>Promise.resolve(localStorage.getItem(e)),set:(e,t)=>Promise.resolve(localStorage.setItem(e,t)),remove:e=>Promise.resolve(localStorage.removeItem(e))}})()},l=e=>e?"string"==typeof e?{publicKey:e}:"[object Object]"===e.toString()?e:{}:{},a=async(e,t,r={})=>{let l=await fetch(n.origin+e,{method:"POST",headers:r,body:t}),a=await l.text(),i=new o(l.status,a);if(l.ok)return i;throw i},i=(e,t,r)=>{if(!e||"string"!=typeof e)throw"The public key is required. Visit https://dashboard.emailjs.com/admin/account";if(!t||"string"!=typeof t)throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!r||"string"!=typeof r)throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates"},s=e=>{if(e&&"[object Object]"!==e.toString())throw"The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/"},d=e=>e.webdriver||!e.languages||0===e.languages.length,c=()=>new o(451,"Unavailable For Headless Browser"),u=(e,t)=>{if(!Array.isArray(e))throw"The BlockList list has to be an array";if("string"!=typeof t)throw"The BlockList watchVariable has to be a string"},p=e=>!e.list?.length||!e.watchVariable,h=(e,t)=>e instanceof FormData?e.get(t):e[t],f=(e,t)=>{if(p(e))return!1;u(e.list,e.watchVariable);let r=h(t,e.watchVariable);return"string"==typeof r&&e.list.includes(r)},m=()=>new o(403,"Forbidden"),v=(e,t)=>{if("number"!=typeof e||e<0)throw"The LimitRate throttle has to be a positive number";if(t&&"string"!=typeof t)throw"The LimitRate ID has to be a non-empty string"},g=async(e,t,r)=>{let o=Number(await r.get(e)||0);return t-Date.now()+o},w=async(e,t,r)=>{if(!t.throttle||!r)return!1;v(t.throttle,t.id);let o=t.id||e;return await g(o,t.throttle,r)>0||(await r.set(o,Date.now().toString()),!1)},y=()=>new o(429,"Too Many Requests"),b=e=>{if(!e||"FORM"!==e.nodeName)throw"The 3rd parameter is expected to be the HTML form element or the style selector of the form"},x=e=>"string"==typeof e?document.querySelector(e):e,S={init:(e,t="https://api.emailjs.com")=>{if(!e)return;let r=l(e);n.publicKey=r.publicKey,n.blockHeadless=r.blockHeadless,n.storageProvider=r.storageProvider,n.blockList=r.blockList,n.limitRate=r.limitRate,n.origin=r.origin||t},send:async(e,t,r,o)=>{let u=l(o),p=u.publicKey||n.publicKey,h=u.blockHeadless||n.blockHeadless,v=u.storageProvider||n.storageProvider,g={...n.blockList,...u.blockList},b={...n.limitRate,...u.limitRate};return h&&d(navigator)?Promise.reject(c()):(i(p,e,t),s(r),r&&f(g,r))?Promise.reject(m()):await w(location.pathname,b,v)?Promise.reject(y()):a("/api/v1.0/email/send",JSON.stringify({lib_version:"4.4.1",user_id:p,service_id:e,template_id:t,template_params:r}),{"Content-type":"application/json"})},sendForm:async(e,t,r,o)=>{let s=l(o),u=s.publicKey||n.publicKey,p=s.blockHeadless||n.blockHeadless,h=n.storageProvider||s.storageProvider,v={...n.blockList,...s.blockList},g={...n.limitRate,...s.limitRate};if(p&&d(navigator))return Promise.reject(c());let S=x(r);i(u,e,t),b(S);let j=new FormData(S);return f(v,j)?Promise.reject(m()):await w(location.pathname,g,h)?Promise.reject(y()):(j.append("lib_version","4.4.1"),j.append("service_id",e),j.append("template_id",t),j.append("user_id",u),a("/api/v1.0/email/send-form",j))},EmailJSResponseStatus:o}},7537:(e,t,r)=>{r.d(t,{UC:()=>eE,YJ:()=>eH,In:()=>eL,q7:()=>eA,VF:()=>eV,p4:()=>eB,JU:()=>e_,ZL:()=>eD,bL:()=>eR,wn:()=>eF,PP:()=>eK,wv:()=>eO,l9:()=>eI,WT:()=>eN,LM:()=>eM});var o=r(8009),n=r(5740);function l(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(1412),i=r(9217),s=r(9952),d=r(6004),c=r(9018),u=r(1675),p=r(9632),h=r(2534),f=r(96),m=r(4666),v=r(707),g=r(830),w=r(2705),y=r(2828),b=r(3024),x=r(9397),S=r(6441),j=r(2421),C=r(7783),k=r(5512),T=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],R="Select",[I,N,L]=(0,i.N)(R),[D,E]=(0,d.A)(R,[L,m.Bk]),M=(0,m.Bk)(),[H,_]=D(R),[A,B]=D(R),V=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:l,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:u,name:p,autoComplete:h,disabled:v,required:g,form:w}=e,y=M(t),[x,S]=o.useState(null),[j,C]=o.useState(null),[T,P]=o.useState(!1),R=(0,c.jH)(u),[N=!1,L]=(0,b.i)({prop:n,defaultProp:l,onChange:a}),[D,E]=(0,b.i)({prop:i,defaultProp:s,onChange:d}),_=o.useRef(null),B=!x||w||!!x.closest("form"),[V,K]=o.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,k.jsx)(m.bL,{...y,children:(0,k.jsxs)(H,{required:g,scope:t,trigger:x,onTriggerChange:S,valueNode:j,onValueNodeChange:C,valueNodeHasChildren:T,onValueNodeHasChildrenChange:P,contentId:(0,f.B)(),value:D,onValueChange:E,open:N,onOpenChange:L,dir:R,triggerPointerDownPosRef:_,disabled:v,children:[(0,k.jsx)(I.Provider,{scope:t,children:(0,k.jsx)(A,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{K(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{K(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),B?(0,k.jsxs)(ek,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:h,value:D,onChange:e=>E(e.target.value),disabled:v,form:w,children:[void 0===D?(0,k.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};V.displayName=R;var K="SelectTrigger",F=o.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...l}=e,i=M(r),d=_(K,r),c=d.disabled||n,u=(0,s.s)(t,d.onTriggerChange),p=N(r),h=o.useRef("touch"),[f,v,w]=eT(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),o=eP(t,e,r);void 0!==o&&d.onValueChange(o.value)}),y=e=>{c||(d.onOpenChange(!0),w()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,k.jsx)(m.Mz,{asChild:!0,...i,children:(0,k.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eC(d.value)?"":void 0,...l,ref:u,onClick:(0,a.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&y(e)}),onPointerDown:(0,a.m)(l.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(l.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(y(),e.preventDefault())})})})});F.displayName=K;var O="SelectValue",G=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:n,children:l,placeholder:a="",...i}=e,d=_(O,r),{onValueNodeHasChildrenChange:c}=d,u=void 0!==l,p=(0,s.s)(t,d.onValueNodeChange);return(0,x.N)(()=>{c(u)},[c,u]),(0,k.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eC(d.value)?(0,k.jsx)(k.Fragment,{children:a}):l})});G.displayName=O;var U=o.forwardRef((e,t)=>{let{__scopeSelect:r,children:o,...n}=e;return(0,k.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t,children:o||"▼"})});U.displayName="SelectIcon";var q=e=>(0,k.jsx)(v.Z,{asChild:!0,...e});q.displayName="SelectPortal";var W="SelectContent",z=o.forwardRef((e,t)=>{let r=_(W,e.__scopeSelect),[l,a]=o.useState();return((0,x.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,k.jsx)(Y,{...e,ref:t}):l?n.createPortal((0,k.jsx)(J,{scope:e.__scopeSelect,children:(0,k.jsx)(I.Slot,{scope:e.__scopeSelect,children:(0,k.jsx)("div",{children:e.children})})}),l):null});z.displayName=W;var[J,X]=D(W),Y=o.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:i,onPointerDownOutside:d,side:c,sideOffset:f,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:S,avoidCollisions:T,...P}=e,R=_(W,r),[I,L]=o.useState(null),[D,E]=o.useState(null),M=(0,s.s)(t,e=>L(e)),[H,A]=o.useState(null),[B,V]=o.useState(null),K=N(r),[F,O]=o.useState(!1),G=o.useRef(!1);o.useEffect(()=>{if(I)return(0,j.Eq)(I)},[I]),(0,p.Oh)();let U=o.useCallback(e=>{let[t,...r]=K().map(e=>e.ref.current),[o]=r.slice(-1),n=document.activeElement;for(let r of e)if(r===n||(r?.scrollIntoView({block:"nearest"}),r===t&&D&&(D.scrollTop=0),r===o&&D&&(D.scrollTop=D.scrollHeight),r?.focus(),document.activeElement!==n))return},[K,D]),q=o.useCallback(()=>U([H,I]),[U,H,I]);o.useEffect(()=>{F&&q()},[F,q]);let{onOpenChange:z,triggerPointerDownPosRef:X}=R;o.useEffect(()=>{if(I){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(X.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(X.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():I.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),X.current=null};return null!==X.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[I,z,X]),o.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[Y,$]=eT(e=>{let t=K().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),o=eP(t,e,r);o&&setTimeout(()=>o.ref.current.focus())}),ee=o.useCallback((e,t,r)=>{let o=!G.current&&!r;(void 0!==R.value&&R.value===t||o)&&(A(e),o&&(G.current=!0))},[R.value]),et=o.useCallback(()=>I?.focus(),[I]),er=o.useCallback((e,t,r)=>{let o=!G.current&&!r;(void 0!==R.value&&R.value===t||o)&&V(e)},[R.value]),eo="popper"===n?Q:Z,en=eo===Q?{side:c,sideOffset:f,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:S,avoidCollisions:T}:{};return(0,k.jsx)(J,{scope:r,content:I,viewport:D,onViewportChange:E,itemRefCallback:ee,selectedItem:H,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:q,selectedItemText:B,position:n,isPositioned:F,searchRef:Y,children:(0,k.jsx)(C.A,{as:w.DX,allowPinchZoom:!0,children:(0,k.jsx)(h.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(l,e=>{R.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,k.jsx)(u.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,k.jsx)(eo,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...P,...en,onPlaced:()=>O(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...P.style},onKeyDown:(0,a.m)(P.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=K().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,o=t.indexOf(r);t=t.slice(o+1)}setTimeout(()=>U(t)),e.preventDefault()}})})})})})})});Y.displayName="SelectContentImpl";var Z=o.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...a}=e,i=_(W,r),d=X(W,r),[c,u]=o.useState(null),[p,h]=o.useState(null),f=(0,s.s)(t,e=>h(e)),m=N(r),v=o.useRef(!1),w=o.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:S,focusSelectedItem:j}=d,C=o.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&p&&y&&b&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),o=S.getBoundingClientRect();if("rtl"!==i.dir){let n=o.left-t.left,a=r.left-n,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),u=l(a,[10,Math.max(10,window.innerWidth-10-d)]);c.style.minWidth=s+"px",c.style.left=u+"px"}else{let n=t.right-o.right,a=window.innerWidth-r.right-n,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),u=l(a,[10,Math.max(10,window.innerWidth-10-d)]);c.style.minWidth=s+"px",c.style.right=u+"px"}let a=m(),s=window.innerHeight-20,d=y.scrollHeight,u=window.getComputedStyle(p),h=parseInt(u.borderTopWidth,10),f=parseInt(u.paddingTop,10),g=parseInt(u.borderBottomWidth,10),w=h+f+d+parseInt(u.paddingBottom,10)+g,x=Math.min(5*b.offsetHeight,w),j=window.getComputedStyle(y),C=parseInt(j.paddingTop,10),k=parseInt(j.paddingBottom,10),T=e.top+e.height/2-10,P=b.offsetHeight/2,R=h+f+(b.offsetTop+P);if(R<=T){let e=a.length>0&&b===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-T,P+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+g);c.style.height=R+t+"px"}else{let e=a.length>0&&b===a[0].ref.current;c.style.top="0px";let t=Math.max(T,h+y.offsetTop+(e?C:0)+P);c.style.height=t+(w-R)+"px",y.scrollTop=R-T+y.offsetTop}c.style.margin="10px 0",c.style.minHeight=x+"px",c.style.maxHeight=s+"px",n?.(),requestAnimationFrame(()=>v.current=!0)}},[m,i.trigger,i.valueNode,c,p,y,b,S,i.dir,n]);(0,x.N)(()=>C(),[C]);let[T,P]=o.useState();(0,x.N)(()=>{p&&P(window.getComputedStyle(p).zIndex)},[p]);let R=o.useCallback(e=>{e&&!0===w.current&&(C(),j?.(),w.current=!1)},[C,j]);return(0,k.jsx)($,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,k.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,k.jsx)(g.sG.div,{...a,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Z.displayName="SelectItemAlignedPosition";var Q=o.forwardRef((e,t)=>{let{__scopeSelect:r,align:o="start",collisionPadding:n=10,...l}=e,a=M(r);return(0,k.jsx)(m.UC,{...a,...l,ref:t,align:o,collisionPadding:n,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Q.displayName="SelectPopperPosition";var[$,ee]=D(W,{}),et="SelectViewport",er=o.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...l}=e,i=X(et,r),d=ee(et,r),c=(0,s.s)(t,i.onViewportChange),u=o.useRef(0);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,k.jsx)(I.Slot,{scope:r,children:(0,k.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,a.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:o}=d;if(o?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let o=window.innerHeight-20,n=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(n<o){let l=n+e,a=Math.min(o,l),i=l-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});er.displayName=et;var eo="SelectGroup",[en,el]=D(eo),ea=o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e,n=(0,f.B)();return(0,k.jsx)(en,{scope:r,id:n,children:(0,k.jsx)(g.sG.div,{role:"group","aria-labelledby":n,...o,ref:t})})});ea.displayName=eo;var ei="SelectLabel",es=o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e,n=el(ei,r);return(0,k.jsx)(g.sG.div,{id:n.id,...o,ref:t})});es.displayName=ei;var ed="SelectItem",[ec,eu]=D(ed),ep=o.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:l=!1,textValue:i,...d}=e,c=_(ed,r),u=X(ed,r),p=c.value===n,[h,m]=o.useState(i??""),[v,w]=o.useState(!1),y=(0,s.s)(t,e=>u.itemRefCallback?.(e,n,l)),b=(0,f.B)(),x=o.useRef("touch"),S=()=>{l||(c.onValueChange(n),c.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,k.jsx)(ec,{scope:r,value:n,disabled:l,textId:b,isSelected:p,onItemTextChange:o.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,k.jsx)(I.ItemSlot,{scope:r,value:n,disabled:l,textValue:h,children:(0,k.jsx)(g.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":v?"":void 0,"aria-selected":p&&v,"data-state":p?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>w(!0)),onBlur:(0,a.m)(d.onBlur,()=>w(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==x.current&&S()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===x.current&&S()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{x.current=e.pointerType,l?u.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{u.searchRef?.current!==""&&" "===e.key||(P.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ed;var eh="SelectItemText",ef=o.forwardRef((e,t)=>{let{__scopeSelect:r,className:l,style:a,...i}=e,d=_(eh,r),c=X(eh,r),u=eu(eh,r),p=B(eh,r),[h,f]=o.useState(null),m=(0,s.s)(t,e=>f(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),v=h?.textContent,w=o.useMemo(()=>(0,k.jsx)("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=p;return(0,x.N)(()=>(y(w),()=>b(w)),[y,b,w]),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(g.sG.span,{id:u.textId,...i,ref:m}),u.isSelected&&d.valueNode&&!d.valueNodeHasChildren?n.createPortal(i.children,d.valueNode):null]})});ef.displayName=eh;var em="SelectItemIndicator",ev=o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e;return eu(em,r).isSelected?(0,k.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t}):null});ev.displayName=em;var eg="SelectScrollUpButton",ew=o.forwardRef((e,t)=>{let r=X(eg,e.__scopeSelect),n=ee(eg,e.__scopeSelect),[l,a]=o.useState(!1),i=(0,s.s)(t,n.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,k.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eg;var ey="SelectScrollDownButton",eb=o.forwardRef((e,t)=>{let r=X(ey,e.__scopeSelect),n=ee(ey,e.__scopeSelect),[l,a]=o.useState(!1),i=(0,s.s)(t,n.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),l?(0,k.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ey;var ex=o.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...l}=e,i=X("SelectScrollButton",r),s=o.useRef(null),d=N(r),c=o.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return o.useEffect(()=>()=>c(),[c]),(0,x.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,k.jsx)(g.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,a.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,a.m)(l.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,a.m)(l.onPointerLeave,()=>{c()})})}),eS=o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e;return(0,k.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t})});eS.displayName="SelectSeparator";var ej="SelectArrow";function eC(e){return""===e||void 0===e}o.forwardRef((e,t)=>{let{__scopeSelect:r,...o}=e,n=M(r),l=_(ej,r),a=X(ej,r);return l.open&&"popper"===a.position?(0,k.jsx)(m.i3,{...n,...o,ref:t}):null}).displayName=ej;var ek=o.forwardRef((e,t)=>{let{value:r,...n}=e,l=o.useRef(null),a=(0,s.s)(t,l),i=function(e){let t=o.useRef({value:e,previous:e});return o.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return o.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let o=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(o)}},[i,r]),(0,k.jsx)(S.s,{asChild:!0,children:(0,k.jsx)("select",{...n,ref:a,defaultValue:r})})});function eT(e){let t=(0,y.c)(e),r=o.useRef(""),n=o.useRef(0),l=o.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),a=o.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,l,a]}function eP(e,t,r){var o;let n=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=(o=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(o+r)%e.length]));1===n.length&&(l=l.filter(e=>e!==r));let a=l.find(e=>e.textValue.toLowerCase().startsWith(n.toLowerCase()));return a!==r?a:void 0}ek.displayName="BubbleSelect";var eR=V,eI=F,eN=G,eL=U,eD=q,eE=z,eM=er,eH=ea,e_=es,eA=ep,eB=ef,eV=ev,eK=ew,eF=eb,eO=eS}};