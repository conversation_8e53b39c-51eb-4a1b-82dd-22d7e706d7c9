# Sky Aviation Project

This project consists of a Next.js frontend client and a Strapi CMS backend.

## Prerequisites

- Node.js (v18.x or higher recommended)
- npm or yarn
- PostgreSQL database

## Installation and Setup

### 1. Clone the repository

```bash
git clone <repository-url>
cd <project-folder>
```

### 2. Setting up Strapi CMS

```bash
# Navigate to the Strapi directory
cd server

# Install dependencies
npm install
# or
yarn install

# Create .env file
cp .env.example .env
```

Start the Strapi development server:

```bash
npm run develop
# or
yarn develop
```

Strapi will be available at: http://localhost:1337

### 3. Setting up the Next.js Frontend

```bash
# Navigate to the client directory
cd client

# Install dependencies
npm install
# or
yarn install
```

When installing shadcn components, if prompted, choose:
```
Use --legacy-peer-deps
```

Start the Next.js development server:

```bash
npm run dev
# or
yarn dev
```

The frontend will be available at: http://localhost:3000

## Building for Production

### Strapi CMS

```bash
cd strapi-cms
npm run build
# or
yarn build

# Start production server
npm run develop
# or
yarn develp
```

### Next.js Frontend

```bash
cd client
npm run build
# or
yarn build

# Start production server
npm run start
# or
yarn start
```

## Git Commit Conventions

When committing code, use the following prefixes:

- `Feat` – feature
- `Fix` – bug fixes
- `Docs` – changes to documentation
- `Style` – style or formatting change
- `Perf` – improves code performance
- `Test` – test a feature

Example:
```
git commit -m "Feat: add user authentication"
```