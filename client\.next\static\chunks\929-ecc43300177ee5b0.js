"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[929],{6967:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3565:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},6710:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},767:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(7401).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4471:(e,t,r)=>{r.d(t,{H_:()=>tc,UC:()=>tl,YJ:()=>tu,q7:()=>td,VF:()=>tv,JU:()=>ts,ZL:()=>ti,z6:()=>tp,hN:()=>tf,bL:()=>to,wv:()=>tm,Pb:()=>tw,G5:()=>tg,ZP:()=>th,l9:()=>ta});var n=r(2115),o=r(3610),a=r(8068),i=r(8166),l=r(1488),u=r(3360),s=r(9741),d=r(4256),c=r(9674),p=r(2292),f=r(196),v=r(7668),m=r(905),w=r(7323),h=r(7028),g=r(1524),y=r(5155),x="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},C="RovingFocusGroup",[E,R,T]=(0,s.N)(C),[j,M]=(0,i.A)(C,[T]),[D,N]=j(C),P=n.forwardRef((e,t)=>(0,y.jsx)(E.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,y.jsx)(E.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,y.jsx)(k,{...e,ref:t})})}));P.displayName=C;var k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:c,currentTabStopId:p,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:v,onEntryFocus:m,preventScrollOnEntryFocus:w=!1,...h}=e,C=n.useRef(null),E=(0,a.s)(t,C),T=(0,d.jH)(c),[j=null,M]=(0,l.i)({prop:p,defaultProp:f,onChange:v}),[N,P]=n.useState(!1),k=(0,g.c)(m),I=R(r),A=n.useRef(!1),[_,F]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(x,k),()=>e.removeEventListener(x,k)},[k]),(0,y.jsx)(D,{scope:r,orientation:i,dir:T,loop:s,currentTabStopId:j,onItemFocus:n.useCallback(e=>M(e),[M]),onItemShiftTab:n.useCallback(()=>P(!0),[]),onFocusableItemAdd:n.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>F(e=>e-1),[]),children:(0,y.jsx)(u.sG.div,{tabIndex:N||0===_?-1:0,"data-orientation":i,...h,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{A.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!A.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(x,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);L([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),w)}}A.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>P(!1))})})}),I="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,...s}=e,d=(0,v.B)(),c=l||d,p=N(I,r),f=p.currentTabStopId===c,m=R(r),{onFocusableItemAdd:w,onFocusableItemRemove:h}=p;return n.useEffect(()=>{if(a)return w(),()=>h()},[a,w,h]),(0,y.jsx)(E.ItemSlot,{scope:r,id:c,focusable:a,active:i,children:(0,y.jsx)(u.sG.span,{tabIndex:f?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?p.onItemFocus(c):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(c)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>L(r))}})})})});A.displayName=I;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function L(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var F=r(2317),S=r(5587),O=r(4065),K=["Enter"," "],G=["ArrowUp","PageDown","End"],U=["ArrowDown","PageUp","Home",...G],V={ltr:[...K,"ArrowRight"],rtl:[...K,"ArrowLeft"]},B={ltr:["ArrowLeft"],rtl:["ArrowRight"]},X="Menu",[W,q,H]=(0,s.N)(X),[Y,z]=(0,i.A)(X,[H,m.Bk,M]),Z=(0,m.Bk)(),J=M(),[$,Q]=Y(X),[ee,et]=Y(X),er=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:l=!0}=e,u=Z(t),[s,c]=n.useState(null),p=n.useRef(!1),f=(0,g.c)(i),v=(0,d.jH)(a);return n.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,y.jsx)(m.bL,{...u,children:(0,y.jsx)($,{scope:t,open:r,onOpenChange:f,content:s,onContentChange:c,children:(0,y.jsx)(ee,{scope:t,onClose:n.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:l,children:o})})})};er.displayName=X;var en=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=Z(r);return(0,y.jsx)(m.Mz,{...o,...n,ref:t})});en.displayName="MenuAnchor";var eo="MenuPortal",[ea,ei]=Y(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=Q(eo,t);return(0,y.jsx)(ea,{scope:t,forceMount:r,children:(0,y.jsx)(h.C,{present:r||a.open,children:(0,y.jsx)(w.Z,{asChild:!0,container:o,children:n})})})};el.displayName=eo;var eu="MenuContent",[es,ed]=Y(eu),ec=n.forwardRef((e,t)=>{let r=ei(eu,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=Q(eu,e.__scopeMenu),i=et(eu,e.__scopeMenu);return(0,y.jsx)(W.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(h.C,{present:n||a.open,children:(0,y.jsx)(W.Slot,{scope:e.__scopeMenu,children:i.modal?(0,y.jsx)(ep,{...o,ref:t}):(0,y.jsx)(ef,{...o,ref:t})})})})}),ep=n.forwardRef((e,t)=>{let r=Q(eu,e.__scopeMenu),i=n.useRef(null),l=(0,a.s)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,S.Eq)(e)},[]),(0,y.jsx)(ev,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ef=n.forwardRef((e,t)=>{let r=Q(eu,e.__scopeMenu);return(0,y.jsx)(ev,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:v,onEscapeKeyDown:w,onPointerDownOutside:h,onFocusOutside:g,onInteractOutside:x,onDismiss:b,disableOutsideScroll:C,...E}=e,R=Q(eu,r),T=et(eu,r),j=Z(r),M=J(r),D=q(r),[N,k]=n.useState(null),I=n.useRef(null),A=(0,a.s)(t,I,R.onContentChange),_=n.useRef(0),L=n.useRef(""),S=n.useRef(0),K=n.useRef(null),V=n.useRef("right"),B=n.useRef(0),X=C?O.A:n.Fragment,W=C?{as:F.DX,allowPinchZoom:!0}:void 0,H=e=>{var t,r;let n=L.current+e,o=D().filter(e=>!e.disabled),a=document.activeElement,i=null===(t=o.find(e=>e.ref.current===a))||void 0===t?void 0:t.textValue,l=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}(o.map(e=>e.textValue),n,i),u=null===(r=o.find(e=>e.textValue===l))||void 0===r?void 0:r.ref.current;!function e(t){L.current=t,window.clearTimeout(_.current),""!==t&&(_.current=window.setTimeout(()=>e(""),1e3))}(n),u&&setTimeout(()=>u.focus())};n.useEffect(()=>()=>window.clearTimeout(_.current),[]),(0,p.Oh)();let Y=n.useCallback(e=>{var t,r;return V.current===(null===(t=K.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e].x,l=t[e].y,u=t[a].x,s=t[a].y;l>n!=s>n&&r<(u-i)*(n-l)/(s-l)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null===(r=K.current)||void 0===r?void 0:r.area)},[]);return(0,y.jsx)(es,{scope:r,searchRef:L,onItemEnter:n.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),onItemLeave:n.useCallback(e=>{var t;Y(e)||(null===(t=I.current)||void 0===t||t.focus(),k(null))},[Y]),onTriggerLeave:n.useCallback(e=>{Y(e)&&e.preventDefault()},[Y]),pointerGraceTimerRef:S,onPointerGraceIntentChange:n.useCallback(e=>{K.current=e},[]),children:(0,y.jsx)(X,{...W,children:(0,y.jsx)(f.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null===(t=I.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,y.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:w,onPointerDownOutside:h,onFocusOutside:g,onInteractOutside:x,onDismiss:b,children:(0,y.jsx)(P,{asChild:!0,...M,dir:T.dir,orientation:"vertical",loop:i,currentTabStopId:N,onCurrentTabStopIdChange:k,onEntryFocus:(0,o.m)(v,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,y.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eV(R.open),"data-radix-menu-content":"",dir:T.dir,...j,...E,ref:A,style:{outline:"none",...E.style},onKeyDown:(0,o.m)(E.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&H(e.key));let o=I.current;if(e.target!==o||!U.includes(e.key))return;e.preventDefault();let a=D().filter(e=>!e.disabled).map(e=>e.ref.current);G.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(_.current),L.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eW(e=>{let t=e.target,r=B.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>B.current?"right":"left";V.current=t,B.current=e.clientX}}))})})})})})})});ec.displayName=eu;var em=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{role:"group",...n,ref:t})});em.displayName="MenuGroup";var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{...n,ref:t})});ew.displayName="MenuLabel";var eh="MenuItem",eg="menu.itemSelect",ey=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...l}=e,s=n.useRef(null),d=et(eh,e.__scopeMenu),c=ed(eh,e.__scopeMenu),p=(0,a.s)(t,s),f=n.useRef(!1);return(0,y.jsx)(ex,{...l,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(eg,{bubbles:!0,cancelable:!0});e.addEventListener(eg,e=>null==i?void 0:i(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:t=>{var r;null===(r=e.onPointerDown)||void 0===r||r.call(e,t),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;f.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;!r&&(!t||" "!==e.key)&&K.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ey.displayName=eh;var ex=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:l,...s}=e,d=ed(eh,r),c=J(r),p=n.useRef(null),f=(0,a.s)(t,p),[v,m]=n.useState(!1),[w,h]=n.useState("");return n.useEffect(()=>{let e=p.current;if(e){var t;h((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[s.children]),(0,y.jsx)(W.ItemSlot,{scope:r,disabled:i,textValue:null!=l?l:w,children:(0,y.jsx)(A,{asChild:!0,...c,focusable:!i,children:(0,y.jsx)(u.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eW(e=>{i?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eW(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eb=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,y.jsx)(eN,{scope:e.__scopeMenu,checked:r,children:(0,y.jsx)(ey,{role:"menuitemcheckbox","aria-checked":eB(r)?"mixed":r,...a,ref:t,"data-state":eX(r),onSelect:(0,o.m)(a.onSelect,()=>null==n?void 0:n(!!eB(r)||!r),{checkForDefaultPrevented:!1})})})});eb.displayName="MenuCheckboxItem";var eC="MenuRadioGroup",[eE,eR]=Y(eC,{value:void 0,onValueChange:()=>{}}),eT=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,g.c)(n);return(0,y.jsx)(eE,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,y.jsx)(em,{...o,ref:t})})});eT.displayName=eC;var ej="MenuRadioItem",eM=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=eR(ej,e.__scopeMenu),i=r===a.value;return(0,y.jsx)(eN,{scope:e.__scopeMenu,checked:i,children:(0,y.jsx)(ey,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eX(i),onSelect:(0,o.m)(n.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});eM.displayName=ej;var eD="MenuItemIndicator",[eN,eP]=Y(eD,{checked:!1}),ek=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=eP(eD,r);return(0,y.jsx)(h.C,{present:n||eB(a.checked)||!0===a.checked,children:(0,y.jsx)(u.sG.span,{...o,ref:t,"data-state":eX(a.checked)})})});ek.displayName=eD;var eI=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,y.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eI.displayName="MenuSeparator";var eA=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=Z(r);return(0,y.jsx)(m.i3,{...o,...n,ref:t})});eA.displayName="MenuArrow";var e_="MenuSub",[eL,eF]=Y(e_),eS=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,i=Q(e_,t),l=Z(t),[u,s]=n.useState(null),[d,c]=n.useState(null),p=(0,g.c)(a);return n.useEffect(()=>(!1===i.open&&p(!1),()=>p(!1)),[i.open,p]),(0,y.jsx)(m.bL,{...l,children:(0,y.jsx)($,{scope:t,open:o,onOpenChange:p,content:d,onContentChange:c,children:(0,y.jsx)(eL,{scope:t,contentId:(0,v.B)(),triggerId:(0,v.B)(),trigger:u,onTriggerChange:s,children:r})})})};eS.displayName=e_;var eO="MenuSubTrigger",eK=n.forwardRef((e,t)=>{let r=Q(eO,e.__scopeMenu),i=et(eO,e.__scopeMenu),l=eF(eO,e.__scopeMenu),u=ed(eO,e.__scopeMenu),s=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=u,p={__scopeMenu:e.__scopeMenu},f=n.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return n.useEffect(()=>f,[f]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,y.jsx)(en,{asChild:!0,...p,children:(0,y.jsx)(ex,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eV(r.open),...e,ref:(0,a.t)(t,l.onTriggerChange),onClick:t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eW(t=>{u.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),f()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eW(e=>{var t,n;f();let o=null===(t=r.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(n=r.content)||void 0===n?void 0:n.dataset.side,a="right"===t,i=o[a?"left":"right"],l=o[a?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:i,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:i,y:o.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==u.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&V[i.dir].includes(t.key)){var o;r.onOpenChange(!0),null===(o=r.content)||void 0===o||o.focus(),t.preventDefault()}})})})});eK.displayName=eO;var eG="MenuSubContent",eU=n.forwardRef((e,t)=>{let r=ei(eu,e.__scopeMenu),{forceMount:i=r.forceMount,...l}=e,u=Q(eu,e.__scopeMenu),s=et(eu,e.__scopeMenu),d=eF(eG,e.__scopeMenu),c=n.useRef(null),p=(0,a.s)(t,c);return(0,y.jsx)(W.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(h.C,{present:i||u.open,children:(0,y.jsx)(W.Slot,{scope:e.__scopeMenu,children:(0,y.jsx)(ev,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null===(t=c.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=B[s.dir].includes(e.key);if(t&&r){var n;u.onOpenChange(!1),null===(n=d.trigger)||void 0===n||n.focus(),e.preventDefault()}})})})})})});function eV(e){return e?"open":"closed"}function eB(e){return"indeterminate"===e}function eX(e){return eB(e)?"indeterminate":e?"checked":"unchecked"}function eW(e){return t=>"mouse"===t.pointerType?e(t):void 0}eU.displayName=eG;var eq="DropdownMenu",[eH,eY]=(0,i.A)(eq,[z]),ez=z(),[eZ,eJ]=eH(eq),e$=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:u,modal:s=!0}=e,d=ez(t),c=n.useRef(null),[p=!1,f]=(0,l.i)({prop:a,defaultProp:i,onChange:u});return(0,y.jsx)(eZ,{scope:t,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,y.jsx)(er,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:r})})};e$.displayName=eq;var eQ="DropdownMenuTrigger",e0=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,l=eJ(eQ,r),s=ez(r);return(0,y.jsx)(en,{asChild:!0,...s,children:(0,y.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,a.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e0.displayName=eQ;var e1=e=>{let{__scopeDropdownMenu:t,...r}=e,n=ez(t);return(0,y.jsx)(el,{...n,...r})};e1.displayName="DropdownMenuPortal";var e6="DropdownMenuContent",e2=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=eJ(e6,r),l=ez(r),u=n.useRef(!1);return(0,y.jsx)(ec,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null===(t=i.triggerRef.current)||void 0===t||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e2.displayName=e6;var e5=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(em,{...o,...n,ref:t})});e5.displayName="DropdownMenuGroup";var e8=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(ew,{...o,...n,ref:t})});e8.displayName="DropdownMenuLabel";var e7=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(ey,{...o,...n,ref:t})});e7.displayName="DropdownMenuItem";var e3=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(eb,{...o,...n,ref:t})});e3.displayName="DropdownMenuCheckboxItem";var e4=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(eT,{...o,...n,ref:t})});e4.displayName="DropdownMenuRadioGroup";var e9=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(eM,{...o,...n,ref:t})});e9.displayName="DropdownMenuRadioItem";var te=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(ek,{...o,...n,ref:t})});te.displayName="DropdownMenuItemIndicator";var tt=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(eI,{...o,...n,ref:t})});tt.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(eA,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var tr=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(eK,{...o,...n,ref:t})});tr.displayName="DropdownMenuSubTrigger";var tn=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=ez(r);return(0,y.jsx)(eU,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tn.displayName="DropdownMenuSubContent";var to=e$,ta=e0,ti=e1,tl=e2,tu=e5,ts=e8,td=e7,tc=e3,tp=e4,tf=e9,tv=te,tm=tt,tw=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,i=ez(t),[u=!1,s]=(0,l.i)({prop:n,defaultProp:a,onChange:o});return(0,y.jsx)(eS,{...i,open:u,onOpenChange:s,children:r})},th=tr,tg=tn},7028:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(2115),o=r(8068),a=r(6611),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),u=n.useRef({}),s=n.useRef(e),d=n.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(u.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let t=u.current,r=s.current;if(r!==e){let n=d.current,o=l(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):r&&n!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,a.N)(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=l(u.current).includes(e.animationName);if(e.target===o&&n&&(p("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(u.current=getComputedStyle(e)),i(e)},[])}}(t),u="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),s=(0,o.s)(i.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof r||i.isPresent?n.cloneElement(u,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},9930:(e,t,r)=>{r.d(t,{Kq:()=>Z,LM:()=>J,VY:()=>ee,bL:()=>$,bm:()=>er,hE:()=>Q,rc:()=>et});var n=r(2115),o=r(7650),a=r(3610),i=r(8068),l=r(9741),u=r(8166),s=r(9674),d=r(7323),c=r(7028),p=r(3360),f=r(1524),v=r(1488),m=r(6611),w=r(3543),h=r(5155),g="ToastProvider",[y,x,b]=(0,l.N)("Toast"),[C,E]=(0,u.A)("Toast",[b]),[R,T]=C(g),j=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:l}=e,[u,s]=n.useState(null),[d,c]=n.useState(0),p=n.useRef(!1),f=n.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,h.jsx)(y.Provider,{scope:t,children:(0,h.jsx)(R,{scope:t,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:d,viewport:u,onViewportChange:s,onToastAdd:n.useCallback(()=>c(e=>e+1),[]),onToastRemove:n.useCallback(()=>c(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:f,children:l})})};j.displayName=g;var M="ToastViewport",D=["F8"],N="toast.viewportPause",P="toast.viewportResume",k=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=D,label:a="Notifications ({hotkey})",...l}=e,u=T(M,r),d=x(r),c=n.useRef(null),f=n.useRef(null),v=n.useRef(null),m=n.useRef(null),w=(0,i.s)(t,m,u.onViewportChange),g=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=u.toastCount>0;n.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=m.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=c.current,t=m.current;if(b&&e&&t){let r=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(N);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},n=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(P);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},a=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",a),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[b,u.isClosePausedRef]);let C=n.useCallback(e=>{let{tabbingDirection:t}=e,r=d().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[d]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,a;let r=document.activeElement,i=t.shiftKey;if(t.target===e&&i){null===(n=f.current)||void 0===n||n.focus();return}let l=C({tabbingDirection:i?"backwards":"forwards"}),u=l.findIndex(e=>e===r);z(l.slice(u+1))?t.preventDefault():i?null===(o=f.current)||void 0===o||o.focus():null===(a=v.current)||void 0===a||a.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[d,C]),(0,h.jsxs)(s.lg,{ref:c,role:"region","aria-label":a.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&(0,h.jsx)(A,{ref:f,onFocusFromOutsideViewport:()=>{z(C({tabbingDirection:"forwards"}))}}),(0,h.jsx)(y.Slot,{scope:r,children:(0,h.jsx)(p.sG.ol,{tabIndex:-1,...l,ref:w})}),b&&(0,h.jsx)(A,{ref:v,onFocusFromOutsideViewport:()=>{z(C({tabbingDirection:"backwards"}))}})]})});k.displayName=M;var I="ToastFocusProxy",A=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,a=T(I,r);return(0,h.jsx)(w.s,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=a.viewport)||void 0===t?void 0:t.contains(r))||n()}})});A.displayName=I;var _="Toast",L=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:i,...l}=e,[u=!0,s]=(0,v.i)({prop:n,defaultProp:o,onChange:i});return(0,h.jsx)(c.C,{present:r||u,children:(0,h.jsx)(O,{open:u,...l,ref:t,onClose:()=>s(!1),onPause:(0,f.c)(e.onPause),onResume:(0,f.c)(e.onResume),onSwipeStart:(0,a.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,a.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),s(!1)})})})});L.displayName=_;var[F,S]=C(_,{onClose(){}}),O=n.forwardRef((e,t)=>{let{__scopeToast:r,type:l="foreground",duration:u,open:d,onClose:c,onEscapeKeyDown:v,onPause:m,onResume:w,onSwipeStart:g,onSwipeMove:x,onSwipeCancel:b,onSwipeEnd:C,...E}=e,R=T(_,r),[j,M]=n.useState(null),D=(0,i.s)(t,e=>M(e)),k=n.useRef(null),I=n.useRef(null),A=u||R.duration,L=n.useRef(0),S=n.useRef(A),O=n.useRef(0),{onToastAdd:G,onToastRemove:U}=R,V=(0,f.c)(()=>{var e;(null==j?void 0:j.contains(document.activeElement))&&(null===(e=R.viewport)||void 0===e||e.focus()),c()}),B=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(O.current),L.current=new Date().getTime(),O.current=window.setTimeout(V,e))},[V]);n.useEffect(()=>{let e=R.viewport;if(e){let t=()=>{B(S.current),null==w||w()},r=()=>{let e=new Date().getTime()-L.current;S.current=S.current-e,window.clearTimeout(O.current),null==m||m()};return e.addEventListener(N,r),e.addEventListener(P,t),()=>{e.removeEventListener(N,r),e.removeEventListener(P,t)}}},[R.viewport,A,m,w,B]),n.useEffect(()=>{d&&!R.isClosePausedRef.current&&B(A)},[d,A,R.isClosePausedRef,B]),n.useEffect(()=>(G(),()=>U()),[G,U]);let X=n.useMemo(()=>j?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(j):null,[j]);return R.viewport?(0,h.jsxs)(h.Fragment,{children:[X&&(0,h.jsx)(K,{__scopeToast:r,role:"status","aria-live":"foreground"===l?"assertive":"polite","aria-atomic":!0,children:X}),(0,h.jsx)(F,{scope:r,onClose:V,children:o.createPortal((0,h.jsx)(y.ItemSlot,{scope:r,children:(0,h.jsx)(s.bL,{asChild:!0,onEscapeKeyDown:(0,a.m)(v,()=>{R.isFocusedToastEscapeKeyDownRef.current||V(),R.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,h.jsx)(p.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":R.swipeDirection,...E,ref:D,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Escape"!==e.key||(null==v||v(e.nativeEvent),e.nativeEvent.defaultPrevented||(R.isFocusedToastEscapeKeyDownRef.current=!0,V()))}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{0===e.button&&(k.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{if(!k.current)return;let t=e.clientX-k.current.x,r=e.clientY-k.current.y,n=!!I.current,o=["left","right"].includes(R.swipeDirection),a=["left","up"].includes(R.swipeDirection)?Math.min:Math.max,i=o?a(0,t):0,l=o?0:a(0,r),u="touch"===e.pointerType?10:2,s={x:i,y:l},d={originalEvent:e,delta:s};n?(I.current=s,H("toast.swipeMove",x,d,{discrete:!1})):Y(s,R.swipeDirection,u)?(I.current=s,H("toast.swipeStart",g,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(r)>u)&&(k.current=null)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=I.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),I.current=null,k.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Y(t,R.swipeDirection,R.swipeThreshold)?H("toast.swipeEnd",C,n,{discrete:!0}):H("toast.swipeCancel",b,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),R.viewport)})]}):null}),K=e=>{let{__scopeToast:t,children:r,...o}=e,a=T(_,t),[i,l]=n.useState(!1),[u,s]=n.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,f.c)(e);(0,m.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>l(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>s(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,h.jsx)(d.Z,{asChild:!0,children:(0,h.jsx)(w.s,{...o,children:i&&(0,h.jsxs)(h.Fragment,{children:[a.label," ",r]})})})},G=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,h.jsx)(p.sG.div,{...n,ref:t})});G.displayName="ToastTitle";var U=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,h.jsx)(p.sG.div,{...n,ref:t})});U.displayName="ToastDescription";var V="ToastAction",B=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,h.jsx)(q,{altText:r,asChild:!0,children:(0,h.jsx)(W,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(V,"`. Expected non-empty `string`.")),null)});B.displayName=V;var X="ToastClose",W=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=S(X,r);return(0,h.jsx)(q,{asChild:!0,children:(0,h.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,o.onClose)})})});W.displayName=X;var q=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,h.jsx)(p.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function H(e,t,r,n){let{discrete:o}=n,a=r.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),o?(0,p.hO)(a,i):a.dispatchEvent(i)}var Y=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),a=n>o;return"left"===t||"right"===t?a&&n>r:!a&&o>r};function z(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Z=j,J=k,$=L,Q=G,ee=U,et=B,er=W},1027:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(3463);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,u=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let a=o(t)||o(n);return i[e][a]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,u,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...s}[t]):({...l,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}}]);