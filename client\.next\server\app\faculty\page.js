(()=>{var e={};e.id=309,e.ids=[309],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var i=r(260),a=r(8203),n=r(5155),s=r.n(n),o=r(7292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["faculty",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7904)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\faculty\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,1485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\faculty\\page.jsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/faculty/page",pathname:"/faculty",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5476:(e,t,r)=>{Promise.resolve().then(r.bind(r,778))},5156:(e,t,r)=>{Promise.resolve().then(r.bind(r,1688))},1688:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var i=r(5512),a=r(8009),n=r(6422);let s=function(){let e=new Date("1987-10-15"),t=new Date,r=t.getFullYear()-e.getFullYear();return(t.getMonth()<e.getMonth()||t.getMonth()===e.getMonth()&&t.getDate()<e.getDate())&&r--,r}(),o=[{role:"Founder",name:"Capt.(Dr.) AD Manek",image:"assets/founder.svg",description:`Capt.(Dr.) AD Manek is founder of The Skyline Aviation Club. Started aviation  educational activities from 15th October, 1987. He is actively engaged in aviation training since last ${s} years. He initially completed his Private Pilot licence training in India and thereafter graduated as Pilot Instructor in California, USA. Till now he was able to train 5000 plus trainees for Airline Transport Pilot Licence (ATPL), Commercial Pilot Licence (CPL), Aircraft / Flight Dispatcher Licence (ADX), Radio Telephony ( R ) / FRTOL Licence, Airhostess, Flight Purser and Airport Ground Staff courses and is placed in WORLD OF RECORDS (London)`},{role:"Chief Pilot Instructor",name:"Capt. Ankur Manek",image:"/Ankur.svg",description:"Capt. Ankur Manek, Chief Pilot Instructor, is a highly accomplished aviation professional holding certifications from DGCA (India) and FAA (USA) as a Multi-Engine Instrument Commercial Pilot, Pilot Instructor (AGII), and Aircraft Dispatcher, as well as a CAA (UK) FRTOL Holder, with a B.Sc. in Aviation from the University of Mumbai, membership in the Aeronautical Society of India, and experience as a professional skydiver and trustee."},{role:"Instructor: Radio Telephony",name:"Mr. Shekhar Banerjee",image:"/Shekhar.svg",description:"An experienced aviation professional, Mr. Banerjee is an Instructor for Radio Telephony (R) Licence and ICAO Aviation English Proficiency courses, with over 33 years of experience in civil aviation and communication. A retired Assistant General Manager of Communication Operations at Mumbai Airport, he served as a Board Member for DGCA Aeromobile Exams and holds an ICAO Level-6 Aviation English Proficiency Certificate, backed by training in wireless telegraphy and telephony."},{role:"Instructor: English Language",name:"Mr. Mahesh M.Sheth",image:"/Mahesh.svg",description:"A seasoned educator with degrees in B.Sc. and L.L.B, Mr. Sheth is a retired professor from the Bank of India College, Mumbai. He now serves as an Instructor for English Language and Personality Development, bringing extensive expertise in professional training."},{role:"Instructor: Aviation Subjects",name:"Mr. Dhirubhai Par",image:"/Dhirubhai.svg",description:"With a B.A. in Economics and L.L.B., Mr. Par is a retired Air India Manager with 37 years of experience. He is now an Instructor for Aviation Subjects, leveraging his vast industry knowledge to train future aviation professionals."}];var l=r(5103);let d=()=>{let[e,t]=(0,a.useState)(!1),[r,s]=(0,a.useState)(0),[d,c]=(0,a.useState)();(0,a.useEffect)(()=>{let e=()=>{t(window.innerWidth<=480)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,a.useEffect)(()=>{d&&(s(d.selectedScrollSnap()+1),d.on("select",()=>{s(d.selectedScrollSnap()+1)}))},[d]);let u=e=>{d&&d.scrollTo(e)};return(0,i.jsxs)("div",{className:"bg-gray-50 min-h-screen relative pt-12",children:[(0,i.jsxs)("div",{className:"relative pt-8 tablet:py-12 mobile:pt-32 mobile:pb-8",children:[(0,i.jsx)("div",{className:"absolute inset-0",children:(0,i.jsx)(l.default,{src:"/Facultybg.svg",alt:"Faculty Background",className:"w-full !h-[90%] object-cover mobile:!h-[80%]",layout:"fill"})}),(0,i.jsxs)("div",{className:"relative z-10 max-w-[80%] tablet:max-w-[100%] mobile:max-w-[100%] mobile:mx-4 mx-auto text-center px-4 pb-16 pt-32 tablet:pt-16 tablet:pb-0 mobile:rounded-[12px] mobile:bg-white mobile:py-4 mobile:shadow-cardsBoxShadow",children:[(0,i.jsx)("h1",{className:"font-bold text-[47px] text-textBluePrimary mb-5 tablet:text-[43px] tablet:mt-8 mobile:text-[20px] mobile:text-semibold",children:"Meet Our Esteemed Faculty"}),(0,i.jsx)("p",{className:"text-lg leading-[24px] text-left font-semibold text-[#464C3B] mobile:text-[13px] mobile:leading-[17px] mobile:px-4",children:"At The Skyline Aviation Club, our faculty is a team of highly qualified and experienced professionals dedicated to nurturing the next generation of aviation leaders. Comprising seasoned pilot instructors, aviation instructors, retired airline managers, ATC Officers, and industry experts, they bring decades of hands-on expertise in fields such as pilot training covering all aviation subjects like Air Navigation, Aviation Meteorology, Air Regulations, Aircraft & Engine (General & Specific) and Radio Telephony (RTR) Licence and personality development. Each faculty member is not only an accomplished professional with impressive credentials but also a passionate mentor committed to delivering world-class training and guidance. Together, they form the backbone of our institution, ensuring our students receive unparalleled education and career support in their aviation journey."})]})]}),(0,i.jsx)("div",{className:"container mx-auto mobile:mx-0 mobile:max-w-[373px] px-4 pt-0 py-8 relative mobile:mx-auto",children:e?(0,i.jsxs)("div",{children:[(0,i.jsx)(n.FN,{setApi:c,opts:{loop:!0},children:(0,i.jsx)(n.Wk,{children:o.map((e,t)=>(0,i.jsx)(n.A7,{children:(0,i.jsxs)("div",{className:"flex flex-col gap-2 p-5 w-full max-w-[340px] bg-white shadow-md rounded-lg overflow-hidden items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)(l.default,{src:e.image,alt:e.name,width:100,height:100,className:"rounded-lg w-[19rem] h-64 object-cover"})}),(0,i.jsxs)("div",{className:"flex-1 text-left",children:[(0,i.jsx)("h2",{className:"text-[20px] text-textBluePrimary font-bold",children:e.role}),(0,i.jsx)("p",{className:"text-[16px] text-textBluePrimary font-medium leading-[35px]",children:e.name}),(0,i.jsx)("p",{className:"text-[15px] text-customGrey100 font-medium leading-[21px]",children:e.description})]})]})},t))})}),(0,i.jsx)("div",{className:"flex justify-center items-center mt-4 space-x-2",children:o.map((e,t)=>(0,i.jsx)("button",{onClick:()=>u(t),className:`${r===t+1?"h-3 w-8 rounded-full bg-textBluePrimary":"h-3 w-2 rounded-full bg-carouselDotsBG"}`},t))})]}):(0,i.jsx)("div",{className:"flex flex-col gap-6 items-center relative tablet:flex-row tablet:flex-wrap",children:o.map((e,t)=>(0,i.jsxs)("div",{className:`relative flex flex-col tablet:flex-row lg:flex-row gap-5 p-5 w-full max-w-[1000px] ${t%2==0?"tablet:flex-row lg:flex-row":"tablet:flex-row-reverse lg:flex-row-reverse"} bg-white shadow-md rounded-lg overflow-hidden items-center justify-center`,children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)(l.default,{src:e.image,alt:e.name,width:100,height:100,className:"rounded-lg w-56 h-64 object-cover"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h2",{className:"text-[28px] text-textBluePrimary font-bold mobile:text-[20px]",children:e.role}),(0,i.jsx)("p",{className:"text-[20px] text-textBluePrimary font-bold tablet:text-[20px] mobile:text-[18px]",children:e.name}),(0,i.jsx)("p",{className:"text-[15px] text-customGrey100 font-medium mobile:text-[14px] tablet:text-[15px]",children:e.description})]})]},t))})})]})}},6422:(e,t,r)=>{"use strict";r.d(t,{FN:()=>x,Wk:()=>h,A7:()=>f,Oj:()=>g,Q8:()=>v});var i=r(5512),a=r(8009),n=r(7203),s=r(2706),o=r(9905),l=r(6645),d=r(2705);let c=(0,r(1643).F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...n},s)=>{let o=a?d.DX:"button";return(0,i.jsx)(o,{className:(0,l.cn)(c({variant:t,size:r,className:e})),ref:s,...n})});u.displayName="Button";let p=a.createContext(null);function m(){let e=a.useContext(p);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let x=a.forwardRef(({orientation:e="horizontal",opts:t,setApi:r,plugins:s,className:o,children:d,...c},u)=>{let[m,x]=(0,n.A)({...t,axis:"horizontal"===e?"x":"y"},s),[h,f]=a.useState(!1),[v,g]=a.useState(!1),b=a.useCallback(e=>{e&&(f(e.canScrollPrev()),g(e.canScrollNext()))},[]),y=a.useCallback(()=>{x?.scrollPrev()},[x]),w=a.useCallback(()=>{x?.scrollNext()},[x]),j=a.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),y()):"ArrowRight"===e.key&&(e.preventDefault(),w())},[y,w]);return a.useEffect(()=>{x&&r&&r(x)},[x,r]),a.useEffect(()=>{if(x)return b(x),x.on("reInit",b),x.on("select",b),()=>{x?.off("select",b)}},[x,b]),(0,i.jsx)(p.Provider,{value:{carouselRef:m,api:x,opts:t,orientation:e||(t?.axis==="y"?"vertical":"horizontal"),scrollPrev:y,scrollNext:w,canScrollPrev:h,canScrollNext:v},children:(0,i.jsx)("div",{ref:u,onKeyDownCapture:j,className:(0,l.cn)("relative",o),role:"region","aria-roledescription":"carousel",...c,children:d})})});x.displayName="Carousel";let h=a.forwardRef(({className:e,...t},r)=>{let{carouselRef:a,orientation:n}=m();return(0,i.jsx)("div",{ref:a,className:"overflow-hidden",children:(0,i.jsx)("div",{ref:r,className:(0,l.cn)("flex","horizontal"===n?"-ml-4":"-mt-4 flex-col",e),...t})})});h.displayName="CarouselContent";let f=a.forwardRef(({className:e,...t},r)=>{let{orientation:a}=m();return(0,i.jsx)("div",{ref:r,role:"group","aria-roledescription":"slide",className:(0,l.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===a?"pl-4":"pt-4",e),...t})});f.displayName="CarouselItem";let v=a.forwardRef(({className:e,variant:t="outline",size:r="icon",...a},n)=>{let{orientation:o,scrollPrev:d,canScrollPrev:c}=m();return(0,i.jsxs)(u,{ref:n,variant:t,size:r,className:(0,l.cn)("absolute rounded-full","horizontal"===o?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:d,...a,children:[(0,i.jsx)(s.A,{className:"h-6 w-6 text-textBluePrimary"}),(0,i.jsx)("span",{className:"sr-only",children:"Previous slide"})]})});v.displayName="CarouselPrevious";let g=a.forwardRef(({className:e,variant:t="outline",size:r="icon",...a},n)=>{let{orientation:s,scrollNext:d,canScrollNext:c}=m();return(0,i.jsxs)(u,{ref:n,variant:t,size:r,className:(0,l.cn)("absolute rounded-full","horizontal"===s?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",e),disabled:!c,onClick:d,...a,children:[(0,i.jsx)(o.A,{className:"h-6 w-6 text-textBluePrimary"}),(0,i.jsx)("span",{className:"sr-only",children:"Next slide"})]})});g.displayName="CarouselNext"},7904:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var i=r(2740),a=r(778);let n=()=>(0,i.jsx)(a.default,{})},778:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Skyline-Aviation\\\\client\\\\src\\\\components\\\\FacultyComponent.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\FacultyComponent.jsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[756,22,393],()=>r(4102));module.exports=i})();