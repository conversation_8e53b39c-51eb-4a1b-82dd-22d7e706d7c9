"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[217],{5217:(e,n,t)=>{t.d(n,{UC:()=>ei,Y9:()=>eo,q7:()=>er,bL:()=>et,l9:()=>ea});var r=t(2115),o=t(8166),a=t(9741),i=t(8068),l=t(3610),d=t(1488),s=t(3360),c=t(6611),u=t(7028),f=t(7668),p=t(5155),m="Collapsible",[v,N]=(0,o.A)(m),[b,h]=v(m),g=r.forwardRef((e,n)=>{let{__scopeCollapsible:t,open:o,defaultOpen:a,disabled:i,onOpenChange:l,...c}=e,[u=!1,m]=(0,d.i)({prop:o,defaultProp:a,onChange:l});return(0,p.jsx)(b,{scope:t,disabled:i,contentId:(0,f.B)(),open:u,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),children:(0,p.jsx)(s.sG.div,{"data-state":C(u),"data-disabled":i?"":void 0,...c,ref:n})})});g.displayName=m;var w="CollapsibleTrigger",y=r.forwardRef((e,n)=>{let{__scopeCollapsible:t,...r}=e,o=h(w,t);return(0,p.jsx)(s.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":C(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:n,onClick:(0,l.m)(e.onClick,o.onOpenToggle)})});y.displayName=w;var x="CollapsibleContent",A=r.forwardRef((e,n)=>{let{forceMount:t,...r}=e,o=h(x,e.__scopeCollapsible);return(0,p.jsx)(u.C,{present:t||o.open,children:e=>{let{present:t}=e;return(0,p.jsx)(R,{...r,ref:n,present:t})}})});A.displayName=x;var R=r.forwardRef((e,n)=>{let{__scopeCollapsible:t,present:o,children:a,...l}=e,d=h(x,t),[u,f]=r.useState(o),m=r.useRef(null),v=(0,i.s)(n,m),N=r.useRef(0),b=N.current,g=r.useRef(0),w=g.current,y=d.open||u,A=r.useRef(y),R=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>A.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,c.N)(()=>{let e=m.current;if(e){R.current=R.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let n=e.getBoundingClientRect();N.current=n.height,g.current=n.width,A.current||(e.style.transitionDuration=R.current.transitionDuration,e.style.animationName=R.current.animationName),f(o)}},[d.open,o]),(0,p.jsx)(s.sG.div,{"data-state":C(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!y,...l,ref:v,style:{"--radix-collapsible-content-height":b?"".concat(b,"px"):void 0,"--radix-collapsible-content-width":w?"".concat(w,"px"):void 0,...e.style},children:y&&a})});function C(e){return e?"open":"closed"}var j=t(4256),I="Accordion",O=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[_,T,k]=(0,a.N)(I),[D,E]=(0,o.A)(I,[k,N]),M=N(),U=r.forwardRef((e,n)=>{let{type:t,...r}=e;return(0,p.jsx)(_.Provider,{scope:e.__scopeAccordion,children:"multiple"===t?(0,p.jsx)(H,{...r,ref:n}):(0,p.jsx)(G,{...r,ref:n})})});U.displayName=I;var[P,L]=D(I),[S,F]=D(I,{collapsible:!1}),G=r.forwardRef((e,n)=>{let{value:t,defaultValue:o,onValueChange:a=()=>{},collapsible:i=!1,...l}=e,[s,c]=(0,d.i)({prop:t,defaultProp:o,onChange:a});return(0,p.jsx)(P,{scope:e.__scopeAccordion,value:s?[s]:[],onItemOpen:c,onItemClose:r.useCallback(()=>i&&c(""),[i,c]),children:(0,p.jsx)(S,{scope:e.__scopeAccordion,collapsible:i,children:(0,p.jsx)(q,{...l,ref:n})})})}),H=r.forwardRef((e,n)=>{let{value:t,defaultValue:o,onValueChange:a=()=>{},...i}=e,[l=[],s]=(0,d.i)({prop:t,defaultProp:o,onChange:a}),c=r.useCallback(e=>s(function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...n,e]}),[s]),u=r.useCallback(e=>s(function(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return n.filter(n=>n!==e)}),[s]);return(0,p.jsx)(P,{scope:e.__scopeAccordion,value:l,onItemOpen:c,onItemClose:u,children:(0,p.jsx)(S,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(q,{...i,ref:n})})})}),[W,B]=D(I),q=r.forwardRef((e,n)=>{let{__scopeAccordion:t,disabled:o,dir:a,orientation:d="vertical",...c}=e,u=r.useRef(null),f=(0,i.s)(u,n),m=T(t),v="ltr"===(0,j.jH)(a),N=(0,l.m)(e.onKeyDown,e=>{var n;if(!O.includes(e.key))return;let t=e.target,r=m().filter(e=>{var n;return!(null===(n=e.ref.current)||void 0===n?void 0:n.disabled)}),o=r.findIndex(e=>e.ref.current===t),a=r.length;if(-1===o)return;e.preventDefault();let i=o,l=a-1,s=()=>{(i=o+1)>l&&(i=0)},c=()=>{(i=o-1)<0&&(i=l)};switch(e.key){case"Home":i=0;break;case"End":i=l;break;case"ArrowRight":"horizontal"===d&&(v?s():c());break;case"ArrowDown":"vertical"===d&&s();break;case"ArrowLeft":"horizontal"===d&&(v?c():s());break;case"ArrowUp":"vertical"===d&&c()}null===(n=r[i%a].ref.current)||void 0===n||n.focus()});return(0,p.jsx)(W,{scope:t,disabled:o,direction:a,orientation:d,children:(0,p.jsx)(_.Slot,{scope:t,children:(0,p.jsx)(s.sG.div,{...c,"data-orientation":d,ref:f,onKeyDown:o?void 0:N})})})}),z="AccordionItem",[K,V]=D(z),Y=r.forwardRef((e,n)=>{let{__scopeAccordion:t,value:r,...o}=e,a=B(z,t),i=L(z,t),l=M(t),d=(0,f.B)(),s=r&&i.value.includes(r)||!1,c=a.disabled||e.disabled;return(0,p.jsx)(K,{scope:t,open:s,disabled:c,triggerId:d,children:(0,p.jsx)(g,{"data-orientation":a.orientation,"data-state":en(s),...l,...o,ref:n,disabled:c,open:s,onOpenChange:e=>{e?i.onItemOpen(r):i.onItemClose(r)}})})});Y.displayName=z;var J="AccordionHeader",Q=r.forwardRef((e,n)=>{let{__scopeAccordion:t,...r}=e,o=B(I,t),a=V(J,t);return(0,p.jsx)(s.sG.h3,{"data-orientation":o.orientation,"data-state":en(a.open),"data-disabled":a.disabled?"":void 0,...r,ref:n})});Q.displayName=J;var X="AccordionTrigger",Z=r.forwardRef((e,n)=>{let{__scopeAccordion:t,...r}=e,o=B(I,t),a=V(X,t),i=F(X,t),l=M(t);return(0,p.jsx)(_.ItemSlot,{scope:t,children:(0,p.jsx)(y,{"aria-disabled":a.open&&!i.collapsible||void 0,"data-orientation":o.orientation,id:a.triggerId,...l,...r,ref:n})})});Z.displayName=X;var $="AccordionContent",ee=r.forwardRef((e,n)=>{let{__scopeAccordion:t,...r}=e,o=B(I,t),a=V($,t),i=M(t);return(0,p.jsx)(A,{role:"region","aria-labelledby":a.triggerId,"data-orientation":o.orientation,...i,...r,ref:n,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function en(e){return e?"open":"closed"}ee.displayName=$;var et=U,er=Y,eo=Q,ea=Z,ei=ee},7028:(e,n,t)=>{t.d(n,{C:()=>i});var r=t(2115),o=t(8068),a=t(6611),i=e=>{let{present:n,children:t}=e,i=function(e){var n,t;let[o,i]=r.useState(),d=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[u,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=l(d.current);c.current="mounted"===u?e:"none"},[u]),(0,a.N)(()=>{let n=d.current,t=s.current;if(t!==e){let r=c.current,o=l(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let n;let t=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(d.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=l(d.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:r.useCallback(e=>{e&&(d.current=getComputedStyle(e)),i(e)},[])}}(n),d="function"==typeof t?t({present:i.isPresent}):r.Children.only(t),s=(0,o.s)(i.ref,function(e){var n,t;let r=null===(n=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===n?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(t=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof t||i.isPresent?r.cloneElement(d,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"}}]);