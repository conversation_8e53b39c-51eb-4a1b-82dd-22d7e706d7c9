(()=>{var e={};e.id=158,e.ids=[158],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},2926:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var i=r(260),a=r(8203),n=r(5155),o=r.n(n),s=r(7292),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let d=["",{children:["course",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3405)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\course\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,1485,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\course\\page.jsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/course/page",pathname:"/course",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},24:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9607,23)),Promise.resolve().then(r.t.bind(r,1066,23)),Promise.resolve().then(r.bind(r,1362)),Promise.resolve().then(r.bind(r,74))},6872:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,8531,23)),Promise.resolve().then(r.t.bind(r,1902,23)),Promise.resolve().then(r.bind(r,9526)),Promise.resolve().then(r.bind(r,2595))},3238:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(6301);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:s="",children:l,iconNode:d,...c},m)=>(0,i.createElement)("svg",{ref:m,...o,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",s),...c},[...d.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...o},l)=>(0,i.createElement)(s,{ref:l,iconNode:t,className:n(`lucide-${a(e)}`,r),...o}));return r.displayName=`${e}`,r}},3405:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var i=r(2740),a=r(5635),n=r(74);let o=[{title:"What type of Learning Environemnt will the students be in ?",content:(0,i.jsx)("p",{children:"● We have tried our best efforts to make and maintain high standard learning environment. Our Classroom is fully air conditioned to make learning in comfortable and relaxed conditions. Our philosophy and intention are to limit the number of students in each intake to a manageable level of 20 students. This way, we can give each student more individual attention resulting in improved overall performance by the students. We have no more than 20 students in any phase of training at any one time."})},{title:"How are the students taught ?",content:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-base font-bold text-blue-700 mb-2",children:"● Flight Simulator Training"}),(0,i.jsx)("p",{className:"text-gray-700 text-xs md:text-sm mb-1",children:"Students are given opportunity to have hands on training on In-house Flight Simulator where student learns and acquires basic flying skills through VFR mode and fly in practice area and do touch and goes at the airport to develop take off, circuit pattern, approach and landings and also develops good instrument scanning while on IFR mode."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-base font-bold text-blue-700 mb-2",children:"● Audio Visual Aids"}),(0,i.jsx)("p",{className:"text-gray-700 text-xs md:text-sm mb-1",children:"As technology has changed in each and every field of life, education should not be apart from it. So we arrange educational films based on new syllabus, the entire study program on audiovisuals as viewing has much more impact than hearing. Home Theatre is made available to deliver audiovisual lectures via educational DVDs procured from world renowned publisher like ASA and Jeppesen companies of USA on various aviation topics will be provided for all students."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-base font-bold text-blue-700 mb-2",children:"● Radio Communication Practical Facility"}),(0,i.jsx)("p",{className:"text-gray-700 text-xs md:text-sm mb-1",children:"The Skyline Aviation Club is only aviation training institute in India having Amateur (HAM) Radio Station licenced by Wireless Planning and Co-Ordination (WPC), Ministry of Telecommunication & IT, Government of India since last 30 years. We have well equipped and modern wireless lab having HF, VHF and UHF wireless sets where each and every wireless communication demonstration are carried out to understand the syllabus of Radio Telephony (R) Licence /Flight Radio Telephone Licence and Amateur (HAM) Radio Licence."})]}),(0,i.jsxs)("section",{children:[(0,i.jsx)("h2",{className:"text-base font-bold text-blue-700 mb-2",children:"● Aviation Library"}),(0,i.jsx)("p",{className:"text-gray-700 text-xs md:text-sm mb-1",children:"The Skyline Aviation Club is only aviation training institute in India having a well-furnished library. Library is storing 4000 plus books on various subjects useful for students with selective and informative aviation books, Pilot Training Manuals, ICAO Annexure's, ICAO Documents, aviation magazines, periodicals including all Commercial Pilot Licence syllabus books as prescribed by the DGCA, Ministry of Civil Aviation, Government of India. Pilot Training Books/ Manuals as prescribed by Federal Aviation Administration (FAA), USA and Transport Canada (TC) Canada."})]})]})},{title:"What is the teaching Methodology ?",content:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("p",{children:"● The courses are a carefully structured mix of classroom sessions, computer based training and audio-visual presentations on Home Theatre Screen, Real time Air Traffic Control (ATC) Radio Communication via Visuals and audio plus Well-equipped In-house Flight Simulator. Theoretical aspects guided by the faculty, help the student test the concepts that have been explained in class before assimilating them."}),(0,i.jsx)("p",{children:"● The courses are made simpler by using latest IFR Jeppesen Aeronautical charts for Enroute flying and Instrument Approaches, Landing Charts, Aerodrome Obstruction Charts and Terminal, Sectional and World Aeronautical Charts (WAC) for VFR air navigation. Moreover, the courses are on up-to-date aviation information as per the International Civil Aviation Organization (ICAO) Annexes and documents, Aeronautical Information Publications (AIP) India, AERADIO, NOTAMS and updated Jeppesen Manual especially for the Indian flying environment. The Club is one of the rare institutions who boasts of having highly qualified, rated and experienced war veterans."}),(0,i.jsx)("p",{children:"● Personalized instructions are provided to each and every student. The student's progress is continuously assessed through a series of internal tests and assignments. These are given significant weight in award of Final Certificate of Achievement from the Club"})]})},{title:"I do not stay in Mumbai. Is Lodging and Boarding Provided ?",content:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("p",{children:"● We provide for Lodging and Boarding which is situated walking distance from our training center. The facilities are well maintained to ensure a peaceful and livable environment with all the basic necessities. Separate facilities for boys and girls are available."}),(0,i.jsx)("p",{children:"● Home cooked meals can also be provided twice a day."})]})},{title:"Who will be teaching the students ?",content:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("p",{children:"● Our present pilot training courses are managed by well-qualified and highly experienced professional pilots holding I Commercial Pilot Licence with Pilot Instructor Rating certified by Director General of Civil Aviation (DGCA) Government of India and Federal Aviation Administration (FAA) Government of United States of America who have not just attained knowledge and experiences of commercial pilots, but have special training on instrument rating, multi-engine rating and a special qualification as PILOT INSTRUCTOR. They hold ADVANCED INSTRUMENT GROUND PILOT INSTRUCTOR (A.G.I.I), Certificate from Federal Aviation Administration (FAA) USA especially for you ! Some faculty members are retired pilots from Indian Air Force and Indian Naval Aviation. The faculty is capable of teaching students from zero level of Student Pilot through Airline Transport Pilot with their invaluable aviation experiences of last forty years."}),(0,i.jsx)("p",{children:"● The Club and its instructors are members of many aviation organizations of India like Federation of Indian Pilots (FIP), Aeronautical Society of India (ASI), Indian Women Pilots Association (IWPA) and having association with Aircraft Owners & Pilots Association (AOPA) USA, International Air Cadet Exchange Association (IACEA) Great Britain, Civil Air Patrol (CAP) Auxiliary US Air Force, USA. Ninety-nine Inc. (International Woman's Pilot Association) USA, FAPA USA, EAA USA, Major Airlines Aviation Agencies and best flight training Organizations (FTO) in India and abroad. A combination of such versatile experiences is like adding one more feather to the cap of the Club. Our Chief Pilot Instructor Capt. (Dr.) AD Manek had personally visited and gained knowledge of functioning of International Civil Aviation Organization (ICAO) HQ Montreal, Canada, International Telecommunication Union (ITU) HQ Geneva, Switzerland which are supreme authorities to control civil aviation throughout world consisting 193 countries, along with visit to NASA, USA to share the best possible knowledge to students."})]})},{title:"Once I finish my training at The Skyine Aviation Club what next ?",content:(0,i.jsx)("p",{children:"● Students completing Ground Training will be deputed for Practical flight training with our affiliated DGCA approved Flight Training Organization or Best Flight Training School in foreign countries like USA, Canada, Australia, New Zealand & U.K. Students and Parents will be provided with all assistance on Visa Information, Foreign Exchange, Air Tickets, Insurance and all related Guidance at a single table."})}],s=()=>(0,i.jsxs)("div",{className:"bg-[url('/assets/faqImage.webp')] bg-cover bg-center bg-local px-4 py-8 md:p-12 lg:py-28 xl:py-28",children:[(0,i.jsx)("h3",{className:"text-white font-extrabold text-2xl md:text-4xl lg:text-6xl mb-4 lg:mb-16",children:"Frequently Asked Questions."}),(0,i.jsx)(n.Accordion,{type:"single",collapsible:!0,className:"grid gap-4 items-start justify-center pt-4 grid-cols-1",children:o.map((e,t)=>(0,i.jsxs)(n.AccordionItem,{value:`item-${t}`,className:"p-3 xl:p-4 rounded bg-accordionBG",children:[(0,i.jsx)(n.AccordionTrigger,{className:"text-white text-sm font-medium xl:text-base",children:e.title}),(0,i.jsx)(n.AccordionContent,{className:"text-white pt-3 text-sm font-medium xl:text-base",children:e.content})]},t))})]}),l=(0,r(3238).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);(function(){let e=new Date("1987-10-15"),t=new Date;t.getFullYear(),e.getFullYear(),t.getMonth()<e.getMonth()||t.getMonth()===e.getMonth()&&(t.getDate(),e.getDate())})();let d=[{id:1,slug:"indian-commercial-pilot-licence-course",title:"DGCA",subTitle:"Indian Commercial Pilot Licence Course",desc:"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aircraft & Engine (Technical) General & Specific"}),(0,i.jsx)("li",{className:"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Radio Telephonic"})]}),image:"/cptManek2.jpg",bgImage:"/assets/courses/IndianCommercialPilot.jpg"},{id:2,slug:"american-commercial-pilots-licence",title:"FAA USA",subTitle:"American Commercial Pilot Licence Course",desc:"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical"})]}),image:"/assets/courseTwoImage.png",bgImage:"/assets/courses/AmericanCommercialPilot.jpg"},{id:3,slug:"aircraft-flight-dispatcher-licence-course",title:"FAA USA",subTitle:"Aircraft/Flight Dispatcher Licence Course",desc:"An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Flight Training"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical"})]}),image:"/assets/dispatch.jpg",bgImage:"/assets/courses/flightDispatcher.jpg"},{id:4,slug:"radio-telephony-r-aeromobile-frtol-licence",title:"DGCA/WPC",subTitle:"Radio Telephony (R) Aeromobile/FRTOL Licence Course",desc:"This is a professional course of international standards as per the general guidelines prescribed under international radio regulations applicable to the aeronautical mobile service.",topics:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("h3",{className:"mb-1",children:"Part 1: Practical Test in Regulation and Procedure"}),(0,i.jsx)("h3",{className:"mb-1",children:"Part 2: Oral Examination"}),(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Regulation & Procedure"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Radio Principles & Practice"})]})]}),image:"/assets/courseFourImage.png",bgImage:"/assets/courses/RadioTelephony.jpg"},{id:5,slug:"commercial-pilot-licence-package-program",title:"Inclusive of Indian CPL + American CPL or Canadian CPL",subTitle:"Commercial Pilot Licence Package Program",desc:"The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical Aviation"})]}),image:"/assets/albumImageTwo.png",bgImage:"/assets/courses/CPLProgram.jpg"},{id:6,slug:"airhostess-flight-purser-training-course",title:"Cabin Crew Training Program",subTitle:"Airhostess/Flight Purser Training Course",desc:"Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.",topics:null,image:"/assets/courseSixImage.png",bgImage:"/assets/courses/airhostess.jpg"},{id:7,slug:"airport-ground-staff-course",title:"Airport Ground Services",subTitle:"Airport Ground Staff Course",desc:"If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical Aviation"})]}),image:"/assets/groundStaffHome.jpg",bgImage:"/assets/courses/AirportGroundStaff.jpg"},{id:8,slug:"aviation-foundation-course",title:"Summer Vacation Training Program",subTitle:"Aviation Foundation Course",desc:"This course builds a strong foundation for students aspiring to airline careers like Pilot, Dispatcher, Air Hostess, Ground Staff, AME, ATC Officer, and more.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical Aviation"})]}),image:"/assets/foundationCourse.jpg",bgImage:"/assets/courses/AviationFoundation.jpg"},{id:9,slug:"two-day-aeroplane-or-helicopter-training-workshop",title:"Aeroplane/Helicopter Orientation Training",subTitle:"Two Day Aeroplane/Helicopter Training Workshop",desc:"Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical Aviation"})]}),image:"/assets/courseDetailBanner.png"},{id:10,slug:"foreign-commercial-pilot-licence-conversion-course",title:"Indian Commercial Pilot Licence (DGCA)",subTitle:"Foreign Commercial Pilot Licence Conversion Course",desc:"The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Regulation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical Aviation"})]}),image:"/assets/courseThreeImage.png",bgImage:"/assets/courses/ForeignCplConversion.jpg"},{id:11,slug:"helicopter-commercial-pilot-licence-course",title:"HCPL",subTitle:"Helicopter Commercial Pilot Licence Course",desc:"The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.",topics:(0,i.jsxs)("ul",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Helicopter Aerodynamics"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Air Navigation"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Aviation Meteorology"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Helicopter Flight Rules & Regulations"}),(0,i.jsx)("li",{className:"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",children:"Technical General (Helicopter)"})]}),image:"/assets/helicopterImage.webp",bgImage:"/assets/courses/HelicopterCourses.jpg"}];i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment,i.Fragment;var c=r(9607),m=r.n(c),x=r(1362);let p=()=>(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"relative bg-coursesDetailBanner  bg-cover bg-[-26rem] md:bg-top h-[740px]",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent"}),(0,i.jsxs)("div",{className:"absolute bottom-8 text-center mx-4 md:mx-12 lg:mx-32 xl:mx-32 lg:text-left left-0 right-0",children:[(0,i.jsx)("h1",{className:"font-extrabold text-whiteOne text-2xl leading-9 md:text-[40px] md:leading-[44px] lg:text-6xl lg:w-[42rem] mb-2 md:mb-4",children:"Take Flight with Our Expertly Designed Courses"}),(0,i.jsx)("p",{className:"text-whiteOne font-bold text-lg md:text-3xl lg:text-2xl xl:text-2xl",children:"Explore a Range of Aviation Training Programs"})]})]}),(0,i.jsxs)("div",{className:"relative bg-whiteOne z-0",children:[(0,i.jsx)("div",{className:"hidden lg:block z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute top-60 left-0 right-0 w-full h-[300px]"}),(0,i.jsx)("div",{className:"hidden lg:block absolute bg-dotsImage bottom-[34rem] right-0 bg-center bg-no-repeat w-10 h-36"}),(0,i.jsxs)("div",{className:"relative py-16 px-4 md:px-8 lg:px-32 xl:px-32",children:[(0,i.jsxs)("div",{className:"md:hidden lg:hidden xl:hidden",children:[(0,i.jsx)("h3",{className:"text-center text-xl font-bold mb-2 lg:mb-3 text-textBluePrimary",children:"Courses we offer"}),(0,i.jsx)("p",{className:"text-center text-sm md:text-base font-light text-customBlack mb-6",children:"Courses Designed to Help You Soar Take Off with The Skyline Aviation Club !"})]}),(0,i.jsx)("div",{className:"md:hidden",children:(0,i.jsx)(x.default,{})}),(0,i.jsx)("div",{className:"hidden md:grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-12",children:d.map((e,t)=>(0,i.jsxs)("div",{className:"p-4 rounded shadow-courseCardShadow md:h-[550px] lg:h-[580px] relative bg-whiteOne z-10",children:[(0,i.jsx)("div",{className:"rounded w-full h-[230px]",children:(0,i.jsx)(a.default,{src:e.image,alt:"coursesCardImage",className:"h-full w-3/5 mx-auto rounded-xl",width:328,height:200})}),(0,i.jsxs)("div",{className:"flex mt-4 flex-col gap-2 items-center",children:[(0,i.jsx)("h3",{className:"text-textBluePrimary font-bold text-2xl text-center",children:e.subTitle}),(0,i.jsx)("h4",{className:"text-customBlack text-center font-semibold text-base",children:e.title}),(0,i.jsx)("p",{className:"text-customGrey100 font-medium text-sm",children:e.desc}),(0,i.jsxs)(m(),{href:`/course/${e.slug}`,className:"flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 ",children:[(0,i.jsx)("span",{className:"font-bold",children:"View Course Details"}),(0,i.jsx)(l,{className:"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1"})]})]})]},t))})]})]}),(0,i.jsx)(s,{})]}),u=()=>(0,i.jsx)(p,{})},1362:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Skyline-Aviation\\\\client\\\\src\\\\components\\\\CourseCarousel.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\CourseCarousel.jsx","default")},74:(e,t,r)=>{"use strict";r.d(t,{Accordion:()=>a,AccordionContent:()=>s,AccordionItem:()=>n,AccordionTrigger:()=>o});var i=r(6760);let a=(0,i.registerClientReference)(function(){throw Error("Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\ui\\accordion.jsx","Accordion"),n=(0,i.registerClientReference)(function(){throw Error("Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\ui\\accordion.jsx","AccordionItem"),o=(0,i.registerClientReference)(function(){throw Error("Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\ui\\accordion.jsx","AccordionTrigger"),s=(0,i.registerClientReference)(function(){throw Error("Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\ui\\accordion.jsx","AccordionContent")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[756,22,393,667,528],()=>r(2926));module.exports=i})();