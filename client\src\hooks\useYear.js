import { useState, useEffect } from "react";

export function useYear(dobString) {
  const dob = new Date(dobString); // Pass dob as "1987-10-15"
  const [years, setYears] = useState(0);

  useEffect(() => {
    function calculateYears() {
      const now = new Date();
      let age = now.getFullYear() - dob.getFullYear();

      if (
        now.getMonth() < dob.getMonth() ||
        (now.getMonth() === dob.getMonth() && now.getDate() < dob.getDate())
      ) {
        age--;
      }
      return age;
    }

    function getNextBirthday() {
      const now = new Date();
      let next = new Date(now.getFullYear(), dob.getMonth(), dob.getDate());
      if (next <= now) {
        next.setFullYear(next.getFullYear() + 1);
      }
      return next;
    }

    // Set initial years
    setYears(calculateYears());

    // Schedule update only at next birthday
    const msUntilNextBirthday = getNextBirthday() - new Date();
    const timer = setTimeout(() => {
      setYears(calculateYears());
    }, msUntilNextBirthday);

    return () => clearTimeout(timer);
  }, [dob]);

  return years;
}
