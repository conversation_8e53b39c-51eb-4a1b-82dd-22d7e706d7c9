import Image from "next/image";
import FAQContainer from "./FAQContainer";
import { ArrowRight } from "lucide-react";
import { coursesData } from "@/constant/landingPageData";
import Link from "next/link";
import CourseCarousel from "./CourseCarousel";

const CoursesComponent = () => {
  return (
    <>
      <div className="relative bg-coursesBanner bg-cover bg-[-26rem] md:bg-top h-[740px]">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent"></div>
        <div className="absolute bottom-8 text-center mx-4 md:mx-12 lg:mx-32 xl:mx-32 lg:text-left left-0 right-0">
          <h1 className="font-extrabold text-whiteOne text-2xl leading-9 md:text-[40px] md:leading-[44px] lg:text-6xl lg:w-[42rem] mb-2 md:mb-4">
            Take Flight with Our Expertly Designed Courses
          </h1>
          <p className="text-whiteOne font-bold text-lg md:text-3xl lg:text-2xl xl:text-2xl">
            Explore a Range of Aviation Training Programs
          </p>
        </div>
      </div>
      <div className="relative bg-whiteOne z-0">
        <div className="hidden lg:block z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute top-60 left-0 right-0 w-full h-[300px]"></div>
        <div className="hidden lg:block absolute bg-dotsImage bottom-[34rem] right-0 bg-center bg-no-repeat w-10 h-36"></div>
        <div className="relative py-16 px-4 md:px-8 lg:px-32 xl:px-32">
          <div className="md:hidden lg:hidden xl:hidden">
            <h3 className="text-center text-xl font-bold mb-2 lg:mb-3 text-textBluePrimary">
              Courses we offer
            </h3>
            <p className="text-center text-sm md:text-base font-light text-customBlack mb-6">
              Courses Designed to Help You Soar Take Off with The Skyline Aviation Club !
            </p>
          </div>
          <div className="md:hidden">
            <CourseCarousel />
          </div>
          <div className="hidden md:grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-12">
            {coursesData.map((item, index) => (
              <div
                className="p-4 rounded shadow-courseCardShadow md:h-[550px] lg:h-[580px] relative bg-whiteOne z-10"
                key={index}
              >
                <div className="rounded w-full h-[230px]">
                  <Image
                    src={item.image}
                    alt="coursesCardImage"
                    // className="w-full h-full rounded-xl"
                    className="h-full w-3/5 mx-auto rounded-xl"
                    width={328}
                    height={200}
                  />
                </div>
                <div className="flex mt-4 flex-col gap-2 items-center">
                  <h3 className="text-textBluePrimary font-bold text-2xl text-center">
                    {item.subTitle}
                  </h3>
                  <h4 className="text-customBlack text-center font-semibold text-base">
                    {item.title}
                  </h4>
                  <p className="text-customGrey100 font-medium text-sm">
                    {item.desc}
                  </p>
                  <Link
                    href={`/course/${item.slug}`}
                    className="absolute bottom-4 flex items-center gap-2 text-sm font-bold text-white py-3 px-4 md:py-2 md:pr-2 md:pl-6 rounded-full border border-transparent bg-buttonBGPrimary"
                  >
                    View Course Details
                    <ArrowRight
                      size={18}
                      className="ml-2 w-8 h-8 p-1 bg-white rounded-full text-arrowIconColor"
                    />
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <FAQContainer />
    </>
  );
};

export default CoursesComponent;
