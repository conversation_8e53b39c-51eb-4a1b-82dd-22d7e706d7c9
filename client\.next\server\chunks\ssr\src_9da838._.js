module.exports = {

"[project]/src/constant/navbarLinks.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "coursesLinks": (()=>coursesLinks),
    "galleryLInks": (()=>galleryLInks),
    "mainLinks": (()=>mainLinks)
});
const mainLinks = [
    {
        text: "Home",
        link: "/"
    },
    {
        text: "Gallery",
        link: "/gallery"
    },
    {
        text: "Courses",
        link: "/course"
    },
    {
        text: "Events",
        link: "/events"
    },
    {
        text: "About Us",
        link: "/aboutus"
    },
    {
        text: "Contact Us",
        link: "/enquire"
    }
];
const galleryLInks = [
    {
        text: "Albums",
        link: "/gallery"
    },
    {
        text: "TV and Press",
        link: "/gallery"
    },
    {
        text: "Awards",
        link: "/gallery"
    },
    {
        text: "VVIP Testimonials",
        link: "/gallery"
    },
    {
        text: "Government Approvals",
        link: "/gallery"
    }
];
const coursesLinks = [
    {
        text: "Indian Commercial Pilot",
        link: `indian-commercial-pilot-licence-course`
    },
    {
        text: "American Commercial Pilot",
        link: `american-commercial-pilots-licence`
    },
    {
        text: "CPL Package Program",
        link: `commercial-pilot-licence-package-program`
    },
    {
        text: "Foreign CPL Conversion Course",
        link: `foreign-commercial-pilot-licence-conversion-course`
    },
    {
        text: "Helicopter Pilot Training",
        link: `helicopter-commercial-pilot-licence-course`
    },
    {
        text: "American Flight Dispatcher",
        link: `aircraft-flight-dispatcher-licence-course`
    },
    {
        text: "Radio Telephony Licence",
        link: `radio-telephony-r-aeromobile-frtol-licence`
    },
    {
        text: "Airhostess/Flight Purser",
        link: `airhostess-flight-purser-training-course`
    },
    {
        text: "Airport Ground Staff",
        link: `airport-ground-staff-course`
    },
    {
        text: "Aviation Foundation Course",
        link: `aviation-foundation-course`
    },
    {
        text: "Aeroplane/Helicopter Training Workshop",
        link: `two-day-aeroplane-or-helicopter-training-workshop`
    }
];
;
}}),
"[project]/src/constant/courseDetailData.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// const eligibilityData = [
//   {
//     image: "/assets/ageCriteriaImage.png",
//     class: "w-16 h-20 md:w-20 md:h-14 ",
//     title: "Age Criteria",
//     desc: (
//       <p className="text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2">
//         Minimum age - 17 Years <br />
//         Maximum age - 35 Years
//       </p>
//     ),
//   },
//   {
//     image: "/assets/educationImage.png",
//     class: "w-16 h-20 md:w-20 md:h-14 ",
//     title: "Education",
//     desc: (
//       <p className="text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2">
//         10+2 with Maths and Physics <br /> or{" "}
//         <span className="text-textBluePrimary">Higher Education*</span>
//       </p>
//     ),
//   },
//   {
//     image: "/assets/personalityImage.png",
//     class: "w-16 h-20 md:w-20 md:h-14 ",
//     title: "Personality",
//     desc: (
//       <p className="text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2">
//         Be able to read, write, speak and understand the English language
//       </p>
//     ),
//   },
//   {
//     image: "/assets/medicalImage.png",
//     class: "w-16 h-20 md:w-20 md:h-14 ",
//     title: "Medical",
//     desc: (
//       <p className="text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2">
//         DGCA Medical Class 2 Certificate (CA35) (Note: We Will arrange Medical
//         Examination with our panel doctor who is DGCA approved Class II Medical
//         Examiner)
//       </p>
//     ),
//   },
// ];
// const jobAssistanceData = [
//   {
//     title: "Comprehensive Career Grooming",
//     desc: "Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement",
//   },
//   {
//     title: "Global Job Opportunities",
//     desc: " We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry.",
//   },
//   {
//     title: "Proven Success",
//     desc: "So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !",
//   },
// ];
__turbopack_esm__({
    "courseData": (()=>courseData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
;
const courseData = [
    {
        id: 1,
        slug: "indian-commercial-pilot-licence-course",
        courseName: "DGCA",
        courseDesc: "Indian Commercial Pilot Licence Course",
        introduction: "If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.",
        careerProspects: [
            {
                title: "Competitive Salaries",
                description: "Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies."
            },
            {
                title: "Exciting and Dynamic Career",
                description: "Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."
            },
            {
                title: "Prestige and Respect",
                description: "Gain recognition and admiration among peers and society as a skilled and accomplished aviator."
            },
            {
                title: "Travel the World",
                description: "Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."
            }
        ],
        theoryTrainingData: [
            {
                title: "Women Empowerment Scheme",
                description: "50% scholarship for girls & PWFA members by The Skyline Aviation Club"
            },
            {
                title: "Jai Jawan Scheme",
                description: "50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"
            }
        ],
        practicalTrainingData: [
            {
                title: "Scholarship - Govt. of India",
                description: "Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Govt. of Gujarat",
                description: "Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Banks",
                description: "Up to 85% of course fees from affiliated banks for general category students."
            }
        ],
        courseStructure: [
            {
                title: "DGCA Commercial Pilot Licence Course – Airplane",
                courseDuration: "2 years",
                parts: [
                    {
                        title: "Part I - Theory Course",
                        duration: "Theory Course - 4 Months (Full Time)"
                    },
                    {
                        title: "Part II - Practical Flight Training of 200 Flight hours (185 Hours on Single Engine Land + 15 Hours on Multi Engine Lnad)",
                        duration: "1 Year & 8 Months at our Affiliated Flight Training Organization (FTO)"
                    }
                ]
            }
        ],
        subCourseStructure: [
            {
                title: "DGCA Commercial Pilot Licence Written Exams",
                subjects: [
                    "Air Navigation",
                    "Aviation Meteorology",
                    "Air Regulations",
                    "Aircraft & Engine (Technical) General & Specific"
                ]
            },
            {
                title: "WPC, Ministry of Telecommunication Exam",
                licence: "Radio Telephony (Restricted) Licence / Flight Radio Telephone Licence",
                parts: [
                    "Part I: Radio Communication Procedures",
                    "Part II: Radio Theory"
                ]
            }
        ],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Age Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Minimum age - 17 Years ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 166,
                            columnNumber: 36
                        }, this),
                        "Maximum age - 35 Years"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 165,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Education",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "10+2 with Maths and Physics ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 177,
                            columnNumber: 41
                        }, this),
                        " or",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-textBluePrimary",
                            children: "Higher Education*"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 178,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 176,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Personality",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "Be able to read, write, speak and understand the English language"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 187,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Medical",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "DGCA Medical Class 2 Certificate (CA35)"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 197,
                    columnNumber: 11
                }, this)
            }
        ],
        jobAssistanceData: [
            {
                title: "Comprehensive Career Grooming",
                desc: "Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement"
            },
            {
                title: "Global Job Opportunities",
                desc: " We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry."
            },
            {
                title: "Proven Success",
                desc: "So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !"
            }
        ]
    },
    {
        id: 2,
        slug: "american-commercial-pilots-licence",
        courseName: "FAA USA",
        courseDesc: "American Commercial Pilot's Licence",
        introduction: "If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.",
        careerProspects: [
            {
                title: "Competitive Salaries",
                description: "Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies."
            },
            {
                title: "Exciting and Dynamic Career",
                description: "Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."
            },
            {
                title: "Prestige and Respect",
                description: "Gain recognition and admiration among peers and society as a skilled and accomplished aviator."
            },
            {
                title: "Travel the World",
                description: "Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."
            }
        ],
        theoryTrainingData: [
            {
                title: "Women Empowerment Scheme",
                description: "50% scholarship for girls & PWFA members by The Skyline Aviation Club"
            },
            {
                title: "Jai Jawan Scheme",
                description: "50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"
            }
        ],
        practicalTrainingData: [
            {
                title: "Scholarship - Govt. of India",
                description: "Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Govt. of Gujarat",
                description: "Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Banks",
                description: "Up to 85% of course fees from affiliated banks for general category students."
            }
        ],
        courseStructure: [
            {
                title: "American FAA Commercial Pilot Licence Course - Airplane",
                courseDuration: "1 year",
                parts: [
                    {
                        title: "Part I - Theory Course (in India)",
                        duration: "Theory Course - 3 Months (inclusive of PPC, IRC, CPC)"
                    },
                    {
                        title: "Part II - Practical Flight Training (in USA)",
                        duration: "Practical Flight Training - 9 Months (inclusive of 254 hrs with 25 hrs MEL) at our affiliated flight school Avel Flight School, Illinois, Chicago, USA"
                    }
                ]
            }
        ],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-16 md:w-20 md:h-14 ",
                title: "Age Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Minimum age - 17 Years ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 299,
                            columnNumber: 36
                        }, this),
                        "Maximum age - 35 Years"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 298,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-16 md:w-20 md:h-14 ",
                title: "Education",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "10+2 with Maths and Physics ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 310,
                            columnNumber: 41
                        }, this),
                        " or",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-textBluePrimary",
                            children: "Higher Education*"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 311,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 309,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Personality",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "Be able to read, write, speak and understand the English language"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 320,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Medical",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "DGCA Medical Class 2 Certificate (CA35)"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 330,
                    columnNumber: 11
                }, this)
            }
        ],
        jobAssistanceData: [
            {
                title: "Career Grooming & Job Assistance",
                desc: "Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad."
            },
            {
                title: "Proven Placement Record",
                desc: "So far, we have helped 398 students secure pilot jobs in various airlines worldwide."
            }
        ],
        shortNote: "Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad. So far we have helped 398 students to get Pilot Jobs in various airlines",
        visaAsistant: "After completion of theory course, students have the option to choose a practical training location in India or United Stated of America. Students interested in going to USA will be assisted in Admission, I-20, DS-160, SEVIS and M1/F1 US Student Visa. So far we have helped 506 students procuring various students visas"
    },
    {
        id: 3,
        slug: "aircraft-flight-dispatcher-licence-course",
        courseName: "FAA USA",
        courseDesc: "Aircraft/Flight Dispatcher Licence Course",
        introduction: "An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline. We Offer FAA(USA) Aircraft Dispatcher Licence Courses which will prepare you for entry into all Airlines worldwide.",
        careerProspects: [
            {
                title: "Competitive Salaries",
                description: "Earn 1.5 Lacs per month as you progress in your career with top airlines and private aviation companies."
            },
            {
                title: "Challenging and Demanding Career",
                description: "This career demands a high level of precision, focus, and resilience under pressure, making it both challenging and highly rewarding for those passionate about aviation."
            },
            {
                title: "Hot Seat Job",
                description: "The term perfectly describes the role of an Aircraft Dispatcher because of the intense pressure, accountability, and critical decision-making required in real time"
            },
            {
                title: "High Responsibities maintaining Multiple Aircrafts",
                description: "An Aircraft Dispatcher carries high responsibilities in maintaining multiple aircraft operations, including Simultaneous Flight Oversight, Real-Time Monitoring, Safety Assurance, Collaboration Across Teams. Prioritization Under Pressure"
            },
            {
                title: "Future Progress Quickly",
                description: "The Aircraft Dispatcher role offers opportunities for quick future progress due to: High Demand in Aviation, Career Advancement, Transferable Skills, Licensing and Certification, Dynamic Work Environment."
            }
        ],
        theoryTrainingData: [
            {
                title: "Women Empowerment Scheme",
                description: "50% scholarship for girls & PWFA members by The Skyline Aviation Club"
            },
            {
                title: "Jai Jawan Scheme",
                description: "50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"
            }
        ],
        practicalTrainingData: [
            {
                title: "Educational Loan - Banks",
                description: "Up to 85% of course fees from affiliated banks for general category students."
            }
        ],
        courseStructure: [
            {
                title: "FAA (USA) Aircraft/Flight Dispatcher Licence Course",
                courseDuration: "6 Months",
                parts: [
                    {
                        title: "Part I - Theory Course (in India)",
                        duration: "Theory Course - 4 Months (inclusive of CPL, RTR, IRC And ICAO Aviation English Course)"
                    },
                    {
                        title: "Part II - Practical Flight Training (in USA)",
                        duration: "Practical Flight Training - 2 Months (inclusive of 200 hrs of Classroom Preparation )"
                    }
                ]
            }
        ],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-16 md:w-20 md:h-14 ",
                title: "Age Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Training Eligibility: 21 years ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 427,
                            columnNumber: 44
                        }, this),
                        "Licence Issuance: 23 years"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 426,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-16 md:w-20 md:h-14 ",
                title: "Education",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "10+2 with Maths and Physics ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 438,
                            columnNumber: 41
                        }, this),
                        " or",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-textBluePrimary",
                            children: "Higher Education*"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 439,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 437,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14",
                title: "Personality",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "Be able to read, write, speak and understand the English language"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 448,
                    columnNumber: 11
                }, this)
            }
        ],
        jobAssistanceData: [
            {
                title: "Career Grooming & Job Assistance",
                desc: "Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad."
            },
            {
                title: "Proven Placement Record",
                desc: "So far, we have helped 398 students secure pilot jobs in various airlines worldwide."
            }
        ]
    },
    {
        id: 4,
        slug: "radio-telephony-r-aeromobile-frtol-licence",
        courseName: "DGCA/WPC",
        courseDesc: "Radio Telephony (R) Aeromobile/FRTOL Licence",
        introduction: "This professional course is structured in line with international radio regulations governing the aeronautical mobile service. It meets global standards, and candidates who achieve the required level of performance are awarded a government-recognized licence to operate.",
        careerProspects: [
            {
                title: "Competitive Salaries",
                description: "Start your aviation journey with a competitive salary of ₹25,000 as an FRTOL holder, paving the way for a rewarding career."
            },
            {
                title: "Career Opportunities as an FRTOL",
                description: "Student completing this course and on passing examination and acquiring RT (R) Licence issued by wireless Planning & Co-Ordination Wing, Ministry of Communication, Government of India, can seek employment in the various Indian as well as Foreign Airlines, Airport authorities, Aviation Companies, Oil rigs etc. as Aeronautical Radio Communication Officer"
            }
        ],
        courseStructure: [
            {
                title: "FRTOL",
                courseDuration: "2 Months",
                parts: [
                    {
                        title: "Part I - Radio Telephony and Aviation Phonetics",
                        duration: ""
                    },
                    {
                        title: "Part II - Technical Knowledge",
                        duration: ""
                    }
                ]
            }
        ],
        theoryTrainingData: [],
        practicalTrainingData: [],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Age Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "Minimum Age: 18 years"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 509,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Education",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "12th Pass/Appeared (Any Stream) ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 520,
                            columnNumber: 45
                        }, this),
                        " or",
                        " ",
                        "any ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-textBluePrimary",
                            children: "Higher Education*"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 521,
                            columnNumber: 17
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 519,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Personality",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "Be able to read, write, speak and understand the English language"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 530,
                    columnNumber: 11
                }, this)
            }
        ],
        jobAssistanceData: [
            {
                title: "Job Placement for Radio Operators",
                desc: "Successful students holding requisite aviation qualifications, including a valid RTR (Radio Telephony Restricted) licence issued by the Government of India, will be groomed and assisted for job placement as Radio Officers on oil rigs and in aviation companies abroad."
            },
            {
                title: "Proven Track Record",
                desc: "So far, we have successfully helped 21 students secure Radio Operator jobs in various airlines and aviation organizations worldwide."
            }
        ]
    },
    {
        id: 5,
        slug: "commercial-pilot-licence-package-program",
        courseName: "Inclusive of Indian CPL + American CPL or Canadian CPL",
        courseDesc: "Commercial Pilot Licence Package Program",
        introduction: "The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.",
        careerProspects: [
            {
                title: "All-in-One Aviation Training",
                description: "Students receive all required aviation training under one roof, streamlining the learning process and eliminating the need for multiple institutions."
            },
            {
                title: "Economical Package Deal",
                description: "The bundled course structure is cost-effective for parents, offering maximum value across various aviation domains."
            },
            {
                title: "Versatile Career Preparation",
                description: "This program prepares students for multiple aviation roles, including Commercial Pilot, Flight Dispatcher, and Radio Telephony Officer, increasing job flexibility."
            },
            {
                title: "In-depth & Holistic Training",
                description: "The course delivers a comprehensive understanding of aviation, ensuring students are ready for the challenges of the aviation industry."
            },
            {
                title: "Recognized Certification & Logbook Endorsement",
                description: "Upon successful completion, students receive a course completion certificate and official logbook endorsement, boosting credibility for airline recruitment."
            }
        ],
        theoryTrainingData: [
            {
                title: "Women Empowerment Scheme",
                description: "50% scholarship for girls & PWFA members by The Skyline Aviation Club"
            },
            {
                title: "Jai Jawan Scheme",
                description: "50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"
            }
        ],
        practicalTrainingData: [
            {
                title: "Scholarship - Govt. of India",
                description: "Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Govt. of Gujarat",
                description: "Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Banks",
                description: "Up to 85% of course fees from affiliated banks for general category students."
            }
        ],
        courseStructure: [
            {
                title: "Complete Aviation Training Package – CPL, RTR, ATPL, Dispatcher & ELP",
                courseDuration: "1 Year",
                parts: [
                    {
                        title: "Part I - Theory Training (in India)",
                        duration: "Theory Course – 3 Months (Full-time in Mumbai, covering Commercial Pilot Training, RTR, Aviation English, ATPL/Dispatcher, and ELP)"
                    },
                    {
                        title: "Part II - Practical Flight Training (in USA)",
                        duration: "Practical Training – 9 Months (Flight Training at our partnered facility in the USA)"
                    }
                ]
            }
        ],
        subCourseStructure: [
            {
                title: "FAA Subject Coverage",
                subjects: [
                    "Air Regulation",
                    "Aviation Meteorology",
                    "Air Navigation",
                    "Audio Visual Training",
                    "Aircraft and Engine",
                    "Radio Communication",
                    "Aviation English",
                    "English Language Proficiency (ELP)"
                ]
            },
            {
                title: "FAA Training Levels",
                subjects: [
                    "Student Pilot",
                    "Private Pilot",
                    "Instrument Rating",
                    "Commercial Pilot",
                    "RTR Licence",
                    "Aviation English",
                    "Airline Transport Pilot",
                    "Aircraft Dispatcher"
                ]
            }
        ],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Age Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Minimum age - 17 Years ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 663,
                            columnNumber: 36
                        }, this),
                        "Maximum age - 35 Years"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 662,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Education",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "10+2 with Maths and Physics ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 674,
                            columnNumber: 41
                        }, this),
                        " or",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-textBluePrimary",
                            children: "Higher Education*"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 675,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 673,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Personality",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "Be able to read, write, speak and understand the English language"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 684,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Medical",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "DGCA Medical Class 2 Certificate (CA35)"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 694,
                    columnNumber: 11
                }, this)
            }
        ]
    },
    {
        id: 6,
        slug: "airhostess-flight-purser-training-course",
        courseName: "Cabin Crew Training Program",
        courseDesc: "Airhostess/Flight Purser Training Course",
        introduction: "Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.",
        careerProspects: [
            {
                title: "Competitive Salaries",
                description: "Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines."
            },
            {
                title: "Competitive Salaries",
                description: "Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines."
            },
            {
                title: "High Flying Career",
                description: "Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."
            },
            {
                title: "Respect among Peers",
                description: "Gain recognition and admiration among peers and society as a skilled and accomplished aviator."
            },
            {
                title: "Fly all over the World",
                description: "Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."
            }
        ],
        theoryTrainingData: [
            {
                title: "Women Empowerment Scheme",
                description: "25% Scholarship for girls & PWFA members by The Skyline Aviation Club"
            },
            {
                title: "Jai Jawan Scheme",
                description: "25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"
            }
        ],
        courseStructure: [
            {
                title: "Cabin Crew Course",
                courseDuration: "4 Months",
                parts: [
                    {
                        title: "Swimming",
                        description: "Essential life-saving skill for emergencies."
                    },
                    {
                        title: "Yoga",
                        description: "Enhances flexibility and mental calmness."
                    },
                    {
                        title: "Meditation",
                        description: "Improves focus and stress management."
                    },
                    {
                        title: "Aviation Security",
                        description: "Trains in airport and aircraft security measures."
                    },
                    {
                        title: "Reading Body Language",
                        description: "Interpreting passenger behavior for better service and safety."
                    },
                    {
                        title: "In-Flight Services",
                        description: "Handling passenger needs and service during flights."
                    },
                    {
                        title: "In-Flight Food",
                        description: "Understanding catering and food management."
                    },
                    {
                        title: "In-Flight Entertainment",
                        description: "Providing and managing onboard entertainment."
                    },
                    {
                        title: "Communication Skills",
                        description: "Effective interaction with passengers and crew."
                    },
                    {
                        title: "Aircraft Technical Knowledge",
                        description: "Basic understanding of aircraft mechanics and systems."
                    },
                    {
                        title: "Emergency Equipment on Board the Aircraft",
                        description: "Knowledge of emergency tools and their use."
                    },
                    {
                        title: "The Brace Position",
                        description: "Training on proper brace positions during emergencies."
                    },
                    {
                        title: "Aircraft Evacuation",
                        description: "Procedures for safely evacuating an aircraft."
                    },
                    {
                        title: "Survival (Sea, Jungle, Desert)",
                        description: "Techniques for survival in various terrains."
                    },
                    {
                        title: "Security Measures to Check Hijacking and Bomb",
                        description: "Protocols to manage security threats."
                    },
                    {
                        title: "Beauty and Perfect Airhostess or Flight Purser",
                        description: "Training on grooming and professionalism."
                    },
                    {
                        title: "First Aid",
                        description: "Basic medical assistance for passengers and crew."
                    },
                    {
                        title: "Etiquette and Manners (Finishing School)",
                        description: "Refinement of interpersonal and professional behavior."
                    }
                ]
            }
        ],
        // subCourseStructure: [
        //   {
        //     title: "Course Syllabus",
        //     subjects: [
        //       "Basic Aeronautical Knowledge",
        //       "Aviation Physiology",
        //       "Ophthalmology",
        //       "Otorhinolaryngology",
        //       "Cardiovascular System",
        //       "Gynaecology & Obstetrics",
        //       "Psychiatry in Aviation",
        //       "Legislation, Rules and Regulations",
        //       "Air Ambulance Operations",
        //       "Medical Tourism",
        //     ],
        //   },
        // ],
        practicalTrainingData: [],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Age & Height Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Age: 18–27 Years ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 861,
                            columnNumber: 30
                        }, this),
                        "Girls: Min 5'2\" (Air Hostess)",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 862,
                            columnNumber: 42
                        }, this),
                        "Boys: Min 5'7\" (Flight Purser)"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 860,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Educational Qualification",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "10+2 (Any Stream) – For Domestic",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 873,
                            columnNumber: 45
                        }, this),
                        "Graduate (Any Stream) – For International"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 872,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Language Skills",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Must be able to read, speak, write, and understand English & Hindi",
                        "\n",
                        "Foreign Language: Added Advantage (For International Airlines)"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 883,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Medical Fitness",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center mt-2",
                    children: "Candidate must be medically fit as per airline standards."
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 894,
                    columnNumber: 11
                }, this)
            }
        ],
        jobAssistanceData: [
            {
                title: "Job Placement Assistance for Cabin Crew",
                desc: "Successful students holding the Diploma in In-Flight Management for Cabin Crew awarded by The Skyline Aviation Club will be groomed and assisted for airline job interviews and written tests. This includes placement support for both domestic airlines in India and international airlines abroad."
            }
        ]
    },
    {
        id: 7,
        slug: "airport-ground-staff-course",
        courseName: "Airport Ground Services",
        courseDesc: "Airport Ground Staff Course",
        introduction: "If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.",
        careerProspects: [
            {
                title: "Competitive Salaries",
                description: "Earn up to ₹20,000 per month with domestic airlines and up to ₹50,000 per month with international airlines."
            },
            {
                title: "High Flying Career",
                description: "Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."
            },
            {
                title: "Respect among Peers",
                description: "Gain recognition and admiration among peers and society as a skilled and accomplished aviator."
            },
            {
                title: "Fly all over the World",
                description: "Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."
            }
        ],
        theoryTrainingData: [
            {
                title: "Women Empowerment Scheme",
                description: "25% Scholarship for girls & PWFA members by The Skyline Aviation Club"
            },
            {
                title: "Jai Jawan Scheme",
                description: "25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"
            },
            {
                title: "Job Placement Assistance",
                description: "Successful Students holding requisite aviation qualification i.e Diploma in In-Flight Management for Cabin Crew received from The Skyline Aviaiton Club will be groomed and assisted for Airlilne Job Interviews and Written Tests for Domestic airlines in India and International Airlines Abroad"
            }
        ],
        courseStructure: [
            {
                title: "Airport Ground Staff",
                courseDuration: "3 Months",
                parts: [
                    {
                        title: "Yoga",
                        description: "Enhances flexibility and mental calmness."
                    },
                    {
                        title: "Meditation",
                        description: "Improves focus and stress management."
                    },
                    {
                        title: "Aviation Security, Geography with respect to all airports in india. Airline and airport codes.",
                        description: "Trains in airport and aircraft security measures."
                    },
                    {
                        title: "Reading Body Language",
                        description: "Interpreting passenger behavior for better service and safety."
                    },
                    {
                        title: "Communication Skills",
                        description: "Effective interaction with passengers and crew."
                    },
                    {
                        title: "Aircraft Technical Knowledge",
                        description: "Basic understanding of aircraft mechanics and systems."
                    },
                    {
                        title: "Emergency Equipment on Board the Aircraft",
                        description: "Knowledge of emergency tools and their use."
                    },
                    {
                        title: "Security Measures to Check Hijacking and Bomb",
                        description: "Protocols to manage security threats."
                    },
                    {
                        title: "Beauty and Perfect Airhostess or Flight Purser",
                        description: "Training on grooming and professionalism."
                    },
                    {
                        title: "First Aid Airport Announcements, Passenger handling, issue of boarding pass.",
                        description: "Basic medical assistance for passengers and crew."
                    },
                    {
                        title: "Etiquette and Manners (Finishing School)",
                        description: "Refinement of interpersonal and professional behavior."
                    }
                ]
            }
        ],
        practicalTrainingData: [],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Age & Height Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Minimum Age: 18 Years",
                        "\n",
                        "Maximum Age: 27 Years",
                        "\n",
                        "Girls: Min 5'2\" (Air Hostess)",
                        "\n",
                        "Boys: Min 5'7\" (Flight Purser)"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1017,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Educational Qualification",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "10+2 (Any Stream) – Domestic Airlines",
                        "\n",
                        "Graduate (Any Stream) – International Airlines"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1030,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Language Proficiency",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Must be able to read, write, speak & understand",
                        "\n",
                        "English and Hindi",
                        "\n",
                        "Foreign Language – Added Advantage for Graduates"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1041,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Medical & Other Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Medically Fit",
                        "\n",
                        "Must understand liability for surface or collision-related damages"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1053,
                    columnNumber: 11
                }, this)
            }
        ]
    },
    {
        id: 8,
        slug: "aviation-foundation-course",
        courseName: "SUMMER VACATION TRAINING PROGRAM",
        courseDesc: "Aviation Foundation Course",
        introduction: "Aviation Foundation Course is specially designed for school and college students to create foundation for pursuing airline careers. Students will be guided towards various airline opportunities available i.e. Airline Pilot, Aircraft/Flight Dispatcher, Air hostess, Flight Purser, Ground Hostess, Traffic Assistant, Aeronautical Engineer, Aircraft Maintenance Engineer, Air Traffic Control Officer, Radio Officer.",
        careerProspects: [
            {
                title: "Benefits",
                description: "Young students have big dreams which change every day. This is a chance to get your child focused on what he or she loves. Students need not wait till 12th to decide a career path. One-stop complete aviation solution from zero to airline pilot. Early exposure to the aviation sector makes them much better prepared than other candidates."
            },
            {
                title: "Rewards",
                description: "Students will be issued a certificate of achievement on the completion of the course. Group photograph. Individual photograph with instructor and helicopter or aeroplane. Memento as Future Pilot. Airline Career Guidance Booklet."
            }
        ],
        theoryTrainingData: [],
        courseStructure: [
            {
                title: "Student & Private Pilot Licence Training",
                courseDuration: "Flexible (Introductory Program)",
                parts: [
                    {
                        title: "Part I – Theory Training (DGCA Syllabus)",
                        duration: "Covers core subjects as per DGCA (Govt. of India) guidelines"
                    },
                    {
                        title: "Subjects Covered",
                        duration: [
                            "Air Regulation",
                            "Aviation Meteorology",
                            "Air Navigation",
                            "Audio Visual Training",
                            "Aircraft and Engine",
                            "Radio Communication"
                        ].join(", ")
                    },
                    {
                        title: "Part II – Practical Orientation",
                        duration: "Includes familiarization briefing + discovery flight in helicopter/airplane for 15 minutes"
                    }
                ]
            }
        ],
        subCourseStructure: [
            {
                title: "Admission Procedure",
                subjects: [
                    "Fill out the online registration form.",
                    "Send the following documents by courier or submit personally:",
                    "– Two passport-size photographs",
                    "– Certified true copy of your school ID card"
                ]
            }
        ],
        practicalTrainingData: [],
        eligibilityData: [
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Who Can Apply?",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Students of Schools & Colleges",
                        "\n",
                        "Hobby Flyers Welcome",
                        "\n",
                        "Age: No Bar"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1126,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Course Duration",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "3 Weeks Duration",
                        "\n",
                        "3 Hours per Day"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1138,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Batch Timings",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Morning Batch: 10 AM – 1 PM",
                        "\n",
                        "Noon Batch: 2 PM – 5 PM"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1149,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Batch Size",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "20 Students per Batch"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1160,
                    columnNumber: 11
                }, this)
            }
        ]
    },
    {
        id: 9,
        slug: "two-day-aeroplane-or-helicopter-training-workshop",
        courseName: "Aeroplane/Helicopter Orientation Training",
        courseDesc: "Two Day Aeroplane/Helicopter Training Workshop",
        introduction: "Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter. They will be exposed at an early age to experience what it feels like to fly amongst the clouds. This course will open multiple career oppurtunities in the field of aviation.",
        careerProspects: [],
        theoryTrainingData: [
            {
                title: "Scholarship - Govt. of India",
                description: "Scholarship of ₹30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."
            }
        ],
        practicalTrainingData: [
            {
                title: "Educational Loan - Govt. of Gujarat",
                description: "Educational Loan of ₹25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Banks",
                description: "Up to 85% of course fees from affiliated Banks for General Category Students."
            },
            {
                title: "Success Track Record",
                description: "So far, 54 students have been successful in procuring these benefits."
            }
        ],
        courseStructure: [
            {
                title: "AEROPLANE/HELICOPTER TRAINING WORKSHOP",
                courseDuration: "2 Day",
                parts: [
                    {
                        title: "Theory Training",
                        duration: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                            children: [
                                "• How helicopters fly",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1209,
                                    columnNumber: 38
                                }, this),
                                "• Parts of a helicopter and their functions",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1210,
                                    columnNumber: 60
                                }, this),
                                "• Cockpit orientation",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1211,
                                    columnNumber: 38
                                }, this),
                                "• Live ATC communication monitoring",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1212,
                                    columnNumber: 52
                                }, this),
                                "• Audio-visual guide on Principles of Flight and History of Aviation",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1213,
                                    columnNumber: 85
                                }, this),
                                "• Aviation career guidance",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1214,
                                    columnNumber: 43
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 1208,
                            columnNumber: 15
                        }, this)
                    },
                    {
                        title: "Practical Experience",
                        duration: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                            children: [
                                "• 15-minute Helicopter Discovery Flight at Juhu Aerodrome",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1224,
                                    columnNumber: 76
                                }, this),
                                "• Full-time tour director",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1225,
                                    columnNumber: 44
                                }, this),
                                "• Travel insurance",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                    fileName: "[project]/src/constant/courseDetailData.jsx",
                                    lineNumber: 1226,
                                    columnNumber: 37
                                }, this),
                                "• Breakfast & Lunch included"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 1223,
                            columnNumber: 17
                        }, this)
                    }
                ]
            }
        ],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Eligibility Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Minimum age - 12 Years ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 1241,
                            columnNumber: 36
                        }, this),
                        "Minimum Class - VI onwards"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1240,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Dress Code",
                desc: "School Uniform with valid School ID"
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Batch Size",
                desc: "Minimum 74 students (batches may be combined if minimum not met)"
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Parental Consent",
                desc: "Discovery Flight Consent Form signed by parent"
            }
        ]
    },
    {
        id: 10,
        slug: "foreign-commercial-pilot-licence-conversion-course",
        courseName: "INDIAN COMMERCIAL PILOT LICENCE (DGCA)",
        courseDesc: "Foreign Commercial Pilot Licence Conversion Course",
        introduction: "The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.",
        careerProspects: [
            {
                title: "Competitive Salaries",
                description: "Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies."
            },
            {
                title: "Exciting and Dynamic Career",
                description: "Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."
            },
            {
                title: "Prestige and Respect",
                description: "Gain recognition and admiration among peers and society as a skilled and accomplished aviator."
            },
            {
                title: "Travel the World",
                description: "Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."
            }
        ],
        // theoryTrainingData: [
        //   {
        //     title: "Skyline UDAN Scholarship",
        //     description:
        //       "For economically weak students. Those students who are brilliant, capable and eligible to become Pilot but economically weak and are eligible for other Scholarship or Education Loan scheme of Government or Charitable Trusts for practical flight training course fees can be considered for grant of this scholarship.",
        //   },
        //   {
        //     title: "Skyline Women Empowerment Scholarship",
        //     description:
        //       "The Skyline Aviation Club is Corporate Member of Indian Women Pilot Association and supports IWPA and Government of India to promote “Beti Bachav, Beti Padhav” Campaign. To encourage young girls to take up aviation as career we have introduced this scholarship. This Scholarship is exclusively for Female Trainee Pilots who are members of Indian Women Pilots Association (IWPA)",
        //   },
        //   {
        //     title: "Skyline Jai Jawan Scholarship",
        //     description:
        //       "Founder of The Skyline Aviation Club Capt.AD Manek is ex NCC Cadet completed NCC training with rank of Sr. Under Officer in the year 1982 and even today he takes part in activities of NCC. To honor services of Capt.AD Manek Maharashtra State NCC Directorate have declared “Capt.AD Manek Scholarship” for Best Cadets of Maharashtra State from the year 2023. To encourage NCC Cadets and Sons /Daughters of Defence Personnels we have introduced this scholarship. This Scholarship is exclusively for NCC Cadets holding “B” or “C” Certificates and Sons, Daughters of Defence Personnels including Indian Army, Indian Navy, Indian Airforce, Coast Guard, and Border Security Force. (Note: Scholarship for Theory (Ground Training) Course will be granted on merit basis subject to score in Skyline Scholarship Examinations.)",
        //   },
        // ],
        // practicalTrainingData: [
        //   {
        //     title: "Educational Loan from Bank (Open for all)",
        //     description:
        //       "Educational Loan of 85 % of Total Course fees can be arranged through our affiliated bank viz. Axis Bank subject to eligibility criteria of student and parents",
        //   },
        //   {
        //     title: "Scholarship from TATA Trusts (Open for all)",
        //     description:
        //       "Students completing Ground Training successfully and passes all DGCA CPL Written Examinations and complete initial flight training may be considered for partial Scholarship by TATA Trusts to encourage youth to join and make career in aviation",
        //   },
        //   {
        //     title: "Indian Women Pilot Association (IWPA) Financial Assistance (Reserved for female trainee pilots)",
        //     description:
        //       "Female Students completing Ground Training successfully and passes all DGCA CPL Written Examinations and complete initial flight training may be considered for partial Scholarship by IWPA to encourage young girls to join and make career in aviation",
        //   },
        //   {
        //     title: "Post Matric Scholarship by Department of Social Justice & Empowerment, Government of India (Reserved for students of all states of India and belonging to Scheduled Caste.)",
        //     description:
        //       "Scholarship of Rs.45 Lacs available under Post Matric Scholarship Scheme of Department of Social Justice & Empowerment, Govt. of India for Students belonging to scheduled caste.",
        //   },
        //   {
        //     title: "Commercial Pilot Licence Educational Loan by Government of Gujarat (Reserved for students of Gujarat State and belonging to Scheduled Caste or Scheduled Tribe or Other Backward Class)",
        //     description:
        //       "Educational loan of Rs.25 Lacs available under Commercial Pilot Licence loan Scheme of Department of Social Justice & Empowerment, Government of Gujarat",
        //   },
        //   {
        //     title: "Videsh Abhiyash Yojna Educational Loan by Government of Gujarat (Reserved for students of Gujarat State and belonging to Scheduled Caste or Scheduled Tribe or Other Backward Class)",
        //     description:
        //       "Educational loan of Rs.15 Lacs available under Videsh Abhiyash Yojna loan Scheme of Department of Social Justice & Empowerment, Government of Gujarat. (This loan can be used for FAA Flight / Aircraft Dispatcher Course in USA)",
        //   },
        // ],
        theoryTrainingData: [
            {
                title: "Women Empowerment Scheme",
                description: "50% scholarship for girls & PWFA members by The Skyline Aviation Club"
            },
            {
                title: "Jai Jawan Scheme",
                description: "50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"
            }
        ],
        practicalTrainingData: [
            {
                title: "Scholarship - Govt. of India",
                description: "Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Govt. of Gujarat",
                description: "Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."
            },
            {
                title: "Educational Loan - Banks",
                description: "Up to 85% of course fees from affiliated banks for general category students."
            }
        ],
        courseStructure: [
            {
                title: "Foreign Pilot Licence Conversion Course (DGCA) - Theory Course",
                courseDuration: "2 months",
                parts: [
                    {
                        title: "Prepares for DGCA Commercial Pilot Licence conversion Exams",
                        duration: "",
                        papers: [
                            "Paper 1 - Composite Paper (Air navigation & Air Meteorology)",
                            "Paper 2 - Air Regulations"
                        ]
                    },
                    {
                        title: "Prepare for WPC, Ministry of Telecommunication Exam",
                        duration: "",
                        papers: [
                            "Part I: Radio Communication Procedures",
                            "Part II: Radio Theory"
                        ]
                    }
                ]
            }
        ],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Age Criteria",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "Minimum age: Age 12+ years ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 1405,
                            columnNumber: 40
                        }, this),
                        "Minimum class: VI onwards"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1404,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Education",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: [
                        "10+2 with Maths and Physics ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 1416,
                            columnNumber: 41
                        }, this),
                        " or",
                        " ",
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-textBluePrimary",
                            children: "Higher Education*"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/courseDetailData.jsx",
                            lineNumber: 1417,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1415,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Personality",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "Be able to read, write, speak and understand the English language"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1426,
                    columnNumber: 11
                }, this)
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Medical",
                desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",
                    children: "DGCA Medical Class 2 Certificate (CA35)"
                }, void 0, false, {
                    fileName: "[project]/src/constant/courseDetailData.jsx",
                    lineNumber: 1436,
                    columnNumber: 11
                }, this)
            }
        ],
        shortNote: "Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad under Airline Prep Course.  So far we have helped 720 plus students to get Pilot Jobs in various airlines",
        jobAssistanceData: [
            {
                title: "Comprehensive Career Grooming",
                desc: "Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement"
            },
            {
                title: "Global Job Opportunities",
                desc: " We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry."
            },
            {
                title: "Proven Success",
                desc: "So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !"
            }
        ]
    },
    {
        id: 11,
        slug: "helicopter-commercial-pilot-licence-course",
        courseName: "HCPL",
        courseDesc: "Helicopter Commercial Pilot Licence Course",
        introduction: "The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.",
        careerProspects: [
            {
                title: "Professional Opportunities",
                description: "Work with private helicopter operators, tourism companies, charter services, and emergency medical services (HEMS)."
            },
            {
                title: "Government and Defense Roles",
                description: "Opportunities in government agencies, paramilitary forces, and search & rescue operations."
            },
            {
                title: "Corporate and VIP Transport",
                description: "Serve as pilot for high-profile individuals, corporate executives, and political dignitaries."
            }
        ],
        theoryTrainingData: [],
        practicalTrainingData: [],
        courseStructure: [
            {
                title: "HCPL Training Program",
                courseDuration: "12 to 18 Months",
                parts: [
                    {
                        title: "Part I - Theory Training",
                        duration: "Covers DGCA syllabus including Air Navigation, Aviation Meteorology, Air Regulations, Aircraft & Engines (Technical), and Radio Telephony."
                    },
                    {
                        title: "Part II - Practical Flight Training",
                        duration: "Minimum of 150 hours of helicopter flying at an approved FTO under instructor supervision."
                    }
                ]
            }
        ],
        eligibilityData: [
            {
                image: "/assets/ageCriteriaImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Age Criteria",
                desc: "Minimum 18 years of age at the time of licence issue"
            },
            {
                image: "/assets/educationImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Education",
                desc: "10+2 with Physics and Mathematics or equivalent (NIOS acceptable)"
            },
            {
                image: "/assets/medicalImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Medical",
                desc: "Class II Medical for training & Class I Medical for licence issuance"
            },
            {
                image: "/assets/personalityImage.png",
                class: "w-16 h-20 md:w-20 md:h-14 ",
                title: "Language Proficiency",
                desc: "Must be able to read, write, speak and understand English"
            }
        ]
    }
];
;
}}),
"[project]/src/components/common/Footer.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$navbarLinks$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/constant/navbarLinks.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$courseDetailData$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/constant/courseDetailData.jsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const Footer = ()=>{
    const currentYear = new Date().getFullYear();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
        className: "bg-footerBG px-2 py-4 md:px-6 lg:px-12 xl:px-16",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col xl:flex-row gap-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-start lg:items-start text-start lg:text-start w-full xl:w-1/4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                src: "/skyline_logo.png",
                                alt: "The Skyline Aviation Club Logo",
                                width: 140,
                                height: 130,
                                className: "mb-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 17,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-xl font-bold text-footerPrimaryText mb-2",
                                children: [
                                    "The Skyline Aviation Club ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "align-super text-sm",
                                        children: "®"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 25,
                                        columnNumber: 39
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 24,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs font-bold text-footerPrimaryText",
                                children: [
                                    "Since 1987, An Aviation Educational & Charitable Trust. ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 28,
                                        columnNumber: 69
                                    }, this),
                                    "Registered By Government."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 27,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-3 mt-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "https://www.instagram.com/skylineaviationofficial/profilecard/?igsh=MTNuZzZiODdjc3NrNQ==",
                                        className: "bg-gray-300 p-3 rounded-full",
                                        "aria-label": "Instagram",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/instagram.png",
                                            alt: "Instagram",
                                            width: 20,
                                            height: 20
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/Footer.jsx",
                                            lineNumber: 33,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 32,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "https://youtube.com/@captadmanek?feature=shared",
                                        className: "bg-gray-300 p-3 rounded-full",
                                        "aria-label": "YouTube",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/youtube.png",
                                            alt: "YouTube",
                                            width: 20,
                                            height: 20
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/Footer.jsx",
                                            lineNumber: 36,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 35,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "https://www.linkedin.com/in/capt-dr-ad-manek-547722134?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app",
                                        className: "bg-gray-300 p-3 rounded-full",
                                        "aria-label": "LinkedIn",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/linkedin.png",
                                            alt: "LinkedIn",
                                            width: 20,
                                            height: 20
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/Footer.jsx",
                                            lineNumber: 39,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 38,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "https://www.facebook.com/share/1CJ9ntLvHa/?mibextid=wwXIfr",
                                        className: "bg-gray-300 p-3 rounded-full",
                                        "aria-label": "Facebook",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                            src: "/facebook.png",
                                            alt: "Facebook",
                                            width: 20,
                                            height: 20
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/Footer.jsx",
                                            lineNumber: 42,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 41,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 31,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Footer.jsx",
                        lineNumber: 16,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col md:flex-row lg:flex-row xl:flex-row gap-8 w-full xl:w-3/4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-bold mb-2 text-footerPrimaryText",
                                        children: "Courses"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 52,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "space-y-1",
                                        children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$navbarLinks$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["coursesLinks"].map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "capitalize",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: `/course/${item.link}`,
                                                    className: "text-black text-sm font-semibold capitalize",
                                                    children: item.text
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 56,
                                                    columnNumber: 19
                                                }, this)
                                            }, index, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 55,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 53,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 51,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-bold mb-2 text-footerPrimaryText",
                                        children: "Contact Info"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 66,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "space-y-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "tel:+919820891262",
                                                    className: "text-black text-sm font-semibold",
                                                    children: "+91 9820891262"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 69,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 68,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "tel:+919820896262",
                                                    className: "text-black text-sm font-semibold",
                                                    children: "+91 9820896262"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 74,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 73,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "tel:+912228983516",
                                                    className: "text-black text-sm font-semibold",
                                                    children: "+91 2228983516"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 79,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 78,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "mailto:<EMAIL>",
                                                    className: "text-black text-sm font-semibold whitespace-nowrap",
                                                    children: "Email: <EMAIL>"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/Footer.jsx",
                                                    lineNumber: 84,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 83,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "text-black text-sm font-semibold whitespace-nowrap",
                                                children: "Amateur Radio Call Sign: VU2 ADO"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 91,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 67,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 65,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1 hidden md:block",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-bold mb-2 text-footerPrimaryText",
                                        children: "Address"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 99,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-black text-sm font-bold",
                                        children: [
                                            "The Skyline Aviation Club, ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 101,
                                                columnNumber: 42
                                            }, this),
                                            "1st Floor, Shivam Apartment,",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 102,
                                                columnNumber: 43
                                            }, this),
                                            "Diagonally Opp. Joggers Park,",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 103,
                                                columnNumber: 44
                                            }, this),
                                            "Beside Simpoli Metro Station, Chikuwadi,",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                fileName: "[project]/src/components/common/Footer.jsx",
                                                lineNumber: 104,
                                                columnNumber: 55
                                            }, this),
                                            "Borivali West, Mumbai - 400092."
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 100,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 98,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Footer.jsx",
                        lineNumber: 48,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "md:hidden mt-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-bold mb-2 text-footerPrimaryText",
                                children: "Address"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 112,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-black text-sm font-bold",
                                children: [
                                    "The Skyline Aviation Club, ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 114,
                                        columnNumber: 40
                                    }, this),
                                    "1st Floor, Shivam Apartment,",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 115,
                                        columnNumber: 41
                                    }, this),
                                    "Diagonally Opp. Joggers Park,",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 116,
                                        columnNumber: 42
                                    }, this),
                                    "Beside Simpoli Metro Station, Chikuwadi,",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                        fileName: "[project]/src/components/common/Footer.jsx",
                                        lineNumber: 117,
                                        columnNumber: 53
                                    }, this),
                                    "Borivali West, Mumbai - 400092."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/Footer.jsx",
                                lineNumber: 113,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Footer.jsx",
                        lineNumber: 111,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Footer.jsx",
                lineNumber: 13,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                div: true,
                className: "mt-6 text-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-footerBottomText text-xs font-medium",
                    children: [
                        "© ",
                        currentYear,
                        " The Skyline Aviation Club. All Rights Reserved."
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/Footer.jsx",
                    lineNumber: 125,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/Footer.jsx",
                lineNumber: 124,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Footer.jsx",
        lineNumber: 12,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Footer;
}}),
"[project]/src/components/common/Navbar.jsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/common/Navbar.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/Navbar.jsx <module evaluation>", "default");
}}),
"[project]/src/components/common/Navbar.jsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/components/common/Navbar.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/Navbar.jsx", "default");
}}),
"[project]/src/components/common/Navbar.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Navbar$2e$jsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/common/Navbar.jsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Navbar$2e$jsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/common/Navbar.jsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Navbar$2e$jsx__$28$client__proxy$29$__);
}}),
"[project]/src/components/ui/toaster.jsx (client proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Toaster = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/toaster.jsx <module evaluation>", "Toaster");
}}),
"[project]/src/components/ui/toaster.jsx (client proxy)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Toaster = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/toaster.jsx", "Toaster");
}}),
"[project]/src/components/ui/toaster.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$jsx__$28$client__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/ui/toaster.jsx (client proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$jsx__$28$client__proxy$29$__ = __turbopack_import__("[project]/src/components/ui/toaster.jsx (client proxy)");
;
__turbopack_export_namespace__(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$jsx__$28$client__proxy$29$__);
}}),
"[project]/src/app/layout.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Footer$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/common/Footer.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Navbar$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/common/Navbar.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/toaster.jsx [app-rsc] (ecmascript)");
;
;
;
;
;
const metadata = {
    title: "The Skyline Aviation Club",
    description: "Your Pilot Journey Begins Here !",
    icons: {
        icon: "/assets/Favicon.png"
    }
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                    rel: "icon",
                    type: "image/png",
                    href: "/assets/Favicon.png"
                }, void 0, false, {
                    fileName: "[project]/src/app/layout.jsx",
                    lineNumber: 18,
                    columnNumber: 8
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/layout.jsx",
                lineNumber: 17,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: `relative antialiased      overflow-x-hidden`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Navbar$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/app/layout.jsx",
                        lineNumber: 25,
                        columnNumber: 9
                    }, this),
                    children,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                        fileName: "[project]/src/app/layout.jsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Footer$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                        fileName: "[project]/src/app/layout.jsx",
                        lineNumber: 28,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.jsx",
                lineNumber: 24,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/layout.jsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/layout.jsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_namespace__(__turbopack_import__("[project]/src/app/layout.jsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=src_9da838._.js.map