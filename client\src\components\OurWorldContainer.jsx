"use client";

import { useState, useRef, useEffect } from "react";
import { FaPlay, FaPause, FaVolumeUp, FaVolumeMute, FaExpand } from "react-icons/fa";

const OurWorldContainer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [player, setPlayer] = useState(null);
  const [isControlsVisible, setIsControlsVisible] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const videoContainerRef = useRef(null);
  const progressRef = useRef(null);
  const videoId = "PWk1ZdCGgjA";

  // Load YouTube API
  useEffect(() => {
    // Create script element for YouTube API
    const tag = document.createElement("script");
    tag.src = "https://www.youtube.com/iframe_api";
    const firstScriptTag = document.getElementsByTagName("script")[0];
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

    // Initialize player when API is ready
    window.onYouTubeIframeAPIReady = () => {
      new window.YT.Player("youtube-player", {
        videoId: videoId,
        playerVars: {
          autoplay: 0,
          controls: 0,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          fs: 0,
          iv_load_policy: 3,
          mute: 1
        },
        events: {
          onReady: (event) => {
            const ytPlayer = event.target;
            setPlayer(ytPlayer);
            setIsMuted(ytPlayer.isMuted());
            setDuration(ytPlayer.getDuration());
          },
          onStateChange: (event) => {
            setIsPlaying(event.data === window.YT.PlayerState.PLAYING);

            // Start progress tracking when video is playing
            if (event.data === window.YT.PlayerState.PLAYING) {
              startProgressTracking(event.target);
            }
          }
        }
      });
    };

    // Cleanup
    return () => {
      window.onYouTubeIframeAPIReady = null;
    };
  }, []);

  // Track video progress
  const startProgressTracking = (ytPlayer) => {
    const updateInterval = setInterval(() => {
      if (ytPlayer && typeof ytPlayer.getCurrentTime === 'function') {
        const current = ytPlayer.getCurrentTime();
        const total = ytPlayer.getDuration();
        setCurrentTime(current);
        setDuration(total);

        // Clear interval if video ended
        if (ytPlayer.getPlayerState() !== window.YT.PlayerState.PLAYING) {
          clearInterval(updateInterval);
        }
      }
    }, 1000);

    // Return cleanup function
    return () => clearInterval(updateInterval);
  };

  // Handle play/pause
  const handlePlayPause = () => {
    if (!player) return;

    if (isPlaying) {
      player.pauseVideo();
    } else {
      player.playVideo();
    }
    setIsPlaying(!isPlaying);
  };

  // Handle mute/unmute
  const handleMuteToggle = () => {
    if (!player) return;

    if (isMuted) {
      player.unMute();
    } else {
      player.mute();
    }
    setIsMuted(!isMuted);
  };

  // Handle fullscreen
  const handleFullscreen = () => {
    if (!videoContainerRef.current) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      videoContainerRef.current.requestFullscreen();
    }
  };

  // Handle seeking
  const handleSeek = (e) => {
    if (!player || !progressRef.current) return;

    const progressBar = progressRef.current;
    const rect = progressBar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const seekToTime = duration * clickPosition;

    player.seekTo(seekToTime, true);
    setCurrentTime(seekToTime);
  };

  // Format time (seconds to MM:SS)
  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // Show/hide controls on hover
  const showControls = () => setIsControlsVisible(true);
  const hideControls = () => setIsControlsVisible(false);

  return (
    <div className="container mx-auto p-4 text-center">
      <h3 className="font-bold text-xl mb-3 md:text-2xl lg:text-3xl xl:text-3xl text-textBluePrimary">
        A Glimpse Into Our World
      </h3>
      <p className="font-bold text-sm mb-6 md:text-sm lg:text-base xl:text-base text-customGrey100">
        Take a closer look at the world of
        The Skyline Aviation Club. <br />
        Experience our facilities, dedicated instructors, and the journey of
        <br /> shaping future aviation professionals.
      </p>

      {/* Video Container with Custom Controls */}
      <div
        ref={videoContainerRef}
        className="relative md:w-4/5 w-full mx-auto rounded-lg overflow-hidden shadow-lg"
        onMouseEnter={showControls}
        onMouseLeave={hideControls}
        onFocus={showControls}
        onBlur={hideControls}
        onTouchStart={showControls}
        role="region"
        aria-label="Video player"
      >
        {/* YouTube Player */}
        <div className="aspect-video bg-black">
          <div id="youtube-player" className="w-full h-full" aria-hidden="true"></div>
        </div>

        {/* Loading indicator */}
        {!player && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30">
            <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {/* Custom Controls */}
        <div
          className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 transition-opacity duration-300 ${isControlsVisible ? 'opacity-100' : 'opacity-0'}`}
          aria-hidden={!isControlsVisible}
        >
          {/* Progress Bar */}
          <div
            ref={progressRef}
            className="w-full h-1 bg-gray-600 rounded-full mb-3 cursor-pointer"
            onClick={handleSeek}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleSeek(e);
              }
            }}
            role="slider"
            aria-label="Video progress"
            aria-valuemin="0"
            aria-valuemax={duration}
            aria-valuenow={currentTime}
            tabIndex="0"
          >
            <div
              className="h-full bg-blue-500 rounded-full"
              style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
            ></div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Play/Pause Button */}
              <button
                onClick={handlePlayPause}
                className="text-white hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1"
                aria-label={isPlaying ? "Pause video" : "Play video"}
                title={isPlaying ? "Pause" : "Play"}
              >
                {isPlaying ? <FaPause /> : <FaPlay />}
              </button>

              {/* Mute/Unmute Button */}
              <button
                onClick={handleMuteToggle}
                className="text-white hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1"
                aria-label={isMuted ? "Unmute video" : "Mute video"}
                title={isMuted ? "Unmute" : "Mute"}
              >
                {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
              </button>

              {/* Time Display */}
              <span className="text-white text-xs">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            {/* Fullscreen Button */}
            <button
              onClick={handleFullscreen}
              className="text-white hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1"
              aria-label="Toggle fullscreen"
              title="Fullscreen"
            >
              <FaExpand />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OurWorldContainer