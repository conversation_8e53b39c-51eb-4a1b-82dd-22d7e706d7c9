{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/CourseDetailComponent.jsx"], "sourcesContent": ["\"use client\";\r\nimport Image from \"next/image\";\r\nimport {\r\n  courseData,\r\n} from \"../constant/courseDetailData\";\r\nimport Link from \"next/link\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { useState, useRef, useEffect } from \"react\";\r\n\r\nconst CourseDetailComponent = () => {\r\n\r\n  const [modalType, setModalType] = useState(null);\r\n  const modalRef = useRef(null);\r\n\r\n  const { slug } = useParams();\r\n  const course = courseData.find((course) => course.slug === slug);\r\n\r\n  if (!course) {\r\n    return <div>Course Not Found</div>;\r\n  }\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    function handleClickOutside(event) {\r\n      if (modalRef.current && !modalRef.current.contains(event.target)) {\r\n        setModalType(false);\r\n      }\r\n    }\r\n\r\n    if (modalType) {\r\n      document.addEventListener(\"mousedown\", handleClickOutside);\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, [modalType]);\r\n\r\n  // console.log(course);\r\n\r\n\r\n  return (\r\n    <>\r\n      <div className=\"relative bg-coursesDetailBanner bg-cover bg-no-repeat bg-[-45rem] md:bg-[-26rem] lg:bg-center h-[700px]\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]\"></div>\r\n        {course ? (\r\n          <div className=\"absolute left-0 right-0 bottom-28 md:bottom-32 mx-4 md:mx-20 text-center\">\r\n            <h1 className=\"font-extrabold text-whiteOne text-2xl md:text-6xl lg:text-6xl xl:text-6xl mb-4\">\r\n              {(course.courseDesc).toUpperCase()}\r\n            </h1>\r\n            <p className=\"text-formBG font-bold md:font-light text-xl md:text-3xl lg:text-2xl lg:w-[32rem] lg:mx-auto\">\r\n              {(course.courseName).toUpperCase()}\r\n            </p>\r\n          </div>\r\n        ) : (\r\n          \"\"\r\n        )}\r\n      </div>\r\n      <div className=\"relative z-0 bg-white\">\r\n        <div className=\"hidden lg:block z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute top-80 left-0 right-0 w-full h-[300px]\"></div>\r\n        <div className=\"hidden lg:block absolute bg-dotsImage top-36 right-0 bg-center bg-no-repeat w-10 h-36\"></div>\r\n        <div className=\"z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-0 lg:bottom-60 left-0 right-0 w-full h-[300px]\"></div>\r\n        <div className=\"hidden lg:block absolute bg-dotsImage top-[48rem] left-0 bg-center bg-no-repeat w-10 h-36\"></div>\r\n        <div className=\"py-4 px-4 lg:px-12 xl:px-32 relative z-10\">\r\n          <div className=\"absolute left-0 right-0 -top-20 mx-4 text-center p-3 md:p-4 lg:p-8 lg:mx-36 xl:p-8 rounded-xl shadow-courseCardShadow bg-white\">\r\n            <h3 className=\"text-textBluePrimary mb-2 font-bold text-xl md:text-2xl xl:text-3xl lg:text-3xl\">\r\n              Introduction\r\n            </h3>\r\n            <p className=\"descLightBlack text-xs leading-5\r\n             lg:text-base xl:text-base font-medium lg:mx-32\">\r\n              {course.introduction}\r\n            </p>\r\n          </div>\r\n          <div className=\"shadow-cardButtonShadow lg:mt-32 xl:mt-44 md:mt-32 mt-24 p-4 md:shadow-none mb-8\">\r\n            <h3 className=\"text-textBluePrimary text-center mb-4 md:mb-6 font-bold text-xl md:text-2xl lg:text-3xl\">\r\n              Eligibility\r\n            </h3>\r\n            <div\r\n              className={`grid gap-2 md:gap-4 lg:gap-6 ${course.eligibilityData.length === 3\r\n                ? 'grid-cols-1 sm:grid-cols-3'\r\n                : 'grid-cols-2 md:grid-cols-4'\r\n                } w-full`}\r\n            >\r\n              {/* <div className=\"flex flex-wrap w-full gap-2 md:gap-4 lg:gap-6\"> */}\r\n              {course.eligibilityData.map((item, index) => (\r\n                <div\r\n                  key={index}\r\n                  className=\"flex flex-col items-center p-2 md:p-4 rounded-xl shadow-xl bg-white\"\r\n                >\r\n                  <Image\r\n                    src={item.image}\r\n                    alt={`${item.title} Icon`}\r\n                    className={`${item.class} mb-2 md:lg-4`}\r\n                    width={100}\r\n                    height={100}\r\n                  />\r\n                  <h3 className=\"font-semibold text-center text-sm md:text-base text-neutralGrayText\">\r\n                    {item.title}\r\n                  </h3>\r\n                  {item.title === 'Education' ? (\r\n                    <>\r\n                      <span className=\"text-center text-sm md:text-base text-neutralGrayText\">\r\n                        {item.desc}\r\n                      </span>\r\n                      <button\r\n                        onClick={() => setModalType(\"Education\")}\r\n                        className=\"mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm\"\r\n                      >\r\n                        Note\r\n                      </button>\r\n                    </>\r\n                  ) : item.title === 'Medical' ? (\r\n                    <>\r\n                      <span className=\"text-center text-sm md:text-base text-neutralGrayText\">\r\n                        {item.desc}\r\n                      </span>\r\n                      <button\r\n                        onClick={() => setModalType(\"Medical\")}\r\n                        className=\"mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm\"\r\n                      >\r\n                        Note\r\n                      </button>\r\n                    </>\r\n                  ) : (\r\n                    <p className=\"text-center text-sm md:text-base text-neutralGrayText\">\r\n                      {item.desc}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              ))}\r\n              {modalType && (\r\n                <div className=\"fixed inset-0 flex items-center justify-center z-50 px-4 md:px-6\" style={{ background: \"rgba(0,0,0,0.3)\" }}>\r\n                  <div\r\n                    ref={modalRef}\r\n                    className={`bg-white rounded-xl p-4 md:p-6 shadow-lg relative ${\r\n                      modalType === \"Scholarship\"\r\n                        ? \"max-w-4xl w-full max-h-[90vh] overflow-y-auto\"\r\n                        : \"max-w-md\"\r\n                    }`}\r\n                  >\r\n                    <button\r\n                      onClick={() => setModalType(null)}\r\n                      className=\"absolute right-4 top-4 text-gray-500 hover:text-gray-700 text-xl font-bold z-10\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n\r\n                    {modalType === \"Scholarship\" ? (\r\n                      <div>\r\n                        <h2 className=\"text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary mb-4 md:mb-6 text-center md:text-left\">\r\n                          Scholarship Details\r\n                        </h2>\r\n\r\n                        {course.theoryTrainingData.length > 0 && (\r\n                          <div className=\"mb-6\">\r\n                            <h3 className=\"font-bold text-lg md:text-xl text-customBlack mb-3\">\r\n                              Scholarships for Theory Training\r\n                            </h3>\r\n                            <ul className=\"space-y-3\">\r\n                              {course.theoryTrainingData.map((item, index) => (\r\n                                <li key={index} className=\"border-b border-gray-200 pb-3 last:border-b-0\">\r\n                                  <h4 className=\"text-base md:text-lg font-semibold text-customBlack mb-2\">\r\n                                    {item.title}\r\n                                  </h4>\r\n                                  <p className=\"font-medium text-sm md:text-base text-gray-700\">\r\n                                    {item.description}\r\n                                  </p>\r\n                                </li>\r\n                              ))}\r\n                            </ul>\r\n                          </div>\r\n                        )}\r\n\r\n                        {course.practicalTrainingData.length > 0 && (\r\n                          <div>\r\n                            <h3 className=\"font-bold text-lg md:text-xl text-customBlack mb-3\">\r\n                              Scholarships for Practical Flight Training\r\n                            </h3>\r\n                            <ul className=\"space-y-3\">\r\n                              {course.practicalTrainingData.map((item, index) => (\r\n                                <li key={index} className=\"border-b border-gray-200 pb-3 last:border-b-0\">\r\n                                  <h4 className=\"text-base md:text-lg font-semibold text-customBlack mb-2\">\r\n                                    {item.title}\r\n                                  </h4>\r\n                                  <p className=\"font-medium text-sm md:text-base text-gray-700\">\r\n                                    {item.description}\r\n                                  </p>\r\n                                </li>\r\n                              ))}\r\n                            </ul>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ) : (\r\n                      <>\r\n                        <h2 className=\"text-lg font-semibold mb-2\">Important Note</h2>\r\n                        {modalType === \"Education\" && (\r\n                          <p className=\"text-sm text-gray-700\">\r\n                            If not from science background, then you require to appear and pass\r\n                            Physics and Mathematics subjects of 12th Level conducted by NIOS, New\r\n                            Delhi. Students can pursue our training program and simultaneously\r\n                            appear for above mentioned exams. Please contact us for further\r\n                            information.\r\n                          </p>\r\n                        )}\r\n\r\n                        {modalType === \"Medical\" && (\r\n                          <p className=\"text-sm text-gray-700\">\r\n                            We Will arrange Medical Examination with our panel doctor who is DGCA approved Class II Medical Examiner\r\n                          </p>\r\n                        )}\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n            </div>\r\n          </div>\r\n          <div className=\"flex gap-4 mb-8 flex-col md:flex-row\">\r\n            <div className=\"bg-white shadow-courseCardShadow p-4 md:p-6 rounded-[20px] md:basis-2/5\">\r\n              <h2 className=\"text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center md:text-left lg:text-left xl:text-left font-bold text-textBluePrimary mb-4\">\r\n                Course Structure\r\n              </h2>\r\n              <ul className=\"space-y-4 md:pl-3 md:marker:text-2xl marker:font-bold md:list-decimal\">\r\n                {course?.courseStructure && course.courseStructure.map((item, index) => (\r\n                  <li key={`course-${index}`} className=\"md:ml-4\">\r\n                    <h3 className=\"font-bold text-base lg:text-2xl text-customBlack mb-2\">\r\n                      {item.title}\r\n                    </h3>\r\n                    <p className=\"font-bold text-base lg:text-xl text-customBlack flex  gap-1 mb-2\">\r\n                      Course Duration:\r\n                      <span className=\"font-medium text-base md:text-xl\">\r\n                        {item.courseDuration}\r\n                      </span>\r\n                    </p>\r\n                    {item.parts.map((item, index) => (\r\n                      <>\r\n                        <p\r\n                          key={index}\r\n                          className=\"font-normal text-baxe lg:text-lg text-customBlack flex flex-col gap-1 mb-2\"\r\n                        >\r\n                          {item.title}\r\n                          <span className=\"font-medium text-base md:text-lg\">\r\n                            {item.duration}\r\n                          </span>\r\n                        </p>\r\n                        {item.papers && (\r\n                          <ul className=\"space-y-1\">\r\n                            {item.papers.map((paper, idx) => (\r\n                              <li\r\n                                key={idx}\r\n                                className=\"font-small text-base md:text-lg\"\r\n                              >\r\n                                {paper}\r\n                              </li>\r\n                            ))}\r\n                          </ul>\r\n                        )}\r\n                      </>\r\n                    ))}\r\n                    {course.subCourseStructure &&\r\n                      course.subCourseStructure.map((item, index) => (\r\n                        <div key={index}>\r\n                          <h3 className=\"font-bold text-base lg:text-xl text-customBlack flex flex-col gap-1 mb-2\">\r\n                            {item.title}\r\n                          </h3>\r\n                          {item.subjects && (\r\n                            <ul className=\"space-y-1\">\r\n                              {item.subjects.map((subject, idx) => (\r\n                                <li\r\n                                  key={idx}\r\n                                  className=\"font-medium text-base md:text-lg\"\r\n                                >\r\n                                  {subject}\r\n                                </li>\r\n                              ))}\r\n                            </ul>\r\n                          )}\r\n                          {item.licence && (\r\n                            <h4 className=\"font-bold text-base lg:text-lg text-customBlack flex flex-col gap-1 mb-2\">\r\n                              {item.licence}\r\n                            </h4>\r\n                          )}\r\n                          {item.parts && (\r\n                            <ul className=\"space-y-1\">\r\n                              {item.parts.map((part, idx) => (\r\n                                <li\r\n                                  key={idx}\r\n                                  className=\"font-medium text-base md:text-lg\"\r\n                                >\r\n                                  {part}\r\n                                </li>\r\n                              ))}\r\n                            </ul>\r\n                          )}\r\n                        </div>\r\n                      ))}\r\n\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n              {course.visaAsistant && (\r\n                <>\r\n                  <h3 className=\"font-bold text-base text-customBlack mb-2\">\r\n                    Student Visa Assistant\r\n                  </h3>\r\n                  <p className=\"mt-4 font-medium text-md text-customBlack mb-4\">\r\n                    {course.visaAsistant}\r\n                  </p>\r\n                </>\r\n              )}\r\n              {course.shortNote && (\r\n                <p className=\"mt-4 font-medium text-md text-customBlack mb-4\">\r\n                  {course.shortNote}\r\n                </p>\r\n              )}\r\n              {/* <p className=\"mt-4 font-medium text-md text-customBlack italic\">\r\n                **If not from science background, then you require to appear and\r\n                pass Physics and Mathematics subjects of 12th Level conducted by\r\n                NIOS, New Delhi. Students can pursue our training program and\r\n                simultaneously appear for above mentioned exams. Please contact\r\n                us for further information.\r\n              </p> */}\r\n              <Link\r\n                href=\"/enquire\"\r\n                className=\"block text-center bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 mt-3 lg:text-lg rounded-full border border-transparent\"\r\n              >\r\n                Enquire Now\r\n              </Link>\r\n            </div>\r\n\r\n\r\n\r\n            {/* Scholarship */}\r\n            <div className=\"flex flex-col gap-6 md:basis-3/5\">\r\n              {(course.theoryTrainingData.length ||\r\n                course.practicalTrainingData.length) > 0 && (\r\n                  <div className=\"bg-white shadow-lg p-6 rounded-[20px]\">\r\n                    <h2 className=\"text-center md:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4\">\r\n                      Scholarship\r\n                    </h2>\r\n                    <div>\r\n                      {course.theoryTrainingData.length > 0 && (\r\n                        <>\r\n                          <h3 className=\"font-bold text-lg text-customBlack mb-2\">\r\n                            Scholarships for Theory Training\r\n                          </h3>\r\n                          <ul className=\"space-y-2\">\r\n                            {course.theoryTrainingData.slice(0, 2).map((item, index) => (\r\n                              <li key={index}>\r\n                                <h3 className=\" text-base font-semibold text-customBlack mb-1\">\r\n                                  {item.title}\r\n                                </h3>\r\n                                <p className=\"font-medium text-xs lg:text-sm\">\r\n                                  {item.description}\r\n                                </p>\r\n                              </li>\r\n                            ))}\r\n                          </ul>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                    {course.practicalTrainingData.length > 0 && (\r\n                      <div className=\"mt-4\">\r\n                        <h3 className=\"font-bold text-lg text-customBlack mb-2\">\r\n                          Scholarships for Practical Flight Training\r\n                        </h3>\r\n                        <ul className=\"space-y-2\">\r\n                          {course.practicalTrainingData.slice(0, 2).map((item, index) => (\r\n                            <li key={index}>\r\n                              <h3 className=\" text-base font-semibold text-customBlack mb-1\">\r\n                                {item.title}\r\n                              </h3>\r\n                              <p className=\"font-medium text-xs lg:text-sm\">\r\n                                {item.description}\r\n                              </p>\r\n                            </li>\r\n                          ))}\r\n                        </ul>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* More button */}\r\n                    {((course.theoryTrainingData.length > 2) || (course.practicalTrainingData.length > 2)) && (\r\n                      <div className=\"mt-4 text-center\">\r\n                        <button\r\n                          onClick={() => setModalType(\"Scholarship\")}\r\n                          className=\"text-textBluePrimary hover:text-blue-700 font-semibold text-sm md:text-base underline\"\r\n                        >\r\n                          ...more\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n              {/* Career Prospects */}\r\n              {course.careerProspects.length > 0 && (\r\n                <div className=\"bg-white shadow-lg p-6 rounded-[20px] col-span-full\">\r\n                  <h2 className=\"text-center md:text-left lg:text-left xl:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4\">\r\n                    Career Prospects\r\n                  </h2>\r\n                  <ul className=\"space-y-1 md:space-y-2\">\r\n                    {course.careerProspects.map((item, index) => (\r\n                      <li key={index}>\r\n                        <h3 className=\"font-semibold text-base lg:text-xl text-customBlack mb-1 md:mb-2\">\r\n                          {item.title}\r\n                        </h3>\r\n                        <p className=\"font-medium text-xs lg:text-sm\">\r\n                          {item.description}\r\n                        </p>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n          </div>\r\n          {/* Job Placement */}\r\n          {\r\n            course.jobAssistanceData &&\r\n            <div className=\"bg-white shadow-courseCardShadow rounded-lg p-4 md:p-6\">\r\n              <h3 className=\"mb-2 md:mb-6 text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center\">\r\n                Job Placement Assistance\r\n              </h3>\r\n              <div className=\"flex mx-auto flex-col gap-4\">\r\n                {course.jobAssistanceData && course.jobAssistanceData.map((item, index) => (\r\n                  <div className=\"flex flex-col gap-1\" key={`job-${index}`}>\r\n                    <h4 className=\"text-sm md:text-base lg:text-xl text-customBlack font-bold\">\r\n                      {item.title}\r\n                    </h4>\r\n                    <p className=\"text-xs md:text-sm lg:text-base text-customBlack font-semibold\">\r\n                      {item.desc}\r\n                    </p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          }\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default CourseDetailComponent;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAGA;AACA;AACA;;;AAPA;;;;;;AASA,MAAM,wBAAwB;;IAE5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,SAAS,uIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,IAAI,KAAK;IAE3D,IAAI,CAAC,QAAQ;QACX,qBAAO,6LAAC;sBAAI;;;;;;IACd;IAIA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,SAAS,mBAAmB,KAAK;gBAC/B,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;oBAChE,aAAa;gBACf;YACF;YAEA,IAAI,WAAW;gBACb,SAAS,gBAAgB,CAAC,aAAa;YACzC;YAEA;mDAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;0CAAG;QAAC;KAAU;IAEd,uBAAuB;IAGvB,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;oBACd,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,AAAC,OAAO,UAAU,CAAE,WAAW;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CACV,AAAC,OAAO,UAAU,CAAE,WAAW;;;;;;;;;;;+BAIpC;;;;;;;0BAGJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkF;;;;;;kDAGhG,6LAAC;wCAAE,WAAU;kDAEV,OAAO,YAAY;;;;;;;;;;;;0CAGxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0F;;;;;;kDAGxG,6LAAC;wCACC,WAAW,CAAC,6BAA6B,EAAE,OAAO,eAAe,CAAC,MAAM,KAAK,IACzE,+BACA,6BACD,OAAO,CAAC;;4CAGV,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAK,KAAK,KAAK;4DACf,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC;4DACzB,WAAW,GAAG,KAAK,KAAK,CAAC,aAAa,CAAC;4DACvC,OAAO;4DACP,QAAQ;;;;;;sEAEV,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;wDAEZ,KAAK,KAAK,KAAK,4BACd;;8EACE,6LAAC;oEAAK,WAAU;8EACb,KAAK,IAAI;;;;;;8EAEZ,6LAAC;oEACC,SAAS,IAAM,aAAa;oEAC5B,WAAU;8EACX;;;;;;;2EAID,KAAK,KAAK,KAAK,0BACjB;;8EACE,6LAAC;oEAAK,WAAU;8EACb,KAAK,IAAI;;;;;;8EAEZ,6LAAC;oEACC,SAAS,IAAM,aAAa;oEAC5B,WAAU;8EACX;;;;;;;yFAKH,6LAAC;4DAAE,WAAU;sEACV,KAAK,IAAI;;;;;;;mDAvCT;;;;;4CA4CR,2BACC,6LAAC;gDAAI,WAAU;gDAAmE,OAAO;oDAAE,YAAY;gDAAkB;0DACvH,cAAA,6LAAC;oDACC,KAAK;oDACL,WAAW,CAAC,kDAAkD,EAC5D,cAAc,gBACV,kDACA,YACJ;;sEAEF,6LAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;sEACX;;;;;;wDAIA,cAAc,8BACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAuG;;;;;;gEAIpH,OAAO,kBAAkB,CAAC,MAAM,GAAG,mBAClC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAqD;;;;;;sFAGnE,6LAAC;4EAAG,WAAU;sFACX,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC;oFAAe,WAAU;;sGACxB,6LAAC;4FAAG,WAAU;sGACX,KAAK,KAAK;;;;;;sGAEb,6LAAC;4FAAE,WAAU;sGACV,KAAK,WAAW;;;;;;;mFALZ;;;;;;;;;;;;;;;;gEAahB,OAAO,qBAAqB,CAAC,MAAM,GAAG,mBACrC,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAqD;;;;;;sFAGnE,6LAAC;4EAAG,WAAU;sFACX,OAAO,qBAAqB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvC,6LAAC;oFAAe,WAAU;;sGACxB,6LAAC;4FAAG,WAAU;sGACX,KAAK,KAAK;;;;;;sGAEb,6LAAC;4FAAE,WAAU;sGACV,KAAK,WAAW;;;;;;;mFALZ;;;;;;;;;;;;;;;;;;;;;iFAcnB;;8EACE,6LAAC;oEAAG,WAAU;8EAA6B;;;;;;gEAC1C,cAAc,6BACb,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;gEAStC,cAAc,2BACb,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqI;;;;;;0DAGnJ,6LAAC;gDAAG,WAAU;0DACX,QAAQ,mBAAmB,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5D,6LAAC;wDAA2B,WAAU;;0EACpC,6LAAC;gEAAG,WAAU;0EACX,KAAK,KAAK;;;;;;0EAEb,6LAAC;gEAAE,WAAU;;oEAAmE;kFAE9E,6LAAC;wEAAK,WAAU;kFACb,KAAK,cAAc;;;;;;;;;;;;4DAGvB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB;;sFACE,6LAAC;4EAEC,WAAU;;gFAET,KAAK,KAAK;8FACX,6LAAC;oFAAK,WAAU;8FACb,KAAK,QAAQ;;;;;;;2EALX;;;;;wEAQN,KAAK,MAAM,kBACV,6LAAC;4EAAG,WAAU;sFACX,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,oBACvB,6LAAC;oFAEC,WAAU;8FAET;mFAHI;;;;;;;;;;;;4DAUhB,OAAO,kBAAkB,IACxB,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnC,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFACX,KAAK,KAAK;;;;;;wEAEZ,KAAK,QAAQ,kBACZ,6LAAC;4EAAG,WAAU;sFACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC3B,6LAAC;oFAEC,WAAU;8FAET;mFAHI;;;;;;;;;;wEAQZ,KAAK,OAAO,kBACX,6LAAC;4EAAG,WAAU;sFACX,KAAK,OAAO;;;;;;wEAGhB,KAAK,KAAK,kBACT,6LAAC;4EAAG,WAAU;sFACX,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,oBACrB,6LAAC;oFAEC,WAAU;8FAET;mFAHI;;;;;;;;;;;mEAzBL;;;;;;uDArCP,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;4CA4E7B,OAAO,YAAY,kBAClB;;kEACE,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAG1D,6LAAC;wDAAE,WAAU;kEACV,OAAO,YAAY;;;;;;;;4CAIzB,OAAO,SAAS,kBACf,6LAAC;gDAAE,WAAU;0DACV,OAAO,SAAS;;;;;;0DAUrB,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;kDAQH,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,OAAO,kBAAkB,CAAC,MAAM,IAChC,OAAO,qBAAqB,CAAC,MAAM,IAAI,mBACrC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2G;;;;;;kEAGzH,6LAAC;kEACE,OAAO,kBAAkB,CAAC,MAAM,GAAG,mBAClC;;8EACE,6LAAC;oEAAG,WAAU;8EAA0C;;;;;;8EAGxD,6LAAC;oEAAG,WAAU;8EACX,OAAO,kBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAChD,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FACX,KAAK,KAAK;;;;;;8FAEb,6LAAC;oFAAE,WAAU;8FACV,KAAK,WAAW;;;;;;;2EALZ;;;;;;;;;;;;;;;;;oDAalB,OAAO,qBAAqB,CAAC,MAAM,GAAG,mBACrC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA0C;;;;;;0EAGxD,6LAAC;gEAAG,WAAU;0EACX,OAAO,qBAAqB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACnD,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FACX,KAAK,KAAK;;;;;;0FAEb,6LAAC;gFAAE,WAAU;0FACV,KAAK,WAAW;;;;;;;uEALZ;;;;;;;;;;;;;;;;oDAchB,CAAC,AAAC,OAAO,kBAAkB,CAAC,MAAM,GAAG,KAAO,OAAO,qBAAqB,CAAC,MAAM,GAAG,CAAE,mBACnF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,SAAS,IAAM,aAAa;4DAC5B,WAAU;sEACX;;;;;;;;;;;;;;;;;4CASV,OAAO,eAAe,CAAC,MAAM,GAAG,mBAC/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAqI;;;;;;kEAGnJ,6LAAC;wDAAG,WAAU;kEACX,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,6LAAC;wEAAE,WAAU;kFACV,KAAK,WAAW;;;;;;;+DALZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAiBnB,OAAO,iBAAiB,kBACxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsG;;;;;;kDAGpH,6LAAC;wCAAI,WAAU;kDACZ,OAAO,iBAAiB,IAAI,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,IAAI;;;;;;;+CAL4B,CAAC,IAAI,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB1E;GArbM;;QAKa,qIAAA,CAAA,YAAS;;;KALtB;uCAubS"}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0]}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}