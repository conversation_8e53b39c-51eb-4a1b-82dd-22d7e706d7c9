{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/course/[slug]", "regex": "^/course/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/course/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/aboutus", "regex": "^/aboutus(?:/)?$", "routeKeys": {}, "namedRegex": "^/aboutus(?:/)?$"}, {"page": "/course", "regex": "^/course(?:/)?$", "routeKeys": {}, "namedRegex": "^/course(?:/)?$"}, {"page": "/enquire", "regex": "^/enquire(?:/)?$", "routeKeys": {}, "namedRegex": "^/enquire(?:/)?$"}, {"page": "/events", "regex": "^/events(?:/)?$", "routeKeys": {}, "namedRegex": "^/events(?:/)?$"}, {"page": "/faculty", "regex": "^/faculty(?:/)?$", "routeKeys": {}, "namedRegex": "^/faculty(?:/)?$"}, {"page": "/founder", "regex": "^/founder(?:/)?$", "routeKeys": {}, "namedRegex": "^/founder(?:/)?$"}, {"page": "/gallery", "regex": "^/gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/gallery(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}