(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_af7d1b._.js", {

"[project]/src/constant/galleryData.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Awards": (()=>Awards),
    "GovermentApprovals": (()=>GovermentApprovals),
    "VVIP": (()=>VVIP),
    "albumData": (()=>albumData),
    "tabButtons": (()=>tabButtons),
    "tabSubButtons": (()=>tabSubButtons),
    "tvImageData": (()=>tvImageData)
});
const tabButtons = [
    "Captain AD Manek",
    "Celebrities",
    "Events",
    "TV and Press",
    "Awards",
    "VVIP Testimonials",
    "Government Approvals"
];
const tabSubButtons = [
    "All Albums",
    "Capt AD Manek Album",
    "Celebrities Album",
    "Events Album"
];
const albumData = [
    // Captain Manek Album
    {
        image: "/assets/gallery/album/CaptainManek/1.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/3.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/5.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/6.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/7.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/8.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/10.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/11.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/12.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/13.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/14.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/15.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/16.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/17.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/19.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/20.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/21.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/22.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/23.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/24.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    {
        image: "/assets/gallery/album/CaptainManek/25.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 1
    },
    // Celebrities Album
    {
        image: "/assets/gallery/album/Celebs/1.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/2.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/4.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/5.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/6.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/7.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/8.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/9.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/10.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/11.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/12.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/13.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/14.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/15.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/16.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/17.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/18.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/19.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/20.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/21.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/22.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/23.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/24.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/25.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/27.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/28.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/29.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/30.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/31.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/32.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/33.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/34.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/35.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/36.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/37.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/38.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    {
        image: "/assets/gallery/album/Celebs/39.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 2
    },
    // Events Album
    {
        image: "/assets/gallery/album/Events/eventOne.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventTwo.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventThree.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventFour.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventFive.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventSix.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventSeven.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventEight.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventNine.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventTen.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventEleven.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventTwelve.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventThirteen.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventFourteen.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventFifteen.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    },
    {
        image: "/assets/gallery/album/Events/eventSixteen.jpg",
        title: "",
        msg: "",
        tab: 0,
        subTab: 3
    }
];
const tvImageData = [
    {
        image: "/assets/gallery/Tv_And_Press/londonPress.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/1.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/2.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/3.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/4.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/5.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/6_pepar.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/7_Ham_Radio.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/8_aviators1.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/Amrut_Manek_Sandesh_Story.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/bombay-times.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/airline_news_shibu.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/Captain_article.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/indian_express.png",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/ia1.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/citizen.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/divyabhaskar_image1.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/Picture9.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/Sadbhavana_Book-1.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/gjsir.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/AD_Manek_News.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/ento1.png",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/ento2.png",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/IMG_0001_4.jpg",
        title: "",
        msg: "",
        tab: 0
    },
    {
        image: "/assets/gallery/Tv_And_Press/gmitra2.jpg",
        title: "",
        msg: "",
        tab: 0
    }
];
const Awards = [
    {
        image: "/assets/gallery/Awards/1.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/2_j9qwon.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/3_ptig9x.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/4_s14bwz.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/5_qqs3lw.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/6_p3scqs.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/7_qsdmyv.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/8_svckpi.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/9_ebuc6l.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/10_hqyuzb.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/11_p3jbv3.jpg",
        title: "",
        msg: "",
        tab: 2
    },
    {
        image: "/assets/gallery/Awards/prideofgujarat_2_gvkmdv.jpg",
        title: "",
        msg: "",
        tab: 2
    }
];
const VVIP = [
    {
        image: "/assets/gallery/VVIP/aeronautical_society.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/anandiben_patel.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/aw4.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/aw14.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/cmletter.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/gopalletter.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/jrdTata.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/lionsclub.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/modi.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/modiletter25.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/rajiv_gandhi.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/rkpant.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/sanjay.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/school.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/shivajirao_Deshmukh.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/tushar.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/vasant_purke.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/vasavaletter.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/vilasrao.jpg",
        title: "",
        msg: "",
        tab: 3
    },
    {
        image: "/assets/gallery/VVIP/vinod.jpg",
        title: "",
        msg: "",
        tab: 3
    }
];
const GovermentApprovals = [
    {
        image: "/assets/gallery/Goverments/govt2.jpg",
        title: "DGCA - INDIA",
        msg: "Letter of Intimation from DGCA Govt. of India, New Delhi",
        tab: 4
    },
    {
        image: "/assets/gallery/Goverments/pilot_license_tefp7d.jpg",
        title: "FAA - USA",
        msg: "American Aviation (FAA) Ground Training Authorization Certificate",
        tab: 4
    },
    {
        image: "/assets/gallery/Goverments/govt1.jpg",
        title: "Registration",
        msg: "Government of India Registration Certificate",
        tab: 4
    },
    {
        image: "/assets/gallery/Goverments/govt3_vdzpmf.jpg",
        title: "Govt. India Wireless Licence",
        msg: "Club Licence, Government of India",
        tab: 4
    },
    {
        image: "/assets/gallery/Goverments/qci.jpg",
        title: "Quality Council of India",
        msg: "QCI Organisational Member Certificate",
        tab: 4
    },
    {
        image: "/assets/gallery/Goverments/govt5.jpg",
        title: "Trademark",
        msg: "Trademark Registration Certificate - The Skyline Aviation Club",
        tab: 4
    }
];
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/dialog.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "Dialog": (()=>Dialog),
    "DialogClose": (()=>DialogClose),
    "DialogContent": (()=>DialogContent),
    "DialogDescription": (()=>DialogDescription),
    "DialogFooter": (()=>DialogFooter),
    "DialogHeader": (()=>DialogHeader),
    "DialogOverlay": (()=>DialogOverlay),
    "DialogPortal": (()=>DialogPortal),
    "DialogTitle": (()=>DialogTitle),
    "DialogTrigger": (()=>DialogTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/@radix-ui/react-dialog/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
"use client";
;
;
;
;
;
const Dialog = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Root;
const DialogTrigger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Trigger;
const DialogPortal = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Portal;
const DialogClose = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Close;
const DialogOverlay = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Overlay, {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.jsx",
        lineNumber: 18,
        columnNumber: 3
    }, this));
_c = DialogOverlay;
DialogOverlay.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Overlay.displayName;
const DialogContent = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.forwardRef(_c1 = ({ className, children, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogPortal, {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DialogOverlay, {}, void 0, false, {
                fileName: "[project]/src/components/ui/dialog.jsx",
                lineNumber: 30,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Content, {
                ref: ref,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("fixed left-[50%] top-[50%] z-50 grid w-[90%] md:w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg", className),
                ...props,
                children: [
                    children,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Close, {
                        className: "absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/dialog.jsx",
                                lineNumber: 41,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "sr-only",
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/dialog.jsx",
                                lineNumber: 42,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/dialog.jsx",
                        lineNumber: 39,
                        columnNumber: 7
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/dialog.jsx",
                lineNumber: 31,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/dialog.jsx",
        lineNumber: 29,
        columnNumber: 3
    }, this));
_c2 = DialogContent;
DialogContent.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Content.displayName;
const DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col space-y-1.5 text-center sm:text-left", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.jsx",
        lineNumber: 53,
        columnNumber: 3
    }, this);
_c3 = DialogHeader;
DialogHeader.displayName = "DialogHeader";
const DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.jsx",
        lineNumber: 63,
        columnNumber: 3
    }, this);
_c4 = DialogFooter;
DialogFooter.displayName = "DialogFooter";
const DialogTitle = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.forwardRef(_c5 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Title, {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-lg font-semibold leading-none tracking-tight", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.jsx",
        lineNumber: 70,
        columnNumber: 3
    }, this));
_c6 = DialogTitle;
DialogTitle.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Title.displayName;
const DialogDescription = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__.forwardRef(_c7 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Description, {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-sm text-muted-foreground", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/dialog.jsx",
        lineNumber: 78,
        columnNumber: 3
    }, this));
_c8 = DialogDescription;
DialogDescription.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__.Description.displayName;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;
__turbopack_refresh__.register(_c, "DialogOverlay");
__turbopack_refresh__.register(_c1, "DialogContent$React.forwardRef");
__turbopack_refresh__.register(_c2, "DialogContent");
__turbopack_refresh__.register(_c3, "DialogHeader");
__turbopack_refresh__.register(_c4, "DialogFooter");
__turbopack_refresh__.register(_c5, "DialogTitle$React.forwardRef");
__turbopack_refresh__.register(_c6, "DialogTitle");
__turbopack_refresh__.register(_c7, "DialogDescription$React.forwardRef");
__turbopack_refresh__.register(_c8, "DialogDescription");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/GalleryComponent.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/constant/galleryData.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/dialog.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
;
;
const tabSlugs = [
    "captain-ad-manek",
    "celebrities",
    "events",
    "tv-and-press",
    "awards",
    "vvip-testimonials",
    "government-approvals"
];
// ✅ Reusable Section Component
const GallerySection = ({ data })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "grid grid-cols-2 sm:grid-cols-[repeat(auto-fit,_minmax(200px,_1fr))] gap-4 px-0 md:px-4 py-6 justify-items-center",
        children: data.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Dialog"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative w-full max-w-[250px] aspect-square bg-gray-200 cursor-pointer rounded-md overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogTrigger"], {
                            asChild: true,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                src: item.image,
                                alt: item.title || `Image ${index + 1}`,
                                fill: true,
                                quality: 100,
                                className: "object-cover hover:scale-105 transition-transform duration-300",
                                sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 250px",
                                priority: true
                            }, void 0, false, {
                                fileName: "[project]/src/components/GalleryComponent.jsx",
                                lineNumber: 41,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/GalleryComponent.jsx",
                            lineNumber: 40,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogOverlay"], {
                            className: "fixed inset-0 bg-black/90 backdrop-blur-md transition-all duration-300"
                        }, void 0, false, {
                            fileName: "[project]/src/components/GalleryComponent.jsx",
                            lineNumber: 51,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogContent"], {
                            className: "max-h-[90vh] overflow-y-auto p-4 rounded-lg bg-white",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogHeader"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogTitle"], {
                                            className: "mb-1 font-semibold text-lg",
                                            children: item.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/GalleryComponent.jsx",
                                            lineNumber: 54,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dialog$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DialogDescription"], {
                                            className: "font-normal text-lg mb-4",
                                            children: item.msg
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/GalleryComponent.jsx",
                                            lineNumber: 57,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/GalleryComponent.jsx",
                                    lineNumber: 53,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-center items-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        src: item.image,
                                        alt: item.title || `Image ${index + 1}`,
                                        width: 800,
                                        height: 600,
                                        quality: 100,
                                        className: "max-h-[60vh] w-auto h-auto object-contain"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/GalleryComponent.jsx",
                                        lineNumber: 62,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/GalleryComponent.jsx",
                                    lineNumber: 61,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/GalleryComponent.jsx",
                            lineNumber: 52,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/GalleryComponent.jsx",
                    lineNumber: 39,
                    columnNumber: 9
                }, this)
            }, index, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 38,
                columnNumber: 7
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/components/GalleryComponent.jsx",
        lineNumber: 36,
        columnNumber: 3
    }, this);
_c = GallerySection;
// ✅ Separate component for search params logic
const GalleryWithSearchParams = ()=>{
    _s();
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [hasScrolledFromURL, setHasScrolledFromURL] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const galleryRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GalleryWithSearchParams.useEffect": ()=>{
            const slug = searchParams.get("activeTab");
            if (slug) {
                const index = tabSlugs.indexOf(slug);
                if (index !== -1) {
                    setActiveTab(index + 1);
                    // Only scroll if this is from URL redirect (not from tab click)
                    // and we haven't already scrolled from URL in this session
                    if (!hasScrolledFromURL && galleryRef.current) {
                        galleryRef.current.scrollIntoView({
                            behavior: "smooth"
                        });
                        setHasScrolledFromURL(true);
                    }
                }
            }
        }
    }["GalleryWithSearchParams.useEffect"], [
        searchParams,
        hasScrolledFromURL
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-6 lg:py-8 lg:px-[3rem] lg:relative",
        ref: galleryRef,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "items-baseline gap-2 mb-8 flex-col xl:flex-row",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "hidden lg:block absolute bg-dotsImage top-40 left-0 bg-center bg-no-repeat w-10 h-36"
                    }, void 0, false, {
                        fileName: "[project]/src/components/GalleryComponent.jsx",
                        lineNumber: 109,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "py-2 md:py-4 xl:flex xl:items-center w-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-textBluePrimary text-xl font-bold lg:text-3xl mr-4",
                            children: "Categories : Album"
                        }, void 0, false, {
                            fileName: "[project]/src/components/GalleryComponent.jsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/GalleryComponent.jsx",
                        lineNumber: 111,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col gap-4 w-full ",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-wrap gap-3 justify-start",
                            children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tabButtons"].map((tab, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: `px-2 py-1 text-sm rounded-3xl border font-semibold shadow-sm transition-all
                  ${activeTab === index + 1 ? "bg-buttonBGPrimary text-white border-transparent" : "bg-white text-textBluePrimary border-textBluePrimary hover:bg-buttonBGPrimary hover:text-white"}`,
                                    onClick: ()=>{
                                        const newTabIndex = index + 1;
                                        setActiveTab(newTabIndex);
                                        // Update URL without triggering scroll
                                        router.push(`/gallery?activeTab=${tabSlugs[newTabIndex - 1]}`, {
                                            scroll: false
                                        });
                                    },
                                    children: tab
                                }, index, false, {
                                    fileName: "[project]/src/components/GalleryComponent.jsx",
                                    lineNumber: 120,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/GalleryComponent.jsx",
                            lineNumber: 118,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/GalleryComponent.jsx",
                        lineNumber: 117,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 108,
                columnNumber: 7
            }, this),
            activeTab === 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GallerySection, {
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["albumData"].filter((item)=>item.subTab === 1)
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 147,
                columnNumber: 9
            }, this),
            activeTab === 2 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GallerySection, {
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["albumData"].filter((item)=>item.subTab === 2)
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 152,
                columnNumber: 9
            }, this),
            activeTab === 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GallerySection, {
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["albumData"].filter((item)=>item.subTab === 3)
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 157,
                columnNumber: 9
            }, this),
            activeTab === 4 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GallerySection, {
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tvImageData"]
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 161,
                columnNumber: 27
            }, this),
            activeTab === 5 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GallerySection, {
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Awards"]
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 162,
                columnNumber: 27
            }, this),
            activeTab === 6 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GallerySection, {
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VVIP"]
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 163,
                columnNumber: 27
            }, this),
            activeTab === 7 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GallerySection, {
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$galleryData$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GovermentApprovals"]
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 164,
                columnNumber: 27
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/GalleryComponent.jsx",
        lineNumber: 104,
        columnNumber: 5
    }, this);
};
_s(GalleryWithSearchParams, "rT64MYui+Om8yuZa9D8yZApVj5A=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c1 = GalleryWithSearchParams;
// ✅ Loading fallback component
const GalleryLoading = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-6 lg:py-8 lg:px-[3rem]",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "animate-pulse",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-8 bg-gray-200 rounded w-64 mb-8"
                }, void 0, false, {
                    fileName: "[project]/src/components/GalleryComponent.jsx",
                    lineNumber: 173,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-wrap gap-3 mb-8",
                    children: Array.from({
                        length: 7
                    }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-8 bg-gray-200 rounded-3xl w-24"
                        }, i, false, {
                            fileName: "[project]/src/components/GalleryComponent.jsx",
                            lineNumber: 176,
                            columnNumber: 11
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/GalleryComponent.jsx",
                    lineNumber: 174,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-2 sm:grid-cols-[repeat(auto-fit,_minmax(200px,_1fr))] gap-4",
                    children: Array.from({
                        length: 6
                    }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "aspect-square bg-gray-200 rounded-md animate-pulse"
                        }, i, false, {
                            fileName: "[project]/src/components/GalleryComponent.jsx",
                            lineNumber: 181,
                            columnNumber: 11
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/GalleryComponent.jsx",
                    lineNumber: 179,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/GalleryComponent.jsx",
            lineNumber: 172,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/GalleryComponent.jsx",
        lineNumber: 171,
        columnNumber: 3
    }, this);
_c2 = GalleryLoading;
const GalleryComponent = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative bg-galleryBanner bg-cover bg-center h-[700px]",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]"
                    }, void 0, false, {
                        fileName: "[project]/src/components/GalleryComponent.jsx",
                        lineNumber: 193,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute left-0 right-0 bottom-12 lg:bottom-24 lg:w-4/5 lg:mx-auto mx-4 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "font-extrabold text-whiteOne text-5xl lg:text-6xl mb-4",
                                children: "Gallery"
                            }, void 0, false, {
                                fileName: "[project]/src/components/GalleryComponent.jsx",
                                lineNumber: 195,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-whiteOne font-bold text-sm md:text-xl",
                                children: "Explore The Skyline Aviation Club's journey through our Gallery, featuring training moments, VVIP testimonials, and award recognitions. Each album showcases the achievements, milestones, and trust we've built in the aviation industry."
                            }, void 0, false, {
                                fileName: "[project]/src/components/GalleryComponent.jsx",
                                lineNumber: 198,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/GalleryComponent.jsx",
                        lineNumber: 194,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 192,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Suspense"], {
                fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GalleryLoading, {}, void 0, false, {
                    fileName: "[project]/src/components/GalleryComponent.jsx",
                    lineNumber: 208,
                    columnNumber: 27
                }, void 0),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(GalleryWithSearchParams, {}, void 0, false, {
                    fileName: "[project]/src/components/GalleryComponent.jsx",
                    lineNumber: 209,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/GalleryComponent.jsx",
                lineNumber: 208,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_c3 = GalleryComponent;
const __TURBOPACK__default__export__ = GalleryComponent;
var _c, _c1, _c2, _c3;
__turbopack_refresh__.register(_c, "GallerySection");
__turbopack_refresh__.register(_c1, "GalleryWithSearchParams");
__turbopack_refresh__.register(_c2, "GalleryLoading");
__turbopack_refresh__.register(_c3, "GalleryComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/gallery/page.jsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_af7d1b._.js.map