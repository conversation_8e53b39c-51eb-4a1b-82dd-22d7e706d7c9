"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[820],{1820:(e,t,a)=>{a.d(t,{default:()=>j});var r=a(5155),s=a(2115),l=a(8126),n=a(9053),i=a(6367),o=a(9398),c=a(3434),d=a(1719),m=a(1902),u=a(8867),x=a(7849);let p=c.bL;c.YJ;let f=c.WT,h=s.forwardRef((e,t)=>{let{className:a,children:s,...l}=e;return(0,r.jsxs)(c.l9,{ref:t,className:(0,x.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...l,children:[s,(0,r.jsx)(c.In,{asChild:!0,children:(0,r.jsx)(d.A,{className:"h-4 w-4 opacity-50"})})]})});h.displayName=c.l9.displayName;let b=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.PP,{ref:t,className:(0,x.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})});b.displayName=c.PP.displayName;let g=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.wn,{ref:t,className:(0,x.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})});g.displayName=c.wn.displayName;let N=s.forwardRef((e,t)=>{let{className:a,children:s,position:l="popper",...n}=e;return(0,r.jsx)(c.ZL,{children:(0,r.jsxs)(c.UC,{ref:t,className:(0,x.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:l,...n,children:[(0,r.jsx)(b,{}),(0,r.jsx)(c.LM,{className:(0,x.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(g,{})]})})});N.displayName=c.UC.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.JU,{ref:t,className:(0,x.cn)("px-2 py-1.5 text-sm font-semibold",a),...s})}).displayName=c.JU.displayName;let y=s.forwardRef((e,t)=>{let{className:a,children:s,...l}=e;return(0,r.jsxs)(c.q7,{ref:t,className:(0,x.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...l,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(c.VF,{children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})}),(0,r.jsx)(c.p4,{children:s})]})});y.displayName=c.q7.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(c.wv,{ref:t,className:(0,x.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=c.wv.displayName;let j=()=>{let e=(0,s.useRef)(),{toast:t}=(0,o.dj)(),[a,c]=(0,s.useState)({fullName:"",course:"",contactNumber:"",city:"",email:"",helpText:""}),[d,m]=(0,s.useState)({}),[u,x]=(0,s.useState)(!1),b=a.fullName.trim()&&a.course.trim()&&a.contactNumber.trim()&&a.city.trim()&&a.email.trim(),g=e=>{let{name:t,value:a}=e.target;c(e=>({...e,[t]:a}))},j=()=>{let e={};return(!a.fullName||a.fullName.length>50||/[^a-zA-Z\s]/.test(a.fullName))&&(e.fullName="Full Name should be less than 50 characters and contain only letters."),(!a.contactNumber||a.contactNumber.length>15||/[^0-9]/.test(a.contactNumber))&&(e.contactNumber="Contact Number should be less than 15 digits and contain only numbers."),(!a.city||a.city.length>50||/[^a-zA-Z\s]/.test(a.city))&&(e.city="City should be less than 50 characters and contain only letters."),a.email?/\S+@\S+\.\S+/.test(a.email)||(e.email="Email Address is invalid."):e.email="Email Address is required.",a.course||(e.course="Please select a course."),m(e),0===Object.keys(e).length},w=function(e,t){let a=(0,s.useRef)(null);return(0,s.useEffect)(()=>()=>clearTimeout(a.current),[]),function(){for(var r=arguments.length,s=Array(r),l=0;l<r;l++)s[l]=arguments[l];clearTimeout(a.current),a.current=setTimeout(()=>{e(...s)},t)}}(e=>i.Ay.send("service_tph07dp","template_hj3d7nh",e,"MG4XYp65GKvErgJlL"),1e3),v=async e=>{if(e.preventDefault(),!b){t({variant:"destructive",title:"Missing Fields!",description:"Please fill out all required fields."});return}if(!j())return;let r={full_name:a.fullName,course_name:a.course,contact_number:a.contactNumber,city:a.city,email_address:a.email,help_text:a.helpText||"Not provided"};try{x(!0),await w(r),t({variant:"success",title:"Success!",description:"Your enquiry has been sent successfully!"}),c({fullName:"",course:"",contactNumber:"",city:"",email:"",helpText:""})}catch(e){t({variant:"destructive",title:"Error!",description:"Failed to send your enquiry. Please try again later."}),console.error("EmailJS Error:",e.text)}finally{x(!1)}};return(0,r.jsxs)("div",{className:"w-full md:w-1/2 tablet:w-[85%] bg-white shadow-lg rounded-lg p-6 mb-8 relative",children:[(0,r.jsxs)("form",{ref:e,className:"space-y-4",onSubmit:v,children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Full Name",(0,r.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,r.jsx)("input",{type:"text",name:"fullName",placeholder:"Enter full name",value:a.fullName,onChange:g,className:"w-full p-3 bg-formBG text-[15px] text-black rounded-lg focus:outline-none ".concat(d.fullName?"ring-2 ring-red-500":""),maxLength:"50",pattern:"[a-zA-Z\\s]*",required:!0}),d.fullName&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:d.fullName})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Select Course Interested In",(0,r.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,r.jsxs)(p,{value:a.course,onValueChange:e=>c(t=>({...t,course:e})),children:[(0,r.jsx)(h,{className:"w-full p-[1.4rem] bg-formBG text-[15px] text-black rounded-lg ".concat(d.course?"ring-2 ring-red-500":""),children:(0,r.jsx)(f,{placeholder:"Select a Course"})}),(0,r.jsx)(N,{children:l.U8.map((e,t)=>(0,r.jsx)(y,{value:e.text,children:e.text},t))})]}),d.course&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:d.course})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Contact Number",(0,r.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,r.jsx)("input",{type:"text",name:"contactNumber",value:a.contactNumber,onChange:g,maxLength:"15",pattern:"[0-9]*",required:!0,placeholder:"Enter contact number",className:"w-full p-3 bg-formBG text-[15px] text-black rounded-lg ".concat(d.contactNumber?"ring-2 ring-red-500":"")}),d.contactNumber&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:d.contactNumber})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["City",(0,r.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,r.jsx)("input",{type:"text",name:"city",value:a.city,onChange:g,maxLength:"50",pattern:"[a-zA-Z\\s]*",required:!0,placeholder:"Enter city",className:"w-full p-3 bg-formBG text-[15px] text-black rounded-lg ".concat(d.city?"ring-2 ring-red-500":"")}),d.city&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:d.city})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:["Email Address",(0,r.jsx)("span",{className:"text-formLabelSubColor",children:"*"})]}),(0,r.jsx)("input",{type:"email",name:"email",value:a.email,onChange:g,required:!0,placeholder:"Enter email",className:"w-full p-3 bg-formBG text-[15px] text-black rounded-lg ".concat(d.email?"ring-2 ring-red-500":"")}),d.email&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:d.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block mb-1 font-medium text-formTextColor text-sm",children:"Tell Us How We Can Help"}),(0,r.jsx)("textarea",{name:"helpText",value:a.helpText,onChange:g,placeholder:"Optional message",rows:"3",className:"w-full p-3 bg-formBG text-[15px] text-black rounded-lg"})]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("button",{type:"submit",className:"flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5",children:[(0,r.jsx)("span",{className:"font-bold",children:"Send Enquiry"}),(0,r.jsx)(n.A,{className:"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1"})]})})]}),u&&(0,r.jsx)("div",{className:"fixed inset-0 bg-[#0000004f] bg-opacity-40 z-50 flex justify-center items-center",children:(0,r.jsx)("div",{className:"w-12 h-12 border-4 border-[#2821ff] border-t-transparent rounded-full animate-spin"})})]})}},8126:(e,t,a)=>{a.d(t,{U8:()=>s,pw:()=>r});let r=[{text:"Home",link:"/"},{text:"Gallery",link:"/gallery"},{text:"Courses",link:"/course"},{text:"Events",link:"/events"},{text:"About Us",link:"/aboutus"},{text:"Contact Us",link:"/enquire"}],s=[{text:"Indian Commercial Pilot",link:"indian-commercial-pilot-licence-course"},{text:"American Commercial Pilot",link:"american-commercial-pilots-licence"},{text:"CPL Package Program",link:"commercial-pilot-licence-package-program"},{text:"Foreign CPL Conversion Course",link:"foreign-commercial-pilot-licence-conversion-course"},{text:"Helicopter Pilot Training",link:"helicopter-commercial-pilot-licence-course"},{text:"American Flight Dispatcher",link:"aircraft-flight-dispatcher-licence-course"},{text:"Radio Telephony Licence",link:"radio-telephony-r-aeromobile-frtol-licence"},{text:"Airhostess/Flight Purser",link:"airhostess-flight-purser-training-course"},{text:"Airport Ground Staff",link:"airport-ground-staff-course"},{text:"Aviation Foundation Course",link:"aviation-foundation-course"},{text:"Aeroplane/Helicopter Training Workshop",link:"two-day-aeroplane-or-helicopter-training-workshop"}]},9398:(e,t,a)=>{a.d(t,{dj:()=>u});var r=a(2115);let s=0,l=new Map,n=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function d(e){c=i(c,e),o.forEach(e=>{e(c)})}function m(e){let{...t}=e,a=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:a});return d({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||r()}}}),{id:a,dismiss:r,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){let[e,t]=r.useState(c);return r.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},7849:(e,t,a)=>{a.d(t,{cn:()=>l});var r=a(3463),s=a(9795);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}}}]);