{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/accordion.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA"}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/accordion.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6CACA"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/faqData.jsx"], "sourcesContent": ["const accordionData = [\r\n  {\r\n    title: \"What type of Learning Environemnt will the students be in ?\",\r\n    content: (\r\n      <p>\r\n        ● We have tried our best efforts to make and maintain high standard\r\n        learning environment. Our Classroom is fully air conditioned to make\r\n        learning in comfortable and relaxed conditions. Our philosophy and\r\n        intention are to limit the number of students in each intake to a\r\n        manageable level of 20 students. This way, we can give each student more\r\n        individual attention resulting in improved overall performance by the\r\n        students. We have no more than 20 students in any phase of training at\r\n        any one time.\r\n      </p>\r\n    ),\r\n  },\r\n  {\r\n    title: \"How are the students taught ?\",\r\n    content: (\r\n      <>\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Flight Simulator Training\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            Students are given opportunity to have hands on training on In-house\r\n            Flight Simulator where student learns and acquires basic flying\r\n            skills through VFR mode and fly in practice area and do touch and\r\n            goes at the airport to develop take off, circuit pattern, approach\r\n            and landings and also develops good instrument scanning while on IFR\r\n            mode.\r\n          </p>\r\n        </section>\r\n\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Audio Visual Aids\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            As technology has changed in each and every field of life, education\r\n            should not be apart from it. So we arrange educational films based\r\n            on new syllabus, the entire study program on audiovisuals as viewing\r\n            has much more impact than hearing. Home Theatre is made available to\r\n            deliver audiovisual lectures via educational DVDs procured from\r\n            world renowned publisher like ASA and Jeppesen companies of USA on\r\n            various aviation topics will be provided for all students.\r\n          </p>\r\n        </section>\r\n\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Radio Communication Practical Facility\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            The Skyline Aviation Club is only aviation training institute in\r\n            India having Amateur (HAM) Radio Station licenced by Wireless\r\n            Planning and Co-Ordination (WPC), Ministry of Telecommunication &\r\n            IT, Government of India since last 30 years. We have well equipped\r\n            and modern wireless lab having HF, VHF and UHF wireless sets where\r\n            each and every wireless communication demonstration are carried out\r\n            to understand the syllabus of Radio Telephony (R) Licence /Flight\r\n            Radio Telephone Licence and Amateur (HAM) Radio Licence.\r\n          </p>\r\n        </section>\r\n\r\n        <section>\r\n          <h2 className=\"text-base font-bold text-blue-700 mb-2\">\r\n            ● Aviation Library\r\n          </h2>\r\n          <p className=\"text-gray-700 text-xs md:text-sm mb-1\">\r\n            The Skyline Aviation Club is only aviation training institute in\r\n            India having a well-furnished library. Library is storing 4000 plus\r\n            books on various subjects useful for students with selective and\r\n            informative aviation books, Pilot Training Manuals, ICAO Annexure's,\r\n            ICAO Documents, aviation magazines, periodicals including all\r\n            Commercial Pilot Licence syllabus books as prescribed by the DGCA,\r\n            Ministry of Civil Aviation, Government of India. Pilot Training\r\n            Books/ Manuals as prescribed by Federal Aviation Administration\r\n            (FAA), USA and Transport Canada (TC) Canada.\r\n          </p>\r\n        </section>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"What is the teaching Methodology ?\",\r\n    content: (\r\n      <>\r\n        <p>\r\n          ● The courses are a carefully structured mix of classroom sessions,\r\n          computer based training and audio-visual presentations on Home Theatre\r\n          Screen, Real time Air Traffic Control (ATC) Radio Communication via\r\n          Visuals and audio plus Well-equipped In-house Flight Simulator.\r\n          Theoretical aspects guided by the faculty, help the student test the\r\n          concepts that have been explained in class before assimilating them.\r\n        </p>\r\n        <p>\r\n          ● The courses are made simpler by using latest IFR Jeppesen\r\n          Aeronautical charts for Enroute flying and Instrument Approaches,\r\n          Landing Charts, Aerodrome Obstruction Charts and Terminal, Sectional\r\n          and World Aeronautical Charts (WAC) for VFR air navigation. Moreover,\r\n          the courses are on up-to-date aviation information as per the\r\n          International Civil Aviation Organization (ICAO) Annexes and\r\n          documents, Aeronautical Information Publications (AIP) India, AERADIO,\r\n          NOTAMS and updated Jeppesen Manual especially for the Indian flying\r\n          environment. The Club is one of the rare institutions who boasts of\r\n          having highly qualified, rated and experienced war veterans.\r\n        </p>\r\n        <p>\r\n          ● Personalized instructions are provided to each and every student.\r\n          The student&apos;s progress is continuously assessed through a series\r\n          of internal tests and assignments. These are given significant weight\r\n          in award of Final Certificate of Achievement from the Club\r\n        </p>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"I do not stay in Mumbai. Is Lodging and Boarding Provided ?\",\r\n    content: (\r\n      <>\r\n        <p>\r\n          ● We provide for Lodging and Boarding which is situated walking\r\n          distance from our training center. The facilities are well maintained\r\n          to ensure a peaceful and livable environment with all the basic\r\n          necessities. Separate facilities for boys and girls are available.\r\n        </p>\r\n        <p>● Home cooked meals can also be provided twice a day.</p>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Who will be teaching the students ?\",\r\n    content: (\r\n      <>\r\n        <p>\r\n          ● Our present pilot training courses are managed by well-qualified and\r\n          highly experienced professional pilots holding I Commercial Pilot\r\n          Licence with Pilot Instructor Rating certified by Director General of\r\n          Civil Aviation (DGCA) Government of India and Federal Aviation\r\n          Administration (FAA) Government of United States of America who have\r\n          not just attained knowledge and experiences of commercial pilots, but\r\n          have special training on instrument rating, multi-engine rating and a\r\n          special qualification as PILOT INSTRUCTOR. They hold ADVANCED\r\n          INSTRUMENT GROUND PILOT INSTRUCTOR (A.G.I.I), Certificate from Federal\r\n          Aviation Administration (FAA) USA especially for you ! Some faculty\r\n          members are retired pilots from Indian Air Force and Indian Naval\r\n          Aviation. The faculty is capable of teaching students from zero level\r\n          of Student Pilot through Airline Transport Pilot with their invaluable\r\n          aviation experiences of last forty years.\r\n        </p>\r\n        <p>\r\n          ● The Club and its instructors are members of many aviation\r\n          organizations of India like Federation of Indian Pilots (FIP),\r\n          Aeronautical Society of India (ASI), Indian Women Pilots Association\r\n          (IWPA) and having association with Aircraft Owners & Pilots\r\n          Association (AOPA) USA, International Air Cadet Exchange Association\r\n          (IACEA) Great Britain, Civil Air Patrol (CAP) Auxiliary US Air Force,\r\n          USA. Ninety-nine Inc. (International Woman&apos;s Pilot Association)\r\n          USA, FAPA USA, EAA USA, Major Airlines Aviation Agencies and best\r\n          flight training Organizations (FTO) in India and abroad. A combination\r\n          of such versatile experiences is like adding one more feather to the\r\n          cap of the Club. Our Chief Pilot Instructor Capt. (Dr.) AD Manek had\r\n          personally visited and gained knowledge of functioning of\r\n          International Civil Aviation Organization (ICAO) HQ Montreal, Canada,\r\n          International Telecommunication Union (ITU) HQ Geneva, Switzerland\r\n          which are supreme authorities to control civil aviation throughout\r\n          world consisting 193 countries, along with visit to NASA, USA to share\r\n          the best possible knowledge to students.\r\n        </p>\r\n      </>\r\n    ),\r\n  },\r\n  {\r\n    title: \"Once I finish my training at The Skyine Aviation Club what next ?\",\r\n    content: (\r\n      <p>\r\n        ● Students completing Ground Training will be deputed for Practical\r\n        flight training with our affiliated DGCA approved Flight Training\r\n        Organization or Best Flight Training School in foreign countries like\r\n        USA, Canada, Australia, New Zealand & U.K. Students and Parents will be\r\n        provided with all assistance on Visa Information, Foreign Exchange, Air\r\n        Tickets, Insurance and all related Guidance at a single table.\r\n      </p>\r\n    ),\r\n  },\r\n];\r\n\r\nexport default accordionData;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,uBACE,8OAAC;sBAAE;;;;;;IAWP;IACA;QACE,OAAO;QACP,uBACE;;8BACE,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAUvD,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAWvD,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAYvD,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;;;IAc7D;IACA;QACE,OAAO;QACP,uBACE;;8BACE,8OAAC;8BAAE;;;;;;8BAQH,8OAAC;8BAAE;;;;;;8BAYH,8OAAC;8BAAE;;;;;;;;IAQT;IACA;QACE,OAAO;QACP,uBACE;;8BACE,8OAAC;8BAAE;;;;;;8BAMH,8OAAC;8BAAE;;;;;;;;IAGT;IACA;QACE,OAAO;QACP,uBACE;;8BACE,8OAAC;8BAAE;;;;;;8BAgBH,8OAAC;8BAAE;;;;;;;;IAqBT;IACA;QACE,OAAO;QACP,uBACE,8OAAC;sBAAE;;;;;;IASP;CACD;uCAEc"}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/FAQContainer.jsx"], "sourcesContent": ["import {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\nimport accordionData from \"@/constant/faqData\";\r\nconst FAQContainer = () => {\r\n  return (\r\n    <div className=\"bg-[url('/assets/faqImage.webp')] bg-cover bg-center bg-local px-4 py-8 md:p-12 lg:py-28 xl:py-28\">\r\n      <h3 className=\"text-white font-extrabold text-2xl md:text-4xl lg:text-6xl mb-4 lg:mb-16\">\r\n        Frequently Asked Questions.\r\n      </h3>\r\n      <Accordion\r\n        type=\"single\" // Ensures only one can be open at a time\r\n        collapsible\r\n        className=\"grid gap-4 items-start justify-center pt-4 grid-cols-1\"\r\n      >\r\n        {accordionData.map((item, index) => (\r\n          <AccordionItem\r\n            key={index}\r\n            value={`item-${index}`}\r\n            className=\"p-3 xl:p-4 rounded bg-accordionBG\"\r\n          >\r\n            <AccordionTrigger className=\"text-white text-sm font-medium xl:text-base\">\r\n              {item.title}\r\n            </AccordionTrigger>\r\n            <AccordionContent className=\"text-white pt-3 text-sm font-medium xl:text-base\">\r\n              {item.content}\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        ))}\r\n      </Accordion>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FAQContainer;\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;;;;AACA,MAAM,eAAe;IACnB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2E;;;;;;0BAGzF,8OAAC,qIAAA,CAAA,YAAS;gBACR,MAAK,SAAS,yCAAyC;;gBACvD,WAAW;gBACX,WAAU;0BAET,2HAAA,CAAA,UAAa,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,qIAAA,CAAA,gBAAa;wBAEZ,OAAO,CAAC,KAAK,EAAE,OAAO;wBACtB,WAAU;;0CAEV,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CACzB,KAAK,KAAK;;;;;;0CAEb,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CACzB,KAAK,OAAO;;;;;;;uBARV;;;;;;;;;;;;;;;;AAejB;uCAEe"}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/landingPageData.jsx"], "sourcesContent": ["const aboutUs = [\r\n  {\r\n    title: \"World-Class Training Facilities\",\r\n    desc: \"At The Skyline Aviation Club, we pride ourselves on providing cutting-edge facilities that mirror real-world aviation environments. Train with state-of-the-art flight simulators, a fleet of modern training aircraft, and classrooms equipped with the latest learning technologies. Our facilities are designed to ensure you are prepared for every challenge the skies may bring.\",\r\n    image: \"/assets/about3.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n  {\r\n    title: \"Proven Track Records\",\r\n    desc: \"Since last 38 years, The Skyline Aviation Club has maintained an outstanding track record of graduate success rate, with alumni now flying for leading global airlines. Our hands-on training approach, experienced instructors, and tailored curriculum ensure you not only pass your certifications but also excel in competitive aviation careers.\",\r\n    image: \"/About_Us.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n  {\r\n    title: \"Comprehensive Student Support\",\r\n    desc: \"From the moment you enroll to the day you land your dream job, we're here for you. Our dedicated student support team helps with admissions, financial planning, and career placement, ensuring your journey is as smooth as your takeoff. With The Skyline Aviation Club, you are mentored throughout your aviation and airline career.\",\r\n    image: \"/assets/about1.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n  {\r\n    title: \"Globally Recognised Certifications\",\r\n    desc: \"The Skyline Aviation Club offers certifications and licences that are recognised under International Civil Aviation Organization (ICAO) and respected worldwide. From Private Pilot licences (PPL) to Instrument Rating (IR) to Commercial Pilot licences (CPL) and more, our programs meet the highest international standards, opening doors to a future in aviation, no matter where you want to fly.\",\r\n    image: \"/assets/about2.jpg\",\r\n    width: 456,\r\n    height: 239,\r\n  },\r\n];\r\n\r\nconst tableData = [\r\n  {\r\n    startDate: \"15th April\",\r\n    endDate: \"5th April\",\r\n  },\r\n  {\r\n    startDate: \"1st May\",\r\n    endDate: \"20th April\",\r\n  },\r\n  {\r\n    startDate: \"15th June\",\r\n    endDate: \"5th June\",\r\n  },\r\n  {\r\n    startDate: \"15th August\",\r\n    endDate: \"5th August\",\r\n  },\r\n  {\r\n    startDate: \"15th October\",\r\n    endDate: \"5th October\",\r\n  },\r\n  {\r\n    startDate: \"15th December\",\r\n    endDate: \"5th December\",\r\n  },\r\n];\r\n\r\nconst coursesData = [\r\n  {\r\n    id: 1,\r\n    slug: \"indian-commercial-pilot-licence-course\",\r\n    title: \"DGCA\",\r\n    subTitle: \"Indian Commercial Pilot Licence Course\",\r\n    desc: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aircraft & Engine (Technical) General & Specific\r\n        </li>\r\n        <li className=\"py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Radio Telephonic\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/cptManek2.jpg\",\r\n  },\r\n  {\r\n    id: 2,\r\n    slug: \"american-commercial-pilots-licence\",\r\n    title: \"FAA USA\",\r\n    subTitle: \"American Commercial Pilot Licence Course\",\r\n    desc: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/courseTwoImage.png\",\r\n  },\r\n  {\r\n    id: 3,\r\n    slug: \"aircraft-flight-dispatcher-licence-course\",\r\n    title: \"FAA USA\",\r\n    subTitle: \"Aircraft/Flight Dispatcher Licence Course\",\r\n    desc: \"An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Flight Training\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/dispatch.jpg\",\r\n  },\r\n  {\r\n    id: 4,\r\n    slug: \"radio-telephony-r-aeromobile-frtol-licence\",\r\n    title: \"DGCA/WPC\",\r\n    subTitle: \"Radio Telephony (R) Aeromobile/FRTOL Licence Course\",\r\n    desc: \"This is a professional course of international standards as per the general guidelines prescribed under international radio regulations applicable to the aeronautical mobile service.\",\r\n    topics: (\r\n      <>\r\n        <h3 className=\"mb-1\">Part 1: Practical Test in Regulation and Procedure</h3>\r\n        <h3 className=\"mb-1\">Part 2: Oral Examination</h3>\r\n        <ul className=\"flex flex-wrap gap-2\">\r\n          <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n            Regulation & Procedure\r\n          </li>\r\n          <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n            Radio Principles & Practice\r\n          </li>\r\n        </ul>\r\n      </>\r\n    ),\r\n    image: \"/assets/courseFourImage.png\",\r\n  },\r\n  {\r\n    id: 5,\r\n    slug: \"commercial-pilot-licence-package-program\",\r\n    title: \"Inclusive of Indian CPL + American CPL or Canadian CPL\",\r\n    subTitle: \"Commercial Pilot Licence Package Program\",\r\n    desc: \"The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/albumImageTwo.png\",\r\n  },\r\n  {\r\n    id: 6,\r\n    slug: \"airhostess-flight-purser-training-course\",\r\n    title: \"Cabin Crew Training Program\",\r\n    subTitle: \"Airhostess/Flight Purser Training Course\",\r\n    desc: \"Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.\",\r\n    topics: null,\r\n    image: \"/assets/courseSixImage.png\",\r\n  },\r\n  {\r\n    id: 7,\r\n    slug: \"airport-ground-staff-course\",\r\n    title: \"Airport Ground Services\",\r\n    subTitle: \"Airport Ground Staff Course\",\r\n    desc: \"If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/groundStaffHome.jpg\",\r\n  },\r\n  {\r\n    id: 8,\r\n    slug: \"aviation-foundation-course\",\r\n    title: \"Summer Vacation Training Program\",\r\n    subTitle:\r\n      \"Aviation Foundation Course\",\r\n    desc: \"This course builds a strong foundation for students aspiring to airline careers like Pilot, Dispatcher, Air Hostess, Ground Staff, AME, ATC Officer, and more.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/foundationCourse.jpg\",\r\n  },\r\n  {\r\n    id: 9,\r\n    slug: \"two-day-aeroplane-or-helicopter-training-workshop\",\r\n    title: \"Aeroplane/Helicopter Orientation Training\",\r\n    subTitle: \"Two Day Aeroplane/Helicopter Training Workshop\",\r\n    desc: \"Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/courseDetailBanner.png\",\r\n  },\r\n  {\r\n    id: 10,\r\n    slug: \"foreign-commercial-pilot-licence-conversion-course\",\r\n    title: \"Indian Commercial Pilot Licence (DGCA)\",\r\n    subTitle: \"Foreign Commercial Pilot Licence Conversion Course\",\r\n    desc: \"The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Regulation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical Aviation\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/courseThreeImage.png\",\r\n  },\r\n  {\r\n    id: 11,\r\n    slug: \"helicopter-commercial-pilot-licence-course\",\r\n    title: \"HCPL\",\r\n    subTitle: \"Helicopter Commercial Pilot Licence Course\",\r\n    desc: \"The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.\",\r\n    topics: (\r\n      <ul className=\"flex flex-wrap gap-2\">\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Helicopter Aerodynamics\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Air Navigation\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Aviation Meteorology\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Helicopter Flight Rules & Regulations\r\n        </li>\r\n        <li className=\"py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary\">\r\n          Technical General (Helicopter)\r\n        </li>\r\n      </ul>\r\n    ),\r\n    image: \"/assets/helicopterImage.webp\",\r\n  },\r\n];\r\n\r\nconst alumniData = [\r\n  {\r\n    title: \"Captain Monalisa Parmar\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I am very happy to say that I am selected as FLIGHT DISPATCHER by\r\n          SPICE JET Airline. Currently I am posted at Surat Airport. Your extra\r\n          ordinary training in Flight Dispatch has really helped me in getting\r\n          job. Thank you very much.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Monalisa.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Himanshu Patel\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Capt. A. D. Manek Sir, It was my pleasure to be a part of The Skyline\r\n          Aviation Club, Mumbai. It played a role of a stepping stone for me to\r\n          be a part of Aviation Industry today. I am glad that we have such\r\n          clubs in India that encourages aviation enthusiasts to rise & fly high\r\n          in their professions. An initiative to begin with something new &\r\n          efficient is where you begin you way to Skyline. I am pleased to be\r\n          the part of your team Capt. A. D. Manek. I wish a very good luck to\r\n          the future generation of trainees at The Skyline Aviation Club.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Himanshu.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Mukarram Electricwala\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I am grateful to Sir and The Skyline Aviation Club for giving me the\r\n          wonderful opportunity to teach at Skyline under Manek sirs guidance.\r\n          Lastly I would like to thank Manek Sir, The Skyline Aviation Club and its\r\n          support staff for just being there whenever I required help I wish Sir\r\n          and The Skyline Aviation Club all the happiness and best of luck for the\r\n          future!”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Mukarram.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Jignesh Devadhia\",\r\n    subTitle: \" F.A.A. Commercial Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I have completed my training and qualified as F.A.A. Commercial Pilot\r\n          and I am about to get my Indian Commercial Pilot Licence. With your\r\n          prefect training I was able to complete my Flight Training and passing\r\n          written exams in shortest possible time and hence I was able to break\r\n          record of my Flight School. Thank you very much for all.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Jignesh.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Niraj Patel\",\r\n    subTitle: \"First Indian Women Helicopter Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I have completed my training and qualified as F.A.A. Commercial Pilot\r\n          and I am about to get my Indian Commercial Pilot Licence. With your\r\n          prefect training I was able to complete my Flight Training and passing\r\n          written exams in shortest possible time and hence I was able to break\r\n          record of my Flight School. Thank you very much for all.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Niraj.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Aruna Kandarpa\",\r\n    subTitle: \"First Indian Women Helicopter Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I have completed my training and qualified as F.A.A. Commercial Pilot\r\n          and I am about to get my Indian Commercial Pilot Licence. With your\r\n          prefect training I was able to complete my Flight Training and passing\r\n          written exams in shortest possible time and hence I was able to break\r\n          record of my Flight School. Thank you very much for all.”\r\n        </p>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “The club and it's faculty have always been dear to my heart and I\r\n          congratulate them on their 20th year of success and achievement.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Aruna.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Kritika Parmar\",\r\n    subTitle: \"First Indian Women Helicopter Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I am proud to be student of The Skyline Aviation Club. With right\r\n          information and guidance and training I am able to complete 130 hrs of\r\n          flight training at Fresno, CA, U.S.A. I am really thankful to Capt. A.\r\n          D. Manek for personally guiding me to obtain education loan of\r\n          Rs.20,00,000/- from Government of Gujarat.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Kritika.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Bradley Verughese Matthew\",\r\n    subTitle: \"FAA Flight Dispatcher Licence\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Respected Sir, I am glad to tell you that my studies and trip to USA\r\n          was a success with your guidance and inspiration ! As you know I have\r\n          received my FAA Flight Dispatcher Licence. So right now I am hungry to\r\n          get into job and prove and serve myself for Aviation”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Bradley.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Mervyn Mascarenhas\",\r\n    subTitle: \"FAA Aire Line Transport Pilot\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I began studying at The Skyline Aviation Club detailed way of\r\n          instructions and the ease with which he explained my quries is\r\n          remarkable. His extensive knowledge of the FAA instructions and\r\n          examinations is admirable. A great passion for what he do is visible\r\n          all around and his teaching standards are excellent. I am now\r\n          confident I will do very well in my FAA Aire Line Transport Pilot\r\n          Licence exams”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Mervyn.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Mihir Mistry\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Dear Capt.A.D.Manek, It's great to be a part of The Skyline Aviation Club\r\n          family and would specially like to thank you for being a father figure\r\n          and blend my life professionally. My success is only because of the\r\n          hardest efforts put in by you.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Mihir.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Captain Sagar K Karnik\",\r\n    subTitle: \"Flight Dispatcher\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “Dear Capt.A.D.Manek, It's great to be a part of The Skyline Aviation Club\r\n          family and would specially like to thank you for being a father figure\r\n          and blend my life professionally. My success is only because of the\r\n          hardest efforts put in by you.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Sagar.png\",\r\n    rating: 5.0,\r\n  },\r\n  {\r\n    title: \"Neelam Patel\",\r\n    subTitle: \"Flight Management\",\r\n    desc: (\r\n      <>\r\n        <p className=\"font-normal text-sm text-customBlack text-sm italic mb-3\">\r\n          “I, Miss Neelam M.Patel, after graduation joined The Skyline Aviation Club\r\n          for In Flight Management Course, and completed the same within the\r\n          stipulated time. During the tenure of the course, I received precious\r\n          informative global knowledge, instruction resulting in my selection as\r\n          an Air Hostess of Air India Ltd. I would like o thank Capt.A.D.Manek\r\n          and all the instructors for their co-operation and valuable\r\n          informative knowledge passed on to me for which I am grateful to The Skyline Aviation Club.”\r\n        </p>\r\n      </>\r\n    ),\r\n    image: \"/assets/alumni/Neelam.png\",\r\n    rating: 5.0,\r\n  },\r\n];\r\n\r\nconst companyImagesArray = [\r\n  \"/assets/air_asia_logo.png\",\r\n  \"/assets/Air_India.svg\",\r\n  \"/assets/spicejet-logo.png\",\r\n  \"/assets/indigo.png\",\r\n  \"/assets/qatar.png\",\r\n  \"/assets/emirates.png\",\r\n];\r\n\r\nexport { aboutUs, tableData, coursesData, alumniData, companyImagesArray };\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA,MAAM,UAAU;IACd;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;IACV;CACD;AAED,MAAM,YAAY;IAChB;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;IACA;QACE,WAAW;QACX,SAAS;IACX;CACD;AAED,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE;;8BACE,8OAAC;oBAAG,WAAU;8BAAO;;;;;;8BACrB,8OAAC;oBAAG,WAAU;8BAAO;;;;;;8BACrB,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCAA6I;;;;;;sCAG3J,8OAAC;4BAAG,WAAU;sCAA6I;;;;;;;;;;;;;;QAMjK,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UACE;QACF,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;8BAG3J,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,sBACE,8OAAC;YAAG,WAAU;;8BACZ,8OAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,8OAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,8OAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,8OAAC;oBAAG,WAAU;8BAAkI;;;;;;8BAGhJ,8OAAC;oBAAG,WAAU;8BAA6I;;;;;;;;;;;;QAK/J,OAAO;IACT;CACD;AAED,MAAM,aAAa;IACjB;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAY5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAU5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAS5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAS5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;;8BACE,8OAAC;oBAAE,WAAU;8BAA2D;;;;;;8BAOxE,8OAAC;oBAAE,WAAU;8BAA2D;;;;;;;;QAM5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAS5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAW5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAQ5E,OAAO;QACP,QAAQ;IACV;IACA;QACE,OAAO;QACP,UAAU;QACV,oBACE;sBACE,cAAA,8OAAC;gBAAE,WAAU;0BAA2D;;;;;;;QAW5E,OAAO;QACP,QAAQ;IACV;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;CACD"}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/CourseCarousel.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/CourseCarousel.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CourseCarousel.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA"}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/CourseCarousel.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/CourseCarousel.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CourseCarousel.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA"}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1188, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/CoursesComponent.jsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport FAQContainer from \"./FAQContainer\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport { coursesData } from \"@/constant/landingPageData\";\r\nimport Link from \"next/link\";\r\nimport CourseCarousel from \"./CourseCarousel\";\r\n\r\nconst CoursesComponent = () => {\r\n  return (\r\n    <>\r\n      <div className=\"relative bg-coursesBanner bg-cover bg-[-26rem] md:bg-top h-[740px]\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent\"></div>\r\n        <div className=\"absolute bottom-8 text-center mx-4 md:mx-12 lg:mx-32 xl:mx-32 lg:text-left left-0 right-0\">\r\n          <h1 className=\"font-extrabold text-whiteOne text-2xl leading-9 md:text-[40px] md:leading-[44px] lg:text-6xl lg:w-[42rem] mb-2 md:mb-4\">\r\n            Take Flight with Our Expertly Designed Courses\r\n          </h1>\r\n          <p className=\"text-whiteOne font-bold text-lg md:text-3xl lg:text-2xl xl:text-2xl\">\r\n            Explore a Range of Aviation Training Programs\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <div className=\"relative bg-whiteOne z-0\">\r\n        <div className=\"hidden lg:block z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute top-60 left-0 right-0 w-full h-[300px]\"></div>\r\n        <div className=\"hidden lg:block absolute bg-dotsImage bottom-[34rem] right-0 bg-center bg-no-repeat w-10 h-36\"></div>\r\n        <div className=\"relative py-16 px-4 md:px-8 lg:px-32 xl:px-32\">\r\n          <div className=\"md:hidden lg:hidden xl:hidden\">\r\n            <h3 className=\"text-center text-xl font-bold mb-2 lg:mb-3 text-textBluePrimary\">\r\n              Courses we offer\r\n            </h3>\r\n            <p className=\"text-center text-sm md:text-base font-light text-customBlack mb-6\">\r\n              Courses Designed to Help You Soar Take Off with The Skyline Aviation Club !\r\n            </p>\r\n          </div>\r\n          <div className=\"md:hidden\">\r\n            <CourseCarousel />\r\n          </div>\r\n          <div className=\"hidden md:grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-12\">\r\n            {coursesData.map((item, index) => (\r\n              <div\r\n                className=\"p-4 rounded shadow-courseCardShadow md:h-[550px] lg:h-[580px] relative bg-whiteOne z-10\"\r\n                key={index}\r\n              >\r\n                <div className=\"rounded w-full h-[230px]\">\r\n                  <Image\r\n                    src={item.image}\r\n                    alt=\"coursesCardImage\"\r\n                    // className=\"w-full h-full rounded-xl\"\r\n                    className=\"h-full w-3/5 mx-auto rounded-xl\"\r\n                    width={328}\r\n                    height={200}\r\n                  />\r\n                </div>\r\n                <div className=\"flex mt-4 flex-col gap-2 items-center\">\r\n                  <h3 className=\"text-textBluePrimary font-bold text-2xl text-center\">\r\n                    {item.subTitle}\r\n                  </h3>\r\n                  <h4 className=\"text-customBlack text-center font-semibold text-base\">\r\n                    {item.title}\r\n                  </h4>\r\n                  <p className=\"text-customGrey100 font-medium text-sm\">\r\n                    {item.desc}\r\n                  </p>\r\n                 \r\n                  <Link\r\n                    href={`/course/${item.slug}`}\r\n                    className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}\r\n                  >\r\n                    <span className=\"font-bold\">View Course Details</span>\r\n                    <ArrowRight className=\"w-6 h-6 bg-white text-arrowIconColor rounded-full p-1\" />\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <FAQContainer />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default CoursesComponent;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA;AAHA;;;;;;;;AAKA,MAAM,mBAAmB;IACvB,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyH;;;;;;0CAGvI,8OAAC;gCAAE,WAAU;0CAAsE;;;;;;;;;;;;;;;;;;0BAKvF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkE;;;;;;kDAGhF,8OAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;0CAInF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACZ,mIAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;wCACC,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,KAAK;oDACf,KAAI;oDACJ,uCAAuC;oDACvC,WAAU;oDACV,OAAO;oDACP,QAAQ;;;;;;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,QAAQ;;;;;;kEAEhB,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEACV,KAAK,IAAI;;;;;;kEAGZ,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;wDAC5B,WAAW,CAAC,kFAAkF,CAAC;;0EAE/F,8OAAC;gEAAK,WAAU;0EAAY;;;;;;0EAC5B,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;uCA5BrB;;;;;;;;;;;;;;;;;;;;;;0BAoCf,8OAAC,kIAAA,CAAA,UAAY;;;;;;;AAGnB;uCAEe"}}, {"offset": {"line": 1430, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1436, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/app/course/page.jsx"], "sourcesContent": ["import CoursesComponent from \"@/components/CoursesComponent\"\r\n\r\nconst CoursePage = () => {\r\n  return (\r\n    <CoursesComponent />\r\n  )\r\n}\r\n\r\nexport default CoursePage;"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,aAAa;IACjB,qBACE,8OAAC,sIAAA,CAAA,UAAgB;;;;;AAErB;uCAEe"}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}