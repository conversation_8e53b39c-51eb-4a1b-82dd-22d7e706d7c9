"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[136],{6367:(e,t,r)=>{r.d(t,{Ay:()=>S});class n{constructor(e=0,t="Network Error"){this.status=e,this.text=t}}let l={origin:"https://api.emailjs.com",blockHeadless:!1,storageProvider:(()=>{if("undefined"!=typeof localStorage)return{get:e=>Promise.resolve(localStorage.getItem(e)),set:(e,t)=>Promise.resolve(localStorage.setItem(e,t)),remove:e=>Promise.resolve(localStorage.removeItem(e))}})()},o=e=>e?"string"==typeof e?{publicKey:e}:"[object Object]"===e.toString()?e:{}:{},a=async(e,t,r={})=>{let o=await fetch(l.origin+e,{method:"POST",headers:r,body:t}),a=await o.text(),i=new n(o.status,a);if(o.ok)return i;throw i},i=(e,t,r)=>{if(!e||"string"!=typeof e)throw"The public key is required. Visit https://dashboard.emailjs.com/admin/account";if(!t||"string"!=typeof t)throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!r||"string"!=typeof r)throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates"},s=e=>{if(e&&"[object Object]"!==e.toString())throw"The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/"},d=e=>e.webdriver||!e.languages||0===e.languages.length,u=()=>new n(451,"Unavailable For Headless Browser"),c=(e,t)=>{if(!Array.isArray(e))throw"The BlockList list has to be an array";if("string"!=typeof t)throw"The BlockList watchVariable has to be a string"},p=e=>!e.list?.length||!e.watchVariable,h=(e,t)=>e instanceof FormData?e.get(t):e[t],f=(e,t)=>{if(p(e))return!1;c(e.list,e.watchVariable);let r=h(t,e.watchVariable);return"string"==typeof r&&e.list.includes(r)},v=()=>new n(403,"Forbidden"),m=(e,t)=>{if("number"!=typeof e||e<0)throw"The LimitRate throttle has to be a positive number";if(t&&"string"!=typeof t)throw"The LimitRate ID has to be a non-empty string"},g=async(e,t,r)=>{let n=Number(await r.get(e)||0);return t-Date.now()+n},w=async(e,t,r)=>{if(!t.throttle||!r)return!1;m(t.throttle,t.id);let n=t.id||e;return await g(n,t.throttle,r)>0||(await r.set(n,Date.now().toString()),!1)},y=()=>new n(429,"Too Many Requests"),b=e=>{if(!e||"FORM"!==e.nodeName)throw"The 3rd parameter is expected to be the HTML form element or the style selector of the form"},x=e=>"string"==typeof e?document.querySelector(e):e,S={init:(e,t="https://api.emailjs.com")=>{if(!e)return;let r=o(e);l.publicKey=r.publicKey,l.blockHeadless=r.blockHeadless,l.storageProvider=r.storageProvider,l.blockList=r.blockList,l.limitRate=r.limitRate,l.origin=r.origin||t},send:async(e,t,r,n)=>{let c=o(n),p=c.publicKey||l.publicKey,h=c.blockHeadless||l.blockHeadless,m=c.storageProvider||l.storageProvider,g={...l.blockList,...c.blockList},b={...l.limitRate,...c.limitRate};return h&&d(navigator)?Promise.reject(u()):(i(p,e,t),s(r),r&&f(g,r))?Promise.reject(v()):await w(location.pathname,b,m)?Promise.reject(y()):a("/api/v1.0/email/send",JSON.stringify({lib_version:"4.4.1",user_id:p,service_id:e,template_id:t,template_params:r}),{"Content-type":"application/json"})},sendForm:async(e,t,r,n)=>{let s=o(n),c=s.publicKey||l.publicKey,p=s.blockHeadless||l.blockHeadless,h=l.storageProvider||s.storageProvider,m={...l.blockList,...s.blockList},g={...l.limitRate,...s.limitRate};if(p&&d(navigator))return Promise.reject(u());let S=x(r);i(c,e,t),b(S);let C=new FormData(S);return f(m,C)?Promise.reject(v()):await w(location.pathname,g,h)?Promise.reject(y()):(C.append("lib_version","4.4.1"),C.append("service_id",e),C.append("template_id",t),C.append("user_id",c),a("/api/v1.0/email/send-form",C))},EmailJSResponseStatus:n}},3434:(e,t,r)=>{r.d(t,{UC:()=>eE,YJ:()=>eH,In:()=>eL,q7:()=>eA,VF:()=>eV,p4:()=>eB,JU:()=>e_,ZL:()=>eD,bL:()=>eR,wn:()=>eF,PP:()=>eK,wv:()=>eO,l9:()=>eN,WT:()=>eI,LM:()=>eM});var n=r(2115),l=r(7650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(3610),i=r(9741),s=r(8068),d=r(8166),u=r(4256),c=r(9674),p=r(2292),h=r(196),f=r(7668),v=r(905),m=r(7323),g=r(3360),w=r(2317),y=r(1524),b=r(1488),x=r(6611),S=r(3543),C=r(5587),j=r(4065),k=r(5155),T=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],R="Select",[N,I,L]=(0,i.N)(R),[D,E]=(0,d.A)(R,[L,v.Bk]),M=(0,v.Bk)(),[H,_]=D(R),[A,B]=D(R),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:h,disabled:m,required:g,form:w}=e,y=M(t),[x,S]=n.useState(null),[C,j]=n.useState(null),[T,P]=n.useState(!1),R=(0,u.jH)(c),[I=!1,L]=(0,b.i)({prop:l,defaultProp:o,onChange:a}),[D,E]=(0,b.i)({prop:i,defaultProp:s,onChange:d}),_=n.useRef(null),B=!x||w||!!x.closest("form"),[V,K]=n.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,k.jsx)(v.bL,{...y,children:(0,k.jsxs)(H,{required:g,scope:t,trigger:x,onTriggerChange:S,valueNode:C,onValueNodeChange:j,valueNodeHasChildren:T,onValueNodeHasChildrenChange:P,contentId:(0,f.B)(),value:D,onValueChange:E,open:I,onOpenChange:L,dir:R,triggerPointerDownPosRef:_,disabled:m,children:[(0,k.jsx)(N.Provider,{scope:t,children:(0,k.jsx)(A,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{K(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{K(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),B?(0,k.jsxs)(ek,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:h,value:D,onChange:e=>E(e.target.value),disabled:m,form:w,children:[void 0===D?(0,k.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};V.displayName=R;var K="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=M(r),d=_(K,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=I(r),h=n.useRef("touch"),[f,m,w]=eT(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eP(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),w()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,k.jsx)(v.Mz,{asChild:!0,...i,children:(0,k.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":ej(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(y(),e.preventDefault())})})})});F.displayName=K;var O="SelectValue",G=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=_(O,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,x.N)(()=>{u(c)},[u,c]),(0,k.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ej(d.value)?(0,k.jsx)(k.Fragment,{children:a}):o})});G.displayName=O;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,k.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var q=e=>(0,k.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var W="SelectContent",z=n.forwardRef((e,t)=>{let r=_(W,e.__scopeSelect),[o,a]=n.useState();return((0,x.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,k.jsx)(Y,{...e,ref:t}):o?l.createPortal((0,k.jsx)(J,{scope:e.__scopeSelect,children:(0,k.jsx)(N.Slot,{scope:e.__scopeSelect,children:(0,k.jsx)("div",{children:e.children})})}),o):null});z.displayName=W;var[J,X]=D(W),Y=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:f,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:S,avoidCollisions:T,...P}=e,R=_(W,r),[N,L]=n.useState(null),[D,E]=n.useState(null),M=(0,s.s)(t,e=>L(e)),[H,A]=n.useState(null),[B,V]=n.useState(null),K=I(r),[F,O]=n.useState(!1),G=n.useRef(!1);n.useEffect(()=>{if(N)return(0,C.Eq)(N)},[N]),(0,p.Oh)();let U=n.useCallback(e=>{let[t,...r]=K().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&D&&(D.scrollTop=0),r===n&&D&&(D.scrollTop=D.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[K,D]),q=n.useCallback(()=>U([H,N]),[U,H,N]);n.useEffect(()=>{F&&q()},[F,q]);let{onOpenChange:z,triggerPointerDownPosRef:X}=R;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=X.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=X.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),X.current=null};return null!==X.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,z,X]),n.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[Y,$]=eT(e=>{let t=K().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eP(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),ee=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==R.value&&R.value===t||n)&&(A(e),n&&(G.current=!0))},[R.value]),et=n.useCallback(()=>null==N?void 0:N.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==R.value&&R.value===t||n)&&V(e)},[R.value]),en="popper"===l?Q:Z,el=en===Q?{side:u,sideOffset:f,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:x,hideWhenDetached:S,avoidCollisions:T}:{};return(0,k.jsx)(J,{scope:r,content:N,viewport:D,onViewportChange:E,itemRefCallback:ee,selectedItem:H,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:q,selectedItemText:B,position:l,isPositioned:F,searchRef:Y,children:(0,k.jsx)(j.A,{as:w.DX,allowPinchZoom:!0,children:(0,k.jsx)(h.n,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null===(t=R.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,k.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,k.jsx)(en,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...P,...el,onPlaced:()=>O(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...P.style},onKeyDown:(0,a.m)(P.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=K().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>U(t)),e.preventDefault()}})})})})})})});Y.displayName="SelectContentImpl";var Z=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(W,r),d=X(W,r),[u,c]=n.useState(null),[p,h]=n.useState(null),f=(0,s.s)(t,e=>h(e)),v=I(r),m=n.useRef(!1),w=n.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:S,focusSelectedItem:C}=d,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&b&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),h=parseInt(c.borderTopWidth,10),f=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=h+f+d+parseInt(c.paddingBottom,10)+g,x=Math.min(5*b.offsetHeight,w),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,P=b.offsetHeight/2,R=h+f+(b.offsetTop+P);if(R<=T){let e=a.length>0&&b===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,P+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+g);u.style.height=R+t+"px"}else{let e=a.length>0&&b===a[0].ref.current;u.style.top="0px";let t=Math.max(T,h+y.offsetTop+(e?j:0)+P);u.style.height=t+(w-R)+"px",y.scrollTop=R-T+y.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=x+"px",u.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,y,b,S,i.dir,l]);(0,x.N)(()=>j(),[j]);let[T,P]=n.useState();(0,x.N)(()=>{p&&P(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===w.current&&(j(),null==C||C(),w.current=!1)},[j,C]);return(0,k.jsx)($,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,k.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,k.jsx)(g.sG.div,{...a,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Z.displayName="SelectItemAlignedPosition";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=M(r);return(0,k.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Q.displayName="SelectPopperPosition";var[$,ee]=D(W,{}),et="SelectViewport",er=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=X(et,r),d=ee(et,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,k.jsx)(N.Slot,{scope:r,children:(0,k.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});er.displayName=et;var en="SelectGroup",[el,eo]=D(en),ea=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,f.B)();return(0,k.jsx)(el,{scope:r,id:l,children:(0,k.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});ea.displayName=en;var ei="SelectLabel",es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=eo(ei,r);return(0,k.jsx)(g.sG.div,{id:l.id,...n,ref:t})});es.displayName=ei;var ed="SelectItem",[eu,ec]=D(ed),ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=_(ed,r),c=X(ed,r),p=u.value===l,[h,v]=n.useState(null!=i?i:""),[m,w]=n.useState(!1),y=(0,s.s)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,l,o)}),b=(0,f.B)(),x=n.useRef("touch"),S=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,k.jsx)(eu,{scope:r,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,k.jsx)(N.ItemSlot,{scope:r,value:l,disabled:o,textValue:h,children:(0,k.jsx)(g.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>w(!0)),onBlur:(0,a.m)(d.onBlur,()=>w(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==x.current&&S()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===x.current&&S()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{if(x.current=e.pointerType,o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{var t;(null===(t=c.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(P.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ed;var eh="SelectItemText",ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=_(eh,r),u=X(eh,r),c=ec(eh,r),p=B(eh,r),[h,f]=n.useState(null),v=(0,s.s)(t,e=>f(e),c.onItemTextChange,e=>{var t;return null===(t=u.itemTextRefCallback)||void 0===t?void 0:t.call(u,e,c.value,c.disabled)}),m=null==h?void 0:h.textContent,w=n.useMemo(()=>(0,k.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=p;return(0,x.N)(()=>(y(w),()=>b(w)),[y,b,w]),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(g.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});ef.displayName=eh;var ev="SelectItemIndicator",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ec(ev,r).isSelected?(0,k.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});em.displayName=ev;var eg="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=X(eg,e.__scopeSelect),l=ee(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,k.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eg;var ey="SelectScrollDownButton",eb=n.forwardRef((e,t)=>{let r=X(ey,e.__scopeSelect),l=ee(ey,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,x.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,k.jsx)(ex,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ey;var ex=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=X("SelectScrollButton",r),s=n.useRef(null),d=I(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,x.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[d]),(0,k.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})}),eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,k.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})});eS.displayName="SelectSeparator";var eC="SelectArrow";function ej(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=M(r),o=_(eC,r),a=X(eC,r);return o.open&&"popper"===a.position?(0,k.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eC;var ek=n.forwardRef((e,t)=>{let{value:r,...l}=e,o=n.useRef(null),a=(0,s.s)(t,o),i=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[i,r]),(0,k.jsx)(S.s,{asChild:!0,children:(0,k.jsx)("select",{...l,ref:a,defaultValue:r})})});function eT(e){let t=(0,y.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eP(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==r?a:void 0}ek.displayName="BubbleSelect";var eR=V,eN=F,eI=G,eL=U,eD=q,eE=z,eM=er,eH=ea,e_=es,eA=ep,eB=ef,eV=em,eK=ew,eF=eb,eO=eS}}]);