"use client";
import { useState, useEffect } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import facultyMembers from "./../constant/facultyMembers";
import Image from "next/image";

const FacultyComponent = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [api, setApi] = useState();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 480);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    if (!api) {
      return;
    }

    setSelectedIndex(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setSelectedIndex(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const scrollTo = (index) => {
    if (api) {
      api.scrollTo(index);
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen relative pt-12">
      <div className="relative pt-8 tablet:py-12 mobile:pt-32 mobile:pb-8">
        <div className="absolute inset-0">
          <Image
            src="/Facultybg.svg"
            alt="Faculty Background"
            className="w-full !h-[90%] object-cover mobile:!h-[80%]"
            layout="fill"
          />
        </div>
        <div className="relative z-10 max-w-[80%] tablet:max-w-[100%] mobile:max-w-[100%] mobile:mx-4 mx-auto text-center px-4 pb-16 pt-32 tablet:pt-16 tablet:pb-0 mobile:rounded-[12px] mobile:bg-white mobile:py-4 mobile:shadow-cardsBoxShadow">
          <h1 className="font-bold text-[47px] text-textBluePrimary mb-5 tablet:text-[43px] tablet:mt-8 mobile:text-[20px] mobile:text-semibold">
            Meet Our Esteemed Faculty
          </h1>
          <p className="text-lg leading-[24px] text-left font-semibold text-[#464C3B] mobile:text-[13px] mobile:leading-[17px] mobile:px-4">
            At The Skyline Aviation Club, our faculty is a team of highly
            qualified and experienced professionals dedicated to nurturing the
            next generation of aviation leaders. Comprising seasoned pilot
            instructors, aviation instructors, retired airline managers, ATC
            Officers, and industry experts, they bring decades of hands-on
            expertise in fields such as pilot training covering all aviation
            subjects like Air Navigation, Aviation Meteorology, Air Regulations,
            Aircraft & Engine (General & Specific) and Radio Telephony (RTR)
            Licence and personality development. Each faculty member is not only
            an accomplished professional with impressive credentials but also a
            passionate mentor committed to delivering world-class training and
            guidance. Together, they form the backbone of our institution,
            ensuring our students receive unparalleled education and career
            support in their aviation journey.
          </p>
        </div>
      </div>
      <div className="container mx-auto mobile:mx-0 mobile:max-w-[373px] px-4 pt-0 py-8 relative mobile:mx-auto">
        {isMobile ? (
          <div>
            <Carousel
              setApi={setApi}
              opts={{
                loop: true,
              }}
            >
              <CarouselContent>
                {facultyMembers.map((member, index) => (
                  <CarouselItem key={index}>
                    <div className="flex flex-col gap-2 p-5 w-full max-w-[340px] bg-white shadow-md rounded-lg overflow-hidden items-center">
                      <div className="flex-shrink-0">
                        <Image
                          src={member.image}
                          alt={member.name}
                          width={100}
                          height={100}
                          className="rounded-lg w-[19rem] h-64 object-cover"
                        />
                      </div>
                      <div className="flex-1 text-left">
                        <h2 className="text-[20px] text-textBluePrimary font-bold">
                          {member.role}
                        </h2>
                        <p className="text-[16px] text-textBluePrimary font-medium leading-[35px]">
                          {member.name}
                        </p>
                        <p className="text-[15px] text-customGrey100 font-medium leading-[21px]">
                          {member.description}
                        </p>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
            <div className="flex justify-center items-center mt-4 space-x-2">
              {facultyMembers.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollTo(index)}
                  className={`${
                    selectedIndex === index + 1
                      ? "h-3 w-8 rounded-full bg-textBluePrimary"
                      : "h-3 w-2 rounded-full bg-carouselDotsBG"
                  }`}
                />
              ))}
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-6 items-center relative tablet:flex-row tablet:flex-wrap">
            {facultyMembers.map((member, index) => (
              <div
                key={index}
                className={`relative flex flex-col tablet:flex-row lg:flex-row gap-5 p-5 w-full max-w-[1000px] ${
                  index % 2 === 0
                    ? "tablet:flex-row lg:flex-row"
                    : "tablet:flex-row-reverse lg:flex-row-reverse"
                } bg-white shadow-md rounded-lg overflow-hidden items-center justify-center`}
              >
                <div className="flex-shrink-0">
                  <Image
                    src={member.image}
                    alt={member.name}
                    width={100}
                    height={100}
                    className="rounded-lg w-56 h-64 object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h2 className="text-[28px] text-textBluePrimary font-bold mobile:text-[20px]">
                    {member.role}
                  </h2>
                  <p className="text-[20px] text-textBluePrimary font-bold tablet:text-[20px] mobile:text-[18px]">
                    {member.name}
                  </p>
                  <p className="text-[15px] text-customGrey100 font-medium mobile:text-[14px] tablet:text-[15px]">
                    {member.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FacultyComponent;
