import "./globals.css";
import Footer from "@/components/common/Footer";
import Navbar from "@/components/common/Navbar";
import { Toaster } from "@/components/ui/toaster"; 

export const metadata = {
  title: "The Skyline Aviation Club",
  description: "Your Pilot Journey Begins Here !",
   icons: {
    icon: "/assets/Favicon.png",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
    <head>
       <link
          rel="icon"
          type="image/png"
          href="/assets/Favicon.png"
        ></link>
    </head> 
      <body className={`relative antialiased`}>
        <Navbar />
        {children}
        <Toaster />
        <Footer />
      </body>
    </html>
  );
}
