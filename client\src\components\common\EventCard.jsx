"use client";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { useState } from "react";

const EventCard = ({ cardData, isLandingPage }) => {
  const [showMore, setShowMore] = useState(null);
  const maxLength = 70;

  const toggleShowMore = (index) => {
    setShowMore((prevIndex) => (prevIndex === index ? null : index));
  };
  return (
    <>
      {/* <div className="h-full bg-white rounded-md shadow-md overflow-hidden flex flex-col"></div> */}
      {cardData.map((item, index) => (
        <Card key={index} className="w-full h-full flex flex-col bg-descLightBlack">
          <CardContent className="flex flex-col p-0 rounded-md h-full">
            {/* Image */}
            <Image
              src={item.image}
              width={1900}
              height={1600}
              alt="avatar"
              className="w-full h-[300px] sm:h-[300px] md:h-[220px] rounded-t-xl object-cover"
              quality={100}
            />

            {/* Description + Button */}
            <div className="bg-descLightBlack p-3 lg:p-6 rounded-b-lg flex flex-col flex-grow justify-between">
              <div>
                <h3 className="text-sm md:text-base font-bold mb-1 text-white">{item.title}</h3>
                <p className="text-xs md:text-sm font-medium text-white break-words">
                  {showMore === index ? item.desc : `${item.desc.slice(0, maxLength)}...`}
                </p>
              </div>

              <div className="mt-4">
                <button
                  onClick={() => toggleShowMore(index)}
                  className="text-white text-xs md:text-sm px-2 py-1 bg-blue-900 rounded-md hover:bg-blue-800 transition-colors"
                >
                  {showMore === index ? "Show Less" : "Show More"}
                </button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </>
  );
}

export default EventCard;