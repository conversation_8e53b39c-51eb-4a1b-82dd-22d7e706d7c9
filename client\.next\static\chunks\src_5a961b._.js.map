{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/navbarLinks.jsx"], "sourcesContent": ["const mainLinks = [\r\n    {\r\n        text: \"Home\",\r\n        link: \"/\",\r\n    },\r\n    {\r\n        text: \"Gallery\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"Courses\",\r\n        link: \"/course\",\r\n    },\r\n    {\r\n        text: \"Events\",\r\n        link: \"/events\",\r\n    },\r\n    {\r\n        text: \"About Us\",\r\n        link: \"/aboutus\",\r\n    },\r\n    {\r\n        text: \"Contact Us\",\r\n        link: \"/enquire\",\r\n    },\r\n]\r\n\r\nconst galleryLInks = [\r\n    {\r\n        text: \"Albums\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"TV and Press\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"Awards\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"VVIP Testimonials\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"Government Approvals\",\r\n        link: \"/gallery\",\r\n    },\r\n]\r\n\r\nconst coursesLinks = [\r\n    {\r\n        text:\"Indian Commercial Pilot\",\r\n        link: `indian-commercial-pilot-licence-course`,\r\n    },\r\n    {\r\n        text:\"American Commercial Pilot\",\r\n        link: `american-commercial-pilots-licence`,\r\n    },\r\n    {\r\n        text:\"CPL Package Program\",\r\n        link: `commercial-pilot-licence-package-program`,\r\n    },\r\n    {\r\n        text:\"Foreign CPL Conversion Course\",\r\n        link: `foreign-commercial-pilot-licence-conversion-course`,\r\n    },\r\n    {\r\n        text:\"Helicopter Pilot Training\",\r\n        link: `helicopter-commercial-pilot-licence-course`,\r\n    },\r\n    {\r\n        text:\"American Flight Dispatcher\",\r\n        link: `aircraft-flight-dispatcher-licence-course`,\r\n    },\r\n    {\r\n        text:\"Radio Telephony Licence\",\r\n        link: `radio-telephony-r-aeromobile-frtol-licence`,\r\n    },\r\n    {\r\n        text:\"Airhostess/Flight Purser\",\r\n        link: `airhostess-flight-purser-training-course`,\r\n    },\r\n    {\r\n        text:\"Airport Ground Staff\",\r\n        link: `airport-ground-staff-course`,\r\n    },\r\n    {\r\n        text:\"Aviation Foundation Course\",\r\n        link: `aviation-foundation-course`,\r\n    },\r\n    {\r\n        text:\"Aeroplane/Helicopter Training Workshop\",\r\n        link: `two-day-aeroplane-or-helicopter-training-workshop`,\r\n    },\r\n]\r\n\r\nexport { mainLinks, galleryLInks, coursesLinks };"], "names": [], "mappings": ";;;;;AAAA,MAAM,YAAY;IACd;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;CACH;AAED,MAAM,eAAe;IACjB;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;CACH;AAED,MAAM,eAAe;IACjB;QACI,MAAK;QACL,MAAM,CAAC,sCAAsC,CAAC;IAClD;IACA;QACI,MAAK;QACL,MAAM,CAAC,kCAAkC,CAAC;IAC9C;IACA;QACI,MAAK;QACL,MAAM,CAAC,wCAAwC,CAAC;IACpD;IACA;QACI,MAAK;QACL,MAAM,CAAC,kDAAkD,CAAC;IAC9D;IACA;QACI,MAAK;QACL,MAAM,CAAC,0CAA0C,CAAC;IACtD;IACA;QACI,MAAK;QACL,MAAM,CAAC,yCAAyC,CAAC;IACrD;IACA;QACI,MAAK;QACL,MAAM,CAAC,0CAA0C,CAAC;IACtD;IACA;QACI,MAAK;QACL,MAAM,CAAC,wCAAwC,CAAC;IACpD;IACA;QACI,MAAK;QACL,MAAM,CAAC,2BAA2B,CAAC;IACvC;IACA;QACI,MAAK;QACL,MAAM,CAAC,0BAA0B,CAAC;IACtC;IACA;QACI,MAAK;QACL,MAAM,CAAC,iDAAiD,CAAC;IAC7D;CACH"}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/courseDetailData.jsx"], "sourcesContent": ["// const eligibilityData = [\r\n//   {\r\n//     image: \"/assets/ageCriteriaImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Age Criteria\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         Minimum age - 17 Years <br />\r\n//         Maximum age - 35 Years\r\n//       </p>\r\n//     ),\r\n//   },\r\n//   {\r\n//     image: \"/assets/educationImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Education\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         10+2 with Maths and Physics <br /> or{\" \"}\r\n//         <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n//       </p>\r\n//     ),\r\n//   },\r\n//   {\r\n//     image: \"/assets/personalityImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Personality\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         Be able to read, write, speak and understand the English language\r\n//       </p>\r\n//     ),\r\n//   },\r\n//   {\r\n//     image: \"/assets/medicalImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Medical\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         DGCA Medical Class 2 Certificate (CA35) (Note: We Will arrange Medical\r\n//         Examination with our panel doctor who is DGCA approved Class II Medical\r\n//         Examiner)\r\n//       </p>\r\n//     ),\r\n//   },\r\n// ];\r\n\r\n// const jobAssistanceData = [\r\n//   {\r\n//     title: \"Comprehensive Career Grooming\",\r\n//     desc: \"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement\",\r\n//   },\r\n//   {\r\n//     title: \"Global Job Opportunities\",\r\n//     desc: \" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry.\",\r\n//   },\r\n//   {\r\n//     title: \"Proven Success\",\r\n//     desc: \"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !\",\r\n//   },\r\n// ];\r\n\r\nconst courseData = [\r\n  {\r\n    id: 1,\r\n    slug: \"indian-commercial-pilot-licence-course\",\r\n    courseName: \"DGCA\",\r\n    courseDesc: \"Indian Commercial Pilot Licence Course\",\r\n    introduction: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Exciting and Dynamic Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Prestige and Respect\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Travel the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"DGCA Commercial Pilot Licence Course – Airplane\",\r\n        courseDuration: \"2 years\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Course\",\r\n            duration: \"Theory Course - 4 Months (Full Time)\",\r\n          },\r\n          {\r\n            title:\r\n              \"Part II - Practical Flight Training of 200 Flight hours (185 Hours on Single Engine Land + 15 Hours on Multi Engine Lnad)\",\r\n            duration:\r\n              \"1 Year & 8 Months at our Affiliated Flight Training Organization (FTO)\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    subCourseStructure: [\r\n      {\r\n        title: \"DGCA Commercial Pilot Licence Written Exams\",\r\n        subjects: [\r\n          \"Air Navigation\",\r\n          \"Aviation Meteorology\",\r\n          \"Air Regulations\",\r\n          \"Aircraft & Engine (Technical) General & Specific\",\r\n        ],\r\n      },\r\n      {\r\n        title: \"WPC, Ministry of Telecommunication Exam\",\r\n        licence:\r\n          \"Radio Telephony (Restricted) Licence / Flight Radio Telephone Licence\",\r\n        parts: [\r\n          \"Part I: Radio Communication Procedures\",\r\n          \"Part II: Radio Theory\",\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 17 Years <br />\r\n            Maximum age - 35 Years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35)\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Comprehensive Career Grooming\",\r\n        desc: \"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement\",\r\n      },\r\n      {\r\n        title: \"Global Job Opportunities\",\r\n        desc: \" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry.\",\r\n      },\r\n      {\r\n        title: \"Proven Success\",\r\n        desc: \"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 2,\r\n    slug: \"american-commercial-pilots-licence\",\r\n    courseName: \"FAA USA\",\r\n    courseDesc: \"American Commercial Pilot's Licence\",\r\n    introduction: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Exciting and Dynamic Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Prestige and Respect\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Travel the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"American FAA Commercial Pilot Licence Course - Airplane\",\r\n        courseDuration: \"1 year\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Course (in India)\",\r\n            duration: \"Theory Course - 3 Months (inclusive of PPC, IRC, CPC)\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training (in USA)\",\r\n            duration:\r\n              \"Practical Flight Training - 9 Months (inclusive of 254 hrs with 25 hrs MEL) at our affiliated flight school Avel Flight School, Illinois, Chicago, USA\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 17 Years <br />\r\n            Maximum age - 35 Years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35) \r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Career Grooming & Job Assistance\",\r\n        desc: \"Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad.\",\r\n      },\r\n      {\r\n        title: \"Proven Placement Record\",\r\n        desc: \"So far, we have helped 398 students secure pilot jobs in various airlines worldwide.\",\r\n      },\r\n    ],\r\n\r\n    shortNote: \"Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad. So far we have helped 398 students to get Pilot Jobs in various airlines\",\r\n    visaAsistant: \"After completion of theory course, students have the option to choose a practical training location in India or United Stated of America. Students interested in going to USA will be assisted in Admission, I-20, DS-160, SEVIS and M1/F1 US Student Visa. So far we have helped 506 students procuring various students visas\",\r\n  },\r\n  {\r\n    id: 3,\r\n    slug: \"aircraft-flight-dispatcher-licence-course\",\r\n    courseName: \"FAA USA\",\r\n    courseDesc: \"Aircraft/Flight Dispatcher Licence Course\",\r\n    introduction: \"An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline. We Offer FAA(USA) Aircraft Dispatcher Licence Courses which will prepare you for entry into all Airlines worldwide.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn 1.5 Lacs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Challenging and Demanding Career\",\r\n        description:\r\n          \"This career demands a high level of precision, focus, and resilience under pressure, making it both challenging and highly rewarding for those passionate about aviation.\",\r\n      },\r\n      {\r\n        title: \"Hot Seat Job\",\r\n        description:\r\n          \"The term perfectly describes the role of an Aircraft Dispatcher because of the intense pressure, accountability, and critical decision-making required in real time\",\r\n      },\r\n      {\r\n        title: \"High Responsibities maintaining Multiple Aircrafts\",\r\n        description:\r\n          \"An Aircraft Dispatcher carries high responsibilities in maintaining multiple aircraft operations, including Simultaneous Flight Oversight, Real-Time Monitoring, Safety Assurance, Collaboration Across Teams. Prioritization Under Pressure\",\r\n      },\r\n      {\r\n        title: \"Future Progress Quickly\",\r\n        description:\r\n          \"The Aircraft Dispatcher role offers opportunities for quick future progress due to: High Demand in Aviation, Career Advancement, Transferable Skills, Licensing and Certification, Dynamic Work Environment.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"FAA (USA) Aircraft/Flight Dispatcher Licence Course\",\r\n        courseDuration: \"6 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Course (in India)\",\r\n            duration:\r\n              \"Theory Course - 4 Months (inclusive of CPL, RTR, IRC And ICAO Aviation English Course)\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training (in USA)\",\r\n            duration:\r\n              \"Practical Flight Training - 2 Months (inclusive of 200 hrs of Classroom Preparation )\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Training Eligibility: 21 years <br />\r\n            Licence Issuance: 23 years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14\",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Career Grooming & Job Assistance\",\r\n        desc: \"Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad.\",\r\n      },\r\n      {\r\n        title: \"Proven Placement Record\",\r\n        desc: \"So far, we have helped 398 students secure pilot jobs in various airlines worldwide.\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 4,\r\n    slug: \"radio-telephony-r-aeromobile-frtol-licence\",\r\n    courseName: \"DGCA/WPC\",\r\n    courseDesc: \"Radio Telephony (R) Aeromobile/FRTOL Licence\",\r\n    introduction: \"This professional course is structured in line with international radio regulations governing the aeronautical mobile service. It meets global standards, and candidates who achieve the required level of performance are awarded a government-recognized licence to operate.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Start your aviation journey with a competitive salary of ₹25,000 as an FRTOL holder, paving the way for a rewarding career.\",\r\n      },\r\n      {\r\n        title: \"Career Opportunities as an FRTOL\",\r\n        description:\r\n          \"Student completing this course and on passing examination and acquiring RT (R) Licence issued by wireless Planning & Co-Ordination Wing, Ministry of Communication, Government of India, can seek employment in the various Indian as well as Foreign Airlines, Airport authorities, Aviation Companies, Oil rigs etc. as Aeronautical Radio Communication Officer\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"FRTOL\",\r\n        courseDuration: \"2 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Radio Telephony and Aviation Phonetics\",\r\n            duration:\r\n              \"\",\r\n          },\r\n          {\r\n            title: \"Part II - Technical Knowledge\",\r\n            duration:\r\n              \"\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    theoryTrainingData: [],\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum Age: 18 years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            12th Pass/Appeared (Any Stream) <br /> or{\" \"}\r\n            any <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Job Placement for Radio Operators\",\r\n        desc: \"Successful students holding requisite aviation qualifications, including a valid RTR (Radio Telephony Restricted) licence issued by the Government of India, will be groomed and assisted for job placement as Radio Officers on oil rigs and in aviation companies abroad.\",\r\n      },\r\n      {\r\n        title: \"Proven Track Record\",\r\n        desc: \"So far, we have successfully helped 21 students secure Radio Operator jobs in various airlines and aviation organizations worldwide.\",\r\n      },\r\n    ],\r\n\r\n  },\r\n  {\r\n    id: 5,\r\n    slug: \"commercial-pilot-licence-package-program\",\r\n    courseName: \"Inclusive of Indian CPL + American CPL or Canadian CPL\",\r\n    courseDesc: \"Commercial Pilot Licence Package Program\",\r\n    introduction: \"The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"All-in-One Aviation Training\",\r\n        description:\r\n          \"Students receive all required aviation training under one roof, streamlining the learning process and eliminating the need for multiple institutions.\",\r\n      },\r\n      {\r\n        title: \"Economical Package Deal\",\r\n        description:\r\n          \"The bundled course structure is cost-effective for parents, offering maximum value across various aviation domains.\",\r\n      },\r\n      {\r\n        title: \"Versatile Career Preparation\",\r\n        description:\r\n          \"This program prepares students for multiple aviation roles, including Commercial Pilot, Flight Dispatcher, and Radio Telephony Officer, increasing job flexibility.\",\r\n      },\r\n      {\r\n        title: \"In-depth & Holistic Training\",\r\n        description:\r\n          \"The course delivers a comprehensive understanding of aviation, ensuring students are ready for the challenges of the aviation industry.\",\r\n      },\r\n      {\r\n        title: \"Recognized Certification & Logbook Endorsement\",\r\n        description:\r\n          \"Upon successful completion, students receive a course completion certificate and official logbook endorsement, boosting credibility for airline recruitment.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Complete Aviation Training Package – CPL, RTR, ATPL, Dispatcher & ELP\",\r\n        courseDuration: \"1 Year\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Training (in India)\",\r\n            duration:\r\n              \"Theory Course – 3 Months (Full-time in Mumbai, covering Commercial Pilot Training, RTR, Aviation English, ATPL/Dispatcher, and ELP)\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training (in USA)\",\r\n            duration:\r\n              \"Practical Training – 9 Months (Flight Training at our partnered facility in the USA)\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    subCourseStructure: [\r\n      {\r\n        title: \"FAA Subject Coverage\",\r\n        subjects: [\r\n          \"Air Regulation\",\r\n          \"Aviation Meteorology\",\r\n          \"Air Navigation\",\r\n          \"Audio Visual Training\",\r\n          \"Aircraft and Engine\",\r\n          \"Radio Communication\",\r\n          \"Aviation English\",\r\n          \"English Language Proficiency (ELP)\",\r\n        ],\r\n      },\r\n      {\r\n        title: \"FAA Training Levels\",\r\n        subjects: [\r\n          \"Student Pilot\",\r\n          \"Private Pilot\",\r\n          \"Instrument Rating\",\r\n          \"Commercial Pilot\",\r\n          \"RTR Licence\",\r\n          \"Aviation English\",\r\n          \"Airline Transport Pilot\",\r\n          \"Aircraft Dispatcher\",\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 17 Years <br />\r\n            Maximum age - 35 Years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35)\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 6,\r\n    slug: \"airhostess-flight-purser-training-course\",\r\n    courseName: \"Cabin Crew Training Program\",\r\n    courseDesc: \"Airhostess/Flight Purser Training Course\",\r\n    introduction: \"Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines.\",\r\n      },\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines.\",\r\n      },\r\n      {\r\n        title: \"High Flying Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Respect among Peers\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Fly all over the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"25% Scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n      // {\r\n      //   title: \"Job Placement Assistance\",\r\n      //   description:\r\n      //     \"Successful Students holding requisite aviation qualification i.e Diploma in In-Flight Management for Cabin Crew received from The Skyline Aviaiton Club will be groomed and assisted for Airlilne Job Interviews and Written Tests for Domestic airlines in India and International Airlines Abroad\",\r\n      // },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Cabin Crew Course\",\r\n        courseDuration: \"4 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Swimming\",\r\n            description: \"Essential life-saving skill for emergencies.\",\r\n          },\r\n          {\r\n            title: \"Yoga\",\r\n            description: \"Enhances flexibility and mental calmness.\",\r\n          },\r\n          {\r\n            title: \"Meditation\",\r\n            description: \"Improves focus and stress management.\",\r\n          },\r\n          {\r\n            title: \"Aviation Security\",\r\n            description: \"Trains in airport and aircraft security measures.\",\r\n          },\r\n          {\r\n            title: \"Reading Body Language\",\r\n            description:\r\n              \"Interpreting passenger behavior for better service and safety.\",\r\n          },\r\n          {\r\n            title: \"In-Flight Services\",\r\n            description: \"Handling passenger needs and service during flights.\",\r\n          },\r\n          {\r\n            title: \"In-Flight Food\",\r\n            description: \"Understanding catering and food management.\",\r\n          },\r\n          {\r\n            title: \"In-Flight Entertainment\",\r\n            description: \"Providing and managing onboard entertainment.\",\r\n          },\r\n          {\r\n            title: \"Communication Skills\",\r\n            description: \"Effective interaction with passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Aircraft Technical Knowledge\",\r\n            description:\r\n              \"Basic understanding of aircraft mechanics and systems.\",\r\n          },\r\n          {\r\n            title: \"Emergency Equipment on Board the Aircraft\",\r\n            description: \"Knowledge of emergency tools and their use.\",\r\n          },\r\n          {\r\n            title: \"The Brace Position\",\r\n            description:\r\n              \"Training on proper brace positions during emergencies.\",\r\n          },\r\n          {\r\n            title: \"Aircraft Evacuation\",\r\n            description: \"Procedures for safely evacuating an aircraft.\",\r\n          },\r\n          {\r\n            title: \"Survival (Sea, Jungle, Desert)\",\r\n            description: \"Techniques for survival in various terrains.\",\r\n          },\r\n          {\r\n            title: \"Security Measures to Check Hijacking and Bomb\",\r\n            description: \"Protocols to manage security threats.\",\r\n          },\r\n          {\r\n            title: \"Beauty and Perfect Airhostess or Flight Purser\",\r\n            description: \"Training on grooming and professionalism.\",\r\n          },\r\n          {\r\n            title: \"First Aid\",\r\n            description: \"Basic medical assistance for passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Etiquette and Manners (Finishing School)\",\r\n            description:\r\n              \"Refinement of interpersonal and professional behavior.\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    // subCourseStructure: [\r\n    //   {\r\n    //     title: \"Course Syllabus\",\r\n    //     subjects: [\r\n    //       \"Basic Aeronautical Knowledge\",\r\n    //       \"Aviation Physiology\",\r\n    //       \"Ophthalmology\",\r\n    //       \"Otorhinolaryngology\",\r\n    //       \"Cardiovascular System\",\r\n    //       \"Gynaecology & Obstetrics\",\r\n    //       \"Psychiatry in Aviation\",\r\n    //       \"Legislation, Rules and Regulations\",\r\n    //       \"Air Ambulance Operations\",\r\n    //       \"Medical Tourism\",\r\n    //     ],\r\n    //   },\r\n    // ],\r\n\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age & Height Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Age: 18–27 Years <br />\r\n            Girls: Min 5'2\" (Air Hostess)<br />\r\n            Boys: Min 5'7\" (Flight Purser)\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Educational Qualification\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 (Any Stream) – For Domestic<br />\r\n            Graduate (Any Stream) – For International\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Language Skills\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Must be able to read, speak, write, and understand English & Hindi{\"\\n\"}\r\n            Foreign Language: Added Advantage (For International Airlines)\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical Fitness\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center mt-2\">\r\n            Candidate must be medically fit as per airline standards.\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Job Placement Assistance for Cabin Crew\",\r\n        desc: \"Successful students holding the Diploma in In-Flight Management for Cabin Crew awarded by The Skyline Aviation Club will be groomed and assisted for airline job interviews and written tests. This includes placement support for both domestic airlines in India and international airlines abroad.\",\r\n      },\r\n    ],\r\n\r\n  },\r\n  {\r\n    id: 7,\r\n    slug: \"airport-ground-staff-course\",\r\n    courseName: \"Airport Ground Services\",\r\n    courseDesc: \"Airport Ground Staff Course\",\r\n    introduction: \"If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn up to ₹20,000 per month with domestic airlines and up to ₹50,000 per month with international airlines.\",\r\n      },\r\n      {\r\n        title: \"High Flying Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Respect among Peers\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Fly all over the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"25% Scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Job Placement Assistance\",\r\n        description:\r\n          \"Successful Students holding requisite aviation qualification i.e Diploma in In-Flight Management for Cabin Crew received from The Skyline Aviaiton Club will be groomed and assisted for Airlilne Job Interviews and Written Tests for Domestic airlines in India and International Airlines Abroad\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Airport Ground Staff\",\r\n        courseDuration: \"3 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Yoga\",\r\n            description: \"Enhances flexibility and mental calmness.\",\r\n          },\r\n          {\r\n            title: \"Meditation\",\r\n            description: \"Improves focus and stress management.\",\r\n          },\r\n          {\r\n            title:\r\n              \"Aviation Security, Geography with respect to all airports in india. Airline and airport codes.\",\r\n            description: \"Trains in airport and aircraft security measures.\",\r\n          },\r\n          {\r\n            title: \"Reading Body Language\",\r\n            description:\r\n              \"Interpreting passenger behavior for better service and safety.\",\r\n          },\r\n          {\r\n            title: \"Communication Skills\",\r\n            description: \"Effective interaction with passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Aircraft Technical Knowledge\",\r\n            description:\r\n              \"Basic understanding of aircraft mechanics and systems.\",\r\n          },\r\n          {\r\n            title: \"Emergency Equipment on Board the Aircraft\",\r\n            description: \"Knowledge of emergency tools and their use.\",\r\n          },\r\n          {\r\n            title: \"Security Measures to Check Hijacking and Bomb\",\r\n            description: \"Protocols to manage security threats.\",\r\n          },\r\n          {\r\n            title: \"Beauty and Perfect Airhostess or Flight Purser\",\r\n            description: \"Training on grooming and professionalism.\",\r\n          },\r\n          {\r\n            title:\r\n              \"First Aid Airport Announcements, Passenger handling, issue of boarding pass.\",\r\n            description: \"Basic medical assistance for passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Etiquette and Manners (Finishing School)\",\r\n            description:\r\n              \"Refinement of interpersonal and professional behavior.\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age & Height Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum Age: 18 Years{\"\\n\"}\r\n            Maximum Age: 27 Years{\"\\n\"}\r\n            Girls: Min 5'2\" (Air Hostess){\"\\n\"}\r\n            Boys: Min 5'7\" (Flight Purser)\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Educational Qualification\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 (Any Stream) – Domestic Airlines{\"\\n\"}\r\n            Graduate (Any Stream) – International Airlines\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Language Proficiency\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Must be able to read, write, speak & understand{\"\\n\"}\r\n            English and Hindi{\"\\n\"}\r\n            Foreign Language – Added Advantage for Graduates\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical & Other Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Medically Fit{\"\\n\"}\r\n            Must understand liability for surface or collision-related damages\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 8,\r\n    slug: \"aviation-foundation-course\",\r\n    courseName: \"SUMMER VACATION TRAINING PROGRAM\",\r\n    courseDesc: \"Aviation Foundation Course\",\r\n    introduction: \"Aviation Foundation Course is specially designed for school and college students to create foundation for pursuing airline careers. Students will be guided towards various airline opportunities available i.e. Airline Pilot, Aircraft/Flight Dispatcher, Air hostess, Flight Purser, Ground Hostess, Traffic Assistant, Aeronautical Engineer, Aircraft Maintenance Engineer, Air Traffic Control Officer, Radio Officer.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Benefits\",\r\n        description:\r\n          \"Young students have big dreams which change every day. This is a chance to get your child focused on what he or she loves. Students need not wait till 12th to decide a career path. One-stop complete aviation solution from zero to airline pilot. Early exposure to the aviation sector makes them much better prepared than other candidates.\"\r\n      },\r\n      {\r\n        title: \"Rewards\",\r\n        description:\r\n          \"Students will be issued a certificate of achievement on the completion of the course. Group photograph. Individual photograph with instructor and helicopter or aeroplane. Memento as Future Pilot. Airline Career Guidance Booklet.\"\r\n      }\r\n    ],\r\n    theoryTrainingData: [],\r\n    courseStructure: [\r\n      {\r\n        title: \"Student & Private Pilot Licence Training\",\r\n        courseDuration: \"Flexible (Introductory Program)\",\r\n        parts: [\r\n          {\r\n            title: \"Part I – Theory Training (DGCA Syllabus)\",\r\n            duration: \"Covers core subjects as per DGCA (Govt. of India) guidelines\",\r\n          },\r\n          {\r\n            title: \"Subjects Covered\",\r\n            duration: [\r\n              \"Air Regulation\",\r\n              \"Aviation Meteorology\",\r\n              \"Air Navigation\",\r\n              \"Audio Visual Training\",\r\n              \"Aircraft and Engine\",\r\n              \"Radio Communication\"\r\n            ].join(\", \"),\r\n          },\r\n          {\r\n            title: \"Part II – Practical Orientation\",\r\n            duration:\r\n              \"Includes familiarization briefing + discovery flight in helicopter/airplane for 15 minutes\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    subCourseStructure: [\r\n      {\r\n        title: \"Admission Procedure\",\r\n        subjects: [\r\n          \"Fill out the online registration form.\",\r\n          \"Send the following documents by courier or submit personally:\",\r\n          \"– Two passport-size photographs\",\r\n          \"– Certified true copy of your school ID card\",\r\n        ],\r\n      },\r\n    ],\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Who Can Apply?\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Students of Schools & Colleges{\"\\n\"}\r\n            Hobby Flyers Welcome{\"\\n\"}\r\n            Age: No Bar\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Course Duration\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            3 Weeks Duration{\"\\n\"}\r\n            3 Hours per Day\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Batch Timings\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Morning Batch: 10 AM – 1 PM{\"\\n\"}\r\n            Noon Batch: 2 PM – 5 PM\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Batch Size\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            20 Students per Batch\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 9,\r\n    slug: \"two-day-aeroplane-or-helicopter-training-workshop\",\r\n    courseName: \"Aeroplane/Helicopter Orientation Training\",\r\n    courseDesc: \"Two Day Aeroplane/Helicopter Training Workshop\",\r\n    introduction: \"Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter. They will be exposed at an early age to experience what it feels like to fly amongst the clouds. This course will open multiple career oppurtunities in the field of aviation.\",\r\n    careerProspects: [],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of ₹30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      }\r\n    ],\r\n\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of ₹25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated Banks for General Category Students.\",\r\n      },\r\n      {\r\n        title: \"Success Track Record\",\r\n        description:\r\n          \"So far, 54 students have been successful in procuring these benefits.\",\r\n      }\r\n    ]\r\n    ,\r\n    courseStructure: [\r\n      {\r\n        title: \"AEROPLANE/HELICOPTER TRAINING WORKSHOP\",\r\n        courseDuration: \"2 Day\",\r\n        parts: [\r\n          {\r\n            title: \"Theory Training\",\r\n            duration: (\r\n              <span className=\"text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n                • How helicopters fly<br />\r\n                • Parts of a helicopter and their functions<br />\r\n                • Cockpit orientation<br />\r\n                • Live ATC communication monitoring<br />\r\n                • Audio-visual guide on Principles of Flight and History of Aviation<br />\r\n                • Aviation career guidance<br />\r\n              </span>\r\n            )\r\n            // \"• How helicopters fly\\n• Parts of a helicopter and their functions\\n• Cockpit orientation\\n• Live ATC communication monitoring\\n• Audio-visual guide on Principles of Flight and History of Aviation\\n• Aviation career guidance\",\r\n          },\r\n          {\r\n            title: \"Practical Experience\",\r\n            duration:\r\n              (\r\n                <span className=\"text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n                  • 15-minute Helicopter Discovery Flight at Juhu Aerodrome<br />\r\n                  • Full-time tour director<br />\r\n                  • Travel insurance<br />\r\n                  • Breakfast & Lunch included\r\n                </span>\r\n              )\r\n          }\r\n        ]\r\n      }\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Eligibility Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 12 Years <br />\r\n            Minimum Class - VI onwards\r\n          </p>\r\n        )\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Dress Code\",\r\n        desc: \"School Uniform with valid School ID\",\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Batch Size\",\r\n        desc: \"Minimum 74 students (batches may be combined if minimum not met)\",\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Parental Consent\",\r\n        desc: \"Discovery Flight Consent Form signed by parent\",\r\n      }\r\n    ],\r\n\r\n  },\r\n  {\r\n    id: 10,\r\n    slug: \"foreign-commercial-pilot-licence-conversion-course\",\r\n    courseName: \"INDIAN COMMERCIAL PILOT LICENCE (DGCA)\",\r\n    courseDesc: \"Foreign Commercial Pilot Licence Conversion Course\",\r\n    introduction: \"The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Exciting and Dynamic Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Prestige and Respect\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Travel the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    // theoryTrainingData: [\r\n    //   {\r\n    //     title: \"Skyline UDAN Scholarship\",\r\n    //     description:\r\n    //       \"For economically weak students. Those students who are brilliant, capable and eligible to become Pilot but economically weak and are eligible for other Scholarship or Education Loan scheme of Government or Charitable Trusts for practical flight training course fees can be considered for grant of this scholarship.\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Skyline Women Empowerment Scholarship\",\r\n    //     description:\r\n    //       \"The Skyline Aviation Club is Corporate Member of Indian Women Pilot Association and supports IWPA and Government of India to promote “Beti Bachav, Beti Padhav” Campaign. To encourage young girls to take up aviation as career we have introduced this scholarship. This Scholarship is exclusively for Female Trainee Pilots who are members of Indian Women Pilots Association (IWPA)\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Skyline Jai Jawan Scholarship\",\r\n    //     description:\r\n    //       \"Founder of The Skyline Aviation Club Capt.AD Manek is ex NCC Cadet completed NCC training with rank of Sr. Under Officer in the year 1982 and even today he takes part in activities of NCC. To honor services of Capt.AD Manek Maharashtra State NCC Directorate have declared “Capt.AD Manek Scholarship” for Best Cadets of Maharashtra State from the year 2023. To encourage NCC Cadets and Sons /Daughters of Defence Personnels we have introduced this scholarship. This Scholarship is exclusively for NCC Cadets holding “B” or “C” Certificates and Sons, Daughters of Defence Personnels including Indian Army, Indian Navy, Indian Airforce, Coast Guard, and Border Security Force. (Note: Scholarship for Theory (Ground Training) Course will be granted on merit basis subject to score in Skyline Scholarship Examinations.)\",\r\n    //   },\r\n    // ],\r\n    // practicalTrainingData: [\r\n    //   {\r\n    //     title: \"Educational Loan from Bank (Open for all)\",\r\n    //     description:\r\n    //       \"Educational Loan of 85 % of Total Course fees can be arranged through our affiliated bank viz. Axis Bank subject to eligibility criteria of student and parents\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Scholarship from TATA Trusts (Open for all)\",\r\n    //     description:\r\n    //       \"Students completing Ground Training successfully and passes all DGCA CPL Written Examinations and complete initial flight training may be considered for partial Scholarship by TATA Trusts to encourage youth to join and make career in aviation\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Indian Women Pilot Association (IWPA) Financial Assistance (Reserved for female trainee pilots)\",\r\n    //     description:\r\n    //       \"Female Students completing Ground Training successfully and passes all DGCA CPL Written Examinations and complete initial flight training may be considered for partial Scholarship by IWPA to encourage young girls to join and make career in aviation\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Post Matric Scholarship by Department of Social Justice & Empowerment, Government of India (Reserved for students of all states of India and belonging to Scheduled Caste.)\",\r\n    //     description:\r\n    //       \"Scholarship of Rs.45 Lacs available under Post Matric Scholarship Scheme of Department of Social Justice & Empowerment, Govt. of India for Students belonging to scheduled caste.\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Commercial Pilot Licence Educational Loan by Government of Gujarat (Reserved for students of Gujarat State and belonging to Scheduled Caste or Scheduled Tribe or Other Backward Class)\",\r\n    //     description:\r\n    //       \"Educational loan of Rs.25 Lacs available under Commercial Pilot Licence loan Scheme of Department of Social Justice & Empowerment, Government of Gujarat\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Videsh Abhiyash Yojna Educational Loan by Government of Gujarat (Reserved for students of Gujarat State and belonging to Scheduled Caste or Scheduled Tribe or Other Backward Class)\",\r\n    //     description:\r\n    //       \"Educational loan of Rs.15 Lacs available under Videsh Abhiyash Yojna loan Scheme of Department of Social Justice & Empowerment, Government of Gujarat. (This loan can be used for FAA Flight / Aircraft Dispatcher Course in USA)\",\r\n    //   },\r\n    // ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Foreign Pilot Licence Conversion Course (DGCA) - Theory Course\",\r\n        courseDuration: \"2 months\",\r\n        parts: [\r\n          {\r\n            title:\r\n              \"Prepares for DGCA Commercial Pilot Licence conversion Exams\",\r\n            duration: \"\",\r\n            papers: [\r\n              \"Paper 1 - Composite Paper (Air navigation & Air Meteorology)\",\r\n              \"Paper 2 - Air Regulations\",\r\n            ],\r\n          },\r\n          {\r\n            title: \"Prepare for WPC, Ministry of Telecommunication Exam\",\r\n            duration: \"\",\r\n            papers: [\r\n              \"Part I: Radio Communication Procedures\",\r\n              \"Part II: Radio Theory\",\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age: Age 12+ years <br />\r\n            Minimum class: VI onwards\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35)\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    shortNote: \"Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad under Airline Prep Course.  So far we have helped 720 plus students to get Pilot Jobs in various airlines\",\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Comprehensive Career Grooming\",\r\n        desc: \"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement\",\r\n      },\r\n      {\r\n        title: \"Global Job Opportunities\",\r\n        desc: \" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry.\",\r\n      },\r\n      {\r\n        title: \"Proven Success\",\r\n        desc: \"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 11,\r\n    slug: \"helicopter-commercial-pilot-licence-course\",\r\n    courseName: \"HCPL\",\r\n    courseDesc: \"Helicopter Commercial Pilot Licence Course\",\r\n    introduction:\r\n      \"The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Professional Opportunities\",\r\n        description:\r\n          \"Work with private helicopter operators, tourism companies, charter services, and emergency medical services (HEMS).\",\r\n      },\r\n      {\r\n        title: \"Government and Defense Roles\",\r\n        description:\r\n          \"Opportunities in government agencies, paramilitary forces, and search & rescue operations.\",\r\n      },\r\n      {\r\n        title: \"Corporate and VIP Transport\",\r\n        description:\r\n          \"Serve as pilot for high-profile individuals, corporate executives, and political dignitaries.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [],\r\n    practicalTrainingData: [],\r\n    courseStructure: [\r\n      {\r\n        title: \"HCPL Training Program\",\r\n        courseDuration: \"12 to 18 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Training\",\r\n            duration:\r\n              \"Covers DGCA syllabus including Air Navigation, Aviation Meteorology, Air Regulations, Aircraft & Engines (Technical), and Radio Telephony.\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training\",\r\n            duration:\r\n              \"Minimum of 150 hours of helicopter flying at an approved FTO under instructor supervision.\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: \"Minimum 18 years of age at the time of licence issue\",\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: \"10+2 with Physics and Mathematics or equivalent (NIOS acceptable)\",\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: \"Class II Medical for training & Class I Medical for licence issuance\",\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Language Proficiency\",\r\n        desc: \"Must be able to read, write, speak and understand English\",\r\n      },\r\n    ],\r\n  }\r\n\r\n];\r\n\r\nexport {courseData };\r\n"], "names": [], "mappings": "AAAA,4BAA4B;AAC5B,MAAM;AACN,6CAA6C;AAC7C,2CAA2C;AAC3C,6BAA6B;AAC7B,cAAc;AACd,iHAAiH;AACjH,wCAAwC;AACxC,iCAAiC;AACjC,aAAa;AACb,SAAS;AACT,OAAO;AACP,MAAM;AACN,2CAA2C;AAC3C,2CAA2C;AAC3C,0BAA0B;AAC1B,cAAc;AACd,iHAAiH;AACjH,qDAAqD;AACrD,0EAA0E;AAC1E,aAAa;AACb,SAAS;AACT,OAAO;AACP,MAAM;AACN,6CAA6C;AAC7C,2CAA2C;AAC3C,4BAA4B;AAC5B,cAAc;AACd,iHAAiH;AACjH,4EAA4E;AAC5E,aAAa;AACb,SAAS;AACT,OAAO;AACP,MAAM;AACN,yCAAyC;AACzC,2CAA2C;AAC3C,wBAAwB;AACxB,cAAc;AACd,iHAAiH;AACjH,iFAAiF;AACjF,kFAAkF;AAClF,oBAAoB;AACpB,aAAa;AACb,SAAS;AACT,OAAO;AACP,KAAK;AAEL,8BAA8B;AAC9B,MAAM;AACN,8CAA8C;AAC9C,gMAAgM;AAChM,OAAO;AACP,MAAM;AACN,yCAAyC;AACzC,8LAA8L;AAC9L,OAAO;AACP,MAAM;AACN,+BAA+B;AAC/B,sJAAsJ;AACtJ,OAAO;AACP,KAAK;;;;;;AAEL,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,OACE;wBACF,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,SACE;gBACF,OAAO;oBACL;oBACA;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,6LAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,6LAAC;;;;;wBAAK;wBAAI;sCACtC,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,6LAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,6LAAC;;;;;wBAAK;wBAAI;sCACtC,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;QAED,WAAW;QACX,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCACvE,6LAAC;;;;;wBAAK;;;;;;;YAI3C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,6LAAC;;;;;wBAAK;wBAAI;sCACtC,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB,EAAE;QACtB,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCACtE,6LAAC;;;;;wBAAK;wBAAI;wBAAI;sCAC1C,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAGjD;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IAEH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,6LAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,6LAAC;;;;;wBAAK;wBAAI;sCACtC,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SAMD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;iBACD;YACH;SACD;QACD,wBAAwB;QACxB,MAAM;QACN,gCAAgC;QAChC,kBAAkB;QAClB,wCAAwC;QACxC,+BAA+B;QAC/B,yBAAyB;QACzB,+BAA+B;QAC/B,iCAAiC;QACjC,oCAAoC;QACpC,kCAAkC;QAClC,8CAA8C;QAC9C,oCAAoC;QACpC,2BAA2B;QAC3B,SAAS;QACT,OAAO;QACP,KAAK;QAEL,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCACrF,6LAAC;;;;;wBAAK;sCACM,6LAAC;;;;;wBAAK;;;;;;;YAIzC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCACtE,6LAAC;;;;;wBAAK;;;;;;;YAI5C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBACnC;wBAAK;;;;;;;YAI9E;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAAuE;;;;;;YAIxF;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IAEH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OACE;wBACF,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OACE;wBACF,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;iBACD;YACH;SACD;QACD,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBAChF;wBAAK;wBACL;wBAAK;wBACG;wBAAK;;;;;;;YAIzC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBAChE;wBAAK;;;;;;;YAIjD;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBACtD;wBAAK;wBACnC;wBAAK;;;;;;;YAI7B;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBACxF;wBAAK;;;;;;;YAIzB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB,EAAE;QACtB,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,OAAO;wBACP,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD,CAAC,IAAI,CAAC;oBACT;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;SACD;QACD,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBACvE;wBAAK;wBACf;wBAAK;;;;;;;YAIhC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBACrF;wBAAK;;;;;;;YAI5B;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;wBAC1E;wBAAK;;;;;;;YAIvC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB,EAAE;QACnB,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QAED,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QAED,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,wBACE,6LAAC;4BAAK,WAAU;;gCAA2F;8CACpF,6LAAC;;;;;gCAAK;8CACgB,6LAAC;;;;;gCAAK;8CAC5B,6LAAC;;;;;gCAAK;8CACQ,6LAAC;;;;;gCAAK;8CAC2B,6LAAC;;;;;gCAAK;8CAChD,6LAAC;;;;;;;;;;;oBAIjC;oBACA;wBACE,OAAO;wBACP,wBAEI,6LAAC;4BAAK,WAAU;;gCAA2F;8CAChD,6LAAC;;;;;gCAAK;8CACtC,6LAAC;;;;;gCAAK;8CACb,6LAAC;;;;;gCAAK;;;;;;;oBAIhC;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,6LAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;SACD;IAEH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,wBAAwB;QACxB,MAAM;QACN,yCAAyC;QACzC,mBAAmB;QACnB,sUAAsU;QACtU,OAAO;QACP,MAAM;QACN,sDAAsD;QACtD,mBAAmB;QACnB,qYAAqY;QACrY,OAAO;QACP,MAAM;QACN,8CAA8C;QAC9C,mBAAmB;QACnB,0zBAA0zB;QAC1zB,OAAO;QACP,KAAK;QACL,2BAA2B;QAC3B,MAAM;QACN,0DAA0D;QAC1D,mBAAmB;QACnB,2KAA2K;QAC3K,OAAO;QACP,MAAM;QACN,4DAA4D;QAC5D,mBAAmB;QACnB,8PAA8P;QAC9P,OAAO;QACP,MAAM;QACN,gHAAgH;QAChH,mBAAmB;QACnB,oQAAoQ;QACpQ,OAAO;QACP,MAAM;QACN,4LAA4L;QAC5L,mBAAmB;QACnB,6LAA6L;QAC7L,OAAO;QACP,MAAM;QACN,wMAAwM;QACxM,mBAAmB;QACnB,oKAAoK;QACpK,OAAO;QACP,MAAM;QACN,qMAAqM;QACrM,mBAAmB;QACnB,6OAA6O;QAC7O,OAAO;QACP,KAAK;QACL,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OACE;wBACF,UAAU;wBACV,QAAQ;4BACN;4BACA;yBACD;oBACH;oBACA;wBACE,OAAO;wBACP,UAAU;wBACV,QAAQ;4BACN;4BACA;yBACD;oBACH;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC3E,6LAAC;;;;;wBAAK;;;;;;;YAIvC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,6LAAC;;;;;wBAAK;wBAAI;sCACtC,6LAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,6LAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,WAAW;QACX,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cACE;QACF,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB,EAAE;QACtB,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;SACD;IACH;CAED"}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 1909, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1915, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/dropdown-menu.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}>\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className)}\r\n    {...props} />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props} />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}) => {\r\n  return (\r\n    (<span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props} />)\r\n  );\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,eAAe,gLAAsB,IAAI;AAE/C,MAAM,sBAAsB,gLAAsB,OAAO;AAEzD,MAAM,oBAAoB,gLAAsB,KAAK;AAErD,MAAM,qBAAqB,gLAAsB,MAAM;AAEvD,MAAM,kBAAkB,gLAAsB,GAAG;AAEjD,MAAM,yBAAyB,gLAAsB,UAAU;AAE/D,MAAM,uCAAyB,8JAAM,UAAU,MAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzF,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YACR;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxE,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAEb,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBACrF,6LAAC,gLAAsB,MAAM;kBAC3B,cAAA,6LAAC,gLAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wGACA,oVACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAGf,oBAAoB,WAAW,GAAG,gLAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACzE,6LAAC,gLAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAEb,iBAAiB,WAAW,GAAG,gLAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7F,6LAAC,gLAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BACT,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,gLAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACjF,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BACT,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBAC1E,6LAAC,gLAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC,SAAS,QAAQ;QACnE,GAAG,KAAK;;;;;;;AAEb,kBAAkB,WAAW,GAAG,gLAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACvE,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAEb,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACJ;IACC,qBACG,6LAAC;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAEf;OATM;AAUN,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/common/Navbar.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { ArrowRight, ChevronDown, ChevronUp, Menu, X } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { mainLinks, coursesLinks } from \"@/constant/navbarLinks\";\r\nimport { courseData } from \"@/constant/courseDetailData\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\nconst Navbar = () => {\r\n  const [toggleMobileMenu, setToggleMobileMenu] = useState(false);\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n\r\n  const handleToggleMobileMenu = () => {\r\n    setToggleMobileMenu((prev) => !prev);\r\n    setDropdownOpen(false); // Close dropdown when toggling menu\r\n  };\r\n\r\n  const toggleDropdown = () => setDropdownOpen((prev) => !prev);\r\n\r\n  const closeMobileMenu = () => {\r\n    setToggleMobileMenu(false);\r\n    setDropdownOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"absolute left-0 right-0 top-8 md:w-4/5 mx-4 md:mx-auto lg:mx-auto z-20\">\r\n      <nav className=\"bg-transparent bg-navbarBGPrimary rounded-full backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary px-3 py-2.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center justify-between w-full\">\r\n          {/* Logo */}\r\n          <Link href={\"/\"}>\r\n            <Image\r\n              src=\"/skyline_logo.png\"\r\n              alt=\"The Skyline Aviation Club Logo\"\r\n              width={50}\r\n              height={50}\r\n              className=\"rounded-full\"\r\n            />\r\n          </Link>\r\n\r\n          {/* Desktop Menu */}\r\n          <div className=\"hidden lg:flex items-center md:gap-3 lg:gap-5 xl:gap-16\">\r\n            {mainLinks.map((item, index) =>\r\n              item.text === \"Courses\" ? (\r\n                <DropdownMenu\r\n                  onOpenChange={(open) => setDropdownOpen(open)}\r\n                  key={index}\r\n                >\r\n                  <DropdownMenuTrigger className=\"text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2\">\r\n                    {item.text}\r\n                    <ChevronDown\r\n                      className={`w-5 h-5 transition-transform ${\r\n                        dropdownOpen ? \"rotate-180\" : \"rotate-0\"\r\n                      }`}\r\n                    />\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent className=\"bg-navbarBGPrimary backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary flex flex-col w-42 gap-3 p-4 rounded-xl\">\r\n                    {coursesLinks.map((course, index) => (\r\n                      <DropdownMenuItem\r\n                        asChild\r\n                        key={index}\r\n                        className=\"p-0 focus:bg-transparent hover:text-textBluePrimary\"\r\n                      >\r\n                        <Link\r\n                          className=\"text-customBlack font-semibold hover:text-textBluePrimary transition block\"\r\n                          href={`/course/${course.link}`}\r\n                        >\r\n                          {course.text}\r\n                        </Link>\r\n                      </DropdownMenuItem>\r\n                    ))}\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              ) : (\r\n                <Link\r\n                  key={index}\r\n                  href={item.link}\r\n                  className=\"text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2\"\r\n                >\r\n                  {item.text}\r\n                </Link>\r\n              )\r\n            )}\r\n          </div>\r\n\r\n          {/* Enquire Now Button (Desktop) */}\r\n          <Link\r\n            href=\"/enquire\"\r\n            className=\"hidden lg:flex items-center gap-2 bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 md:py-2 md:pr-2 md:pl-6 rounded-full border border-transparent\"\r\n          >\r\n            Enquire Now\r\n            <ArrowRight\r\n              size={18}\r\n              className=\"ml-2 w-8 h-8 p-1 bg-white rounded-full text-arrowIconColor\"\r\n            />\r\n          </Link>\r\n\r\n          {/* Mobile Menu Button */}\r\n          <div className=\"lg:hidden flex\">\r\n            <button onClick={handleToggleMobileMenu} type=\"button\">\r\n              {toggleMobileMenu ? <X size={28} /> : <Menu size={28} />}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Mobile Menu */}\r\n      <div\r\n        className={`w-full flex flex-col absolute right-0 top-[4.5rem] bg-navbarBGPrimary border-navbarBGPrimary shadow-dropdownShadow backdrop-blur-[20px] rounded-lg transition-all duration-300 ease-in-out overflow-hidden transform ${\r\n          toggleMobileMenu\r\n            ? \"scale-100 opacity-100 max-h-[600px]\"\r\n            : \"scale-95 opacity-0 max-h-0 pointer-events-none\"\r\n        }`}\r\n      >\r\n        {mainLinks.map((item, index) =>\r\n          item.text === \"Courses\" ? (\r\n            <div className=\"relative\" key={index}>\r\n              <button\r\n                onClick={toggleDropdown}\r\n                className=\"p-3 text-customBlack font-semibold w-full flex items-center justify-between\"\r\n              >\r\n                Courses\r\n                {dropdownOpen ? <ChevronUp /> : <ChevronDown />}\r\n              </button>\r\n              <div\r\n                className={`transition-all duration-300 ease-in-out overflow-hidden ${\r\n                  dropdownOpen ? \"max-h-96 opacity-100\" : \"max-h-0 opacity-0\"\r\n                }`}\r\n              >\r\n                <ul className=\"ml-4\">\r\n                  {courseData.map((course, index) => (\r\n                    <li key={index}>\r\n                      <Link\r\n                        href={`/course/${course.slug}`}\r\n                        className=\"block text-customBlack font-semibold hover:text-textBluePrimary transition p-2\"\r\n                        onClick={closeMobileMenu}\r\n                      >\r\n                        {course.courseDesc}\r\n                      </Link>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <Link\r\n              key={index}\r\n              href={item.link}\r\n              className=\"p-3 text-customBlack font-semibold hover:text-textBluePrimary transition\"\r\n              onClick={closeMobileMenu}\r\n            >\r\n              {item.text}\r\n            </Link>\r\n          )\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Navbar;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AACA;AACA;AAJA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;;;;AAcA,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,yBAAyB;QAC7B,oBAAoB,CAAC,OAAS,CAAC;QAC/B,gBAAgB,QAAQ,oCAAoC;IAC9D;IAEA,MAAM,iBAAiB,IAAM,gBAAgB,CAAC,OAAS,CAAC;IAExD,MAAM,kBAAkB;QACtB,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;sCACV,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACZ,kIAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,MAAM,QACpB,KAAK,IAAI,KAAK,0BACZ,6LAAC,+IAAA,CAAA,eAAY;oCACX,cAAc,CAAC,OAAS,gBAAgB;;sDAGxC,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,WAAU;;gDAC5B,KAAK,IAAI;8DACV,6LAAC,uNAAA,CAAA,cAAW;oDACV,WAAW,CAAC,6BAA6B,EACvC,eAAe,eAAe,YAC9B;;;;;;;;;;;;sDAGN,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,WAAU;sDAC5B,kIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC,+IAAA,CAAA,mBAAgB;oDACf,OAAO;oDAEP,WAAU;8DAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,WAAU;wDACV,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;kEAE7B,OAAO,IAAI;;;;;;mDAPT;;;;;;;;;;;mCAdN;;;;yDA4BP,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL;;;;;;;;;;sCAWb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCACT,MAAM;oCACN,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,SAAS;gCAAwB,MAAK;0CAC3C,iCAAmB,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,6LAAC;gBACC,WAAW,CAAC,qNAAqN,EAC/N,mBACI,wCACA,kDACJ;0BAED,kIAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,MAAM,QACpB,KAAK,IAAI,KAAK,0BACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;;oCACX;oCAEE,6BAAe,6LAAC,mNAAA,CAAA,YAAS;;;;6DAAM,6LAAC,uNAAA,CAAA,cAAW;;;;;;;;;;;0CAE9C,6LAAC;gCACC,WAAW,CAAC,wDAAwD,EAClE,eAAe,yBAAyB,qBACxC;0CAEF,cAAA,6LAAC;oCAAG,WAAU;8CACX,uIAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACvB,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;gDAC9B,WAAU;gDACV,SAAS;0DAER,OAAO,UAAU;;;;;;2CANb;;;;;;;;;;;;;;;;uBAfc;;;;6CA6B/B,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAU;wBACV,SAAS;kCAER,KAAK,IAAI;uBALL;;;;;;;;;;;;;;;;AAYnB;GArJM;KAAA;uCAuJS"}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2426, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/hooks/use-toast.js"], "sourcesContent": ["\"use client\";\r\n// Inspired by react-hot-toast library\r\nimport * as React from \"react\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\"\r\n}\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString();\r\n}\r\n\r\nconst toastTimeouts = new Map()\r\n\r\nconst addToRemoveQueue = (toastId) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state, action) => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      };\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t),\r\n      };\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t),\r\n      };\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      };\r\n  }\r\n}\r\n\r\nconst listeners = []\r\n\r\nlet memoryState = { toasts: [] }\r\n\r\nfunction dispatch(action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\nfunction toast({\r\n  ...props\r\n}) {\r\n  const id = genId()\r\n\r\n  const update = (props) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    };\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  };\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": [], "mappings": ";;;;;AACA,sCAAsC;AACtC;;AAFA;;AAIA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAE3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AAEA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAO;IAC7B,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAC3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBACR;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAAY,EAAE;AAEpB,IAAI,cAAc;IAAE,QAAQ,EAAE;AAAC;AAE/B,SAAS,SAAS,MAAM;IACtB,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAEA,SAAS,MAAM,EACb,GAAG,OACJ;IACC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,8JAAM,QAAQ,CAAC;IAEzC,8JAAM,SAAS;8BAAC;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAY,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAClE;AACF;GAlBS"}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/toast.jsx"], "sourcesContent": ["\"use client\";\r\nimport * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva } from \"class-variance-authority\";\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n        success: \"success group border-green-500 bg-green-500 text-neutral-50\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nconst Toast = React.forwardRef(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    (<ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props} />)\r\n  );\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastAction = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nToastAction.displayName = ToastPrimitives.Action.displayName\r\n\r\nconst ToastClose = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}>\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold [&+div]:text-xs\", className)}\r\n    {...props} />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description ref={ref} className={cn(\"text-sm opacity-90\", className)} {...props} />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\nexport { ToastProvider, ToastViewport, Toast, ToastTitle, ToastDescription, ToastClose, ToastAction };\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AAEA;AAGA;AAJA;AAEA;AAJA;;;;;;;AAQA,MAAM,gBAAgB,qKAAgB,QAAQ;AAE9C,MAAM,8BAAgB,8JAAM,UAAU,MAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC,qKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;;AAEb,cAAc,WAAW,GAAG,qKAAgB,QAAQ,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IAChE,qBACG,6LAAC,qKAAgB,IAAI;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAEf;;AACA,MAAM,WAAW,GAAG,qKAAgB,IAAI,CAAC,WAAW;AAEpD,MAAM,4BAAc,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC,qKAAgB,MAAM;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2dACA;QAED,GAAG,KAAK;;;;;;;AAEb,YAAY,WAAW,GAAG,qKAAgB,MAAM,CAAC,WAAW;AAE5D,MAAM,2BAAa,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,qKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBACT,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,qKAAgB,KAAK,CAAC,WAAW;AAE1D,MAAM,2BAAa,8JAAM,UAAU,OAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,qKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAEb,WAAW,WAAW,GAAG,qKAAgB,KAAK,CAAC,WAAW;AAE1D,MAAM,iCAAmB,8JAAM,UAAU,QAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,6LAAC,qKAAgB,WAAW;QAAC,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QAAa,GAAG,KAAK;;;;;;;AAElG,iBAAiB,WAAW,GAAG,qKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 2722, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2728, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/toaster.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"@/components/ui/toast\"\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    (<ToastProvider>\r\n      {toasts.map(function ({ id, title, description, action, ...props }) {\r\n        return (\r\n          (<Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>)\r\n        );\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>)\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACG,6LAAC,oIAAA,CAAA,gBAAa;;YACZ,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACG,6LAAC,oIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACxB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,oIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,oIAAA,CAAA,aAAU;;;;;;mBARA;;;;;YAWjB;0BACA,6LAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,+HAAA,CAAA,WAAQ;;;KADb"}}, {"offset": {"line": 2807, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}