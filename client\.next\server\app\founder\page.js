(()=>{var e={};e.id=562,e.ids=[562],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},2140:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>m,routeModule:()=>c,tree:()=>d});var a=i(260),n=i(8203),r=i(5155),s=i.n(r),o=i(7292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let d=["",{children:["founder",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,8659)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\founder\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,927)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\layout.jsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,1485,23)),"next/dist/client/components/unauthorized-error"]}],m=["C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\app\\founder\\page.jsx"],p={require:i,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/founder/page",pathname:"/founder",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2461:(e,t,i)=>{Promise.resolve().then(i.bind(i,9637))},6429:(e,t,i)=>{Promise.resolve().then(i.bind(i,1502))},1502:(e,t,i)=>{"use strict";i.d(t,{default:()=>r});var a=i(5512),n=i(8009);let r=()=>{let[e,t]=(0,n.useState)(!1),i=()=>{t(window.innerWidth<=700)};return(0,n.useEffect)(()=>(i(),window.addEventListener("resize",i),()=>window.removeEventListener("resize",i)),[]),(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)("div",{className:"h-12 w-full block md:hidden bg-[#5f6f7f]"}),(0,a.jsx)("div",{className:"relative flex bg-founderBG items-end justify-center min-h-[110vh] mobile:min-h-[100vh] bg-cover bg-top",children:(0,a.jsxs)("div",{className:"relative z-10 text-center mb-12 p-4 rounded-md tablet:mb-16 mobile:mb-4",children:[(0,a.jsx)("h1",{className:"font-extrabold text-white text-5xl mb-8 mobile:text-[42px] mobile:leading-[51px] mobile:font-bold mobile:mb-2",children:"Captain (Dr.) AD Manek"}),(0,a.jsxs)("p",{className:"text-lg text-white mt-2 text-[18px] leading-[23px] font-medium mobile:text-[17px] mobile:leading-[20px] mobile:font-medium",children:["A pioneer in aviation education with an inspiring journey from humble beginnings to soaring success. With over three decades of dedication, he has trained thousands of aviation professionals, shaping the future of the industry. ",(0,a.jsx)("br",{})," His passion, resilience, and vision continue to inspire aspiring aviators worldwide."]})]})}),(0,a.jsxs)("div",{className:"p-6 md:p-5 max-w-4xl mx-auto mt-7 tablet:mt-0 text-center mobile:mt-2",children:[(0,a.jsx)("p",{className:"text-textGreyPrimary text-justify leading-relaxed text-base md:text-lg text-[18px] font-semibold mobile:text-[14px] mobile:leading-[22px]",children:"Captain (Dr.) AD Manek is a living embodiment of perseverance, resilience, and vision. Born in 1961 in a small village Vyara, Gujarat, into a modest background, he began working as a landless laborer at a very young age to support his family. Despite the financial hardships and societal barriers, his dreams soared high when he first saw an airplane at the age of 12,while working on agricultural farm as a child labour sparking a deep fascination with aviation."}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-justify leading-relaxed text-base md:text-lg text-[18px] font-semibold  mobile:text-[14px] mobile:leading-[22px]",children:"Determined to make his mark in the skies, Captain Manek excelled in academics while overcoming significant challenges, including language barriers. His dedication to excellence earned him prestigious accolades such as Best NCC Cadet of Maharashtra (1983), setting the foundation for his remarkable journey. Armed with unwavering determination, he pursued his pilot training in the United States, earning his aviation licences and fulfilling his dream of becoming a professional pilot."}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-justify leading-relaxed text-base md:text-lg text-[18px] font-semibold  mobile:text-[14px] mobile:leading-[22px]",children:"In 1987, Captain (Dr.) AD Manek founded The Skyline Aviation Club, which has since trained over 5,000 aviation professionals, including pilots, dispatchers, and airline crew members. His efforts have directly contributed to elevating aviation standards in India and abroad, making The Skyline Aviation Club a beacon of aviation education."}),(0,a.jsx)("p",{className:"text-textGreyPrimary text-justify leading-relaxed text-base md:text-lg text-[18px] font-semibold  mobile:text-[14px] mobile:leading-[22px]",children:'Captain (Dr.) AD Manek\' inspiring life journey, documented in his autobiography "Udaan Ek Majdoor Bachhe Ki", serves as a motivational tale for countless aspiring aviators. His story exemplifies that hard work, determination, and an indomitable spirit can overcome even the greatest odds. Today, Captain Manek continues to mentor and inspire the next generation of aviation professionals, leaving a lasting legacy in the skies.'})]}),(0,a.jsxs)("div",{className:"mt-8 text-center tablet:mx-6 mobile:px-4 mobile:mt-4",children:[e?(0,a.jsx)("img",{src:"/moblieAward.svg",alt:"Captain Manek's Awards",className:"mx-auto"}):(0,a.jsx)("img",{src:"/Award.svg",alt:"Captain Manek's Awards",className:"mx-auto rounded-[15px] shadow-lg w-[815px] tablet:w-[700px] h-[573px] tablet:h-[492px]"}),(0,a.jsx)("p",{className:"max-w-[52rem] mx-auto mt-4 mb-16 text-textGreyPrimary mobile:mb-4 text-justify leading-relaxed font-semibold text-[18px] md:text-lg mobile:text-[14px]",children:"It took Captain (Dr.) AD Manek 16 years to achieve his goal and dream of flying. Why did it take so long? It took so long because of the many hurdles and obstacles in the way of students trying to achieve something which the society didnt want them to. Only airline pilots children were allowed to become new pilots. Captain (Dr.) AD Manek fought his way through various established procedures. For example he got the Indian government to change the policy of currency exchange for students travelling Internationally."})]})]})}},8659:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});var a=i(2740),n=i(9637);let r=()=>(0,a.jsx)(n.default,{})},9637:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Skyline-Aviation\\\\client\\\\src\\\\components\\\\FounderComponent.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\FounderComponent.jsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[756,393],()=>i(2140));module.exports=a})();