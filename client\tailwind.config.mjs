/** @type {import('tailwindcss').Config} */
const config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['"Helvetica Neue"', "Helvetica", "Arial", "sans-serif"],
      },
      colors: {
        whiteOne: "var(--whiteOne)",
        textBluePrimary: "var(--textBluePrimary)",
        neutralGrayText: "var(--neutralGrayText)",
        customGrey100: "var(--customGrey100)",
        customBlack: "var(--customBlack)",
        textGreyPrimary: "var(--textGreyPrimary)",
        descLightBlack: "var(--descLightBlack)",
        whyChoosePara: "var(--whyChoosePara)",
        fullStarBG: "var(--fullStarBG)",
        halfStarBG: "var(--halfStarBG)",
        emptyStarBG: "var(--emptyStarBG)",
        arrowIconColor: "var(--arrowIconColor)",
        formTextColor: "var(--formTextColor)",
        formTextOptionColor: "var(--formTextOptionColor)",
        footerBG: "var(--footerBG)",
        formBG: "var(--formBG)",
        black: "var(--black)",
        formPara: "var(--formPara)",
        formInputColor: "var(--formInputColor)",
        carouselDotsBG: "var(--carouselDotsBG)",
        accordionBG: "var(--accordionBG)",
        footerBottomText: "var(--footerBottomText)",
        formLabelSubColor: "var(--formLabelSubColor)",
        footerPrimaryText: "var(--footerPrimaryText)",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          1: "hsl(var(--chart-1))",
          2: "hsl(var(--chart-2))",
          3: "hsl(var(--chart-3))",
          4: "hsl(var(--chart-4))",
          5: "hsl(var(--chart-5))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      backgroundImage: {
        // landingBanner: "url(/assets/bannerImage.webp)",
        landingBanner: "url(/homeBannerImage.webp)",
        // coursesBanner: "url(/assets/coursesBanner.png)",
        coursesBanner: "url(/courceBg.jpg)",
        coursesDetailBanner: "url(/assets/courseDetailBanner.png)",
        spiralImage: "url(/assets/spiralImage.png)",
        alumniCurve: "url(/assets/curveImage.png)",
        // aboutusBG: "url('/assets/aboutusbg.svg')",
        aboutusBG: "url('/About_Us.jpg')",
        // founderBG: "url('/assets/founder.svg')",
        founderBG: "url('/founderBg.jpg')",
        // enquireBG: "url('/assets/Enquirebg.svg')",
        enquireBG: "url('/cptManket_modeji.jpg')",
        eventBanner: "url(/eventBg.jpg)",
        galleryBanner: "url(/AmitjiBanner.jpg)",
        // enquireBannerTwo: "url(/assets/enquireBannerTwo.png)",
        enquireBannerTwo: "url(/cptManket_modeji.jpg)",
        dotsImage: "url(/assets/dots.png)",
        eventsCardImage: "url(/assets/eventsCardImage.png)",
        buttonBGPrimary: "linear-gradient(90deg, #0E1795 0%, #1420CD 100%)",
        navbarBGPrimary:
          "linear-gradient(90.5deg, rgba(252, 252, 244, 0.56) 3.53%, rgba(252, 252, 244, 0.32) 104.93%);",
        eventCardBG:
          "linear-gradient(239.49deg, rgba(0, 0, 0, 0) 37.77%, rgba(0, 0, 0, 0.5) 52.4%);",
      },
      boxShadow: {
        courseCardShadow: "0px 10px 37px 0px #00000014",
        dropdownShadow: "0px 4px 20px 0px #00000040",
        cardsBoxShadow: "0px 5.46px 19.11px 0px #00000014",
        cardButtonShadow: "0px 8px 30px 0px #00000014",
      },
      screens: {
        desktop: { min: "1024px", max: "1200px" },
        tablet: { min: "768px", max: "1000px" },
        mobile: { max: "600px", min: "320px" },
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};

export default config;
