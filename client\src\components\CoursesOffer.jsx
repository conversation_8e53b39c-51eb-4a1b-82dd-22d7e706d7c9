import { ArrowRight } from "lucide-react";
import Link from "next/link";
import CourseCarousel from "./CourseCarousel";

const CoursesOffer = () => {
  return (
    <div className=" max-w-7xl mx-auto px-4 py-8 md:p-8">
      <h3 className="font-bold text-xl md:text-2xl lg:text-3xl text-textBluePrimary text-center mb-1 md:mb-2">
        Courses we offer
      </h3>
      <h4 className="font-light text-customBlack text-center mb-4 text-base md:text-lg md:font-medium lg:text-3xl lg:font-light">
        Courses Designed to Help You Soar <br />
        Take Off with The Skyline Aviation Club !
      </h4>
      <CourseCarousel />
      <div className="justify-center flex">
        <Link
          href="/course"
          className="mt-6 md:mt-12 flex items-center gap-2 bg-buttonBGPrimary text-white text-sm lg:text-base p-2 md:py-3 pl-4 md:pr-3 w-[190px] lg:w-[220px] justify-between font-bold rounded-full border border-transparent"
        >
          View All Courses
          <ArrowRight className="w-6 h-6 md:w-9 md:h-9 p-1 bg-white rounded-full text-arrowIconColor" />
        </Link>
      </div>
    </div>
  );
};

export default CoursesOffer;
