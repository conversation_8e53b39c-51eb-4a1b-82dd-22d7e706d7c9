import { ArrowRight } from "lucide-react";
import Link from "next/link";
import CourseCarousel from "./CourseCarousel";

const CoursesOffer = () => {
  return (
    <div className=" max-w-7xl mx-auto px-4 py-8 md:p-8">
      <h3 className="font-bold text-xl md:text-2xl lg:text-3xl text-textBluePrimary text-center mb-1 md:mb-2">
        Courses we offer
      </h3>
      <h4 className="font-light text-customBlack text-center mb-4 text-base md:text-lg md:font-medium lg:text-3xl lg:font-light">
        Courses Designed to Help You Soar <br />
        Take Off with The Skyline Aviation Club !
      </h4>
      <CourseCarousel />
      <div className="justify-center flex">
        <Link
          href='/course'
          className={`flex items-center gap-2 text-white bg-buttonBGPrimary rounded-full p-3 px-5 mt-10 `}
        >
          <span className="font-bold">View Course Details</span>
          <ArrowRight className="w-6 h-6 bg-white text-arrowIconColor rounded-full p-1" />
        </Link>
      </div>
    </div>
  );
};

export default CoursesOffer;
