"use client";

import {
  albumData,
  tvImageData,
  Awards,
  VVIP,
  tabButtons,
  GovermentApprovals,
} from "../constant/galleryData";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogOverlay,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useEffect, useState, useRef, Suspense } from "react";
import Image from "next/image";
import { useSearchParams, useRouter } from "next/navigation";

const tabSlugs = [
  "captain-ad-manek",
  "celebrities",
  "events",
  "tv-and-press",
  "awards",
  "vvip-testimonials",
  "government-approvals",
];

// ✅ Reusable Section Component
const GallerySection = ({ data }) => (
  <div className="grid grid-cols-2 sm:grid-cols-[repeat(auto-fit,_minmax(200px,_1fr))] gap-4 px-0 md:px-4 py-6 justify-items-center">
    {data.map((item, index) => (
      <Dialog key={index}>
        <div className="relative w-full max-w-[250px] aspect-square bg-gray-200 cursor-pointer rounded-md overflow-hidden">
          <DialogTrigger asChild>
            <Image
              src={item.image}
              alt={item.title || `Image ${index + 1}`}
              fill
              quality={100}
              className="object-cover hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 250px"
              priority
            />
          </DialogTrigger>
          <DialogOverlay className="fixed inset-0 bg-black/90 backdrop-blur-md transition-all duration-300" />
          <DialogContent className="max-h-[90vh] overflow-y-auto p-4 rounded-lg bg-white">
            <DialogHeader>
              <DialogTitle className="mb-1 font-semibold text-lg">
                {item.title}
              </DialogTitle>
              <DialogDescription className="font-normal text-lg mb-4">
                {item.msg}
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-center items-center">
              <Image
                src={item.image}
                alt={item.title || `Image ${index + 1}`}
                width={800}
                height={600}
                quality={100}
                className="max-h-[60vh] w-auto h-auto object-contain"
              />
            </div>
          </DialogContent>
        </div>
      </Dialog>
    ))}
  </div>
);

// ✅ Separate component for search params logic
const GalleryWithSearchParams = () => {
  const [activeTab, setActiveTab] = useState(1);
  const [hasScrolledFromURL, setHasScrolledFromURL] = useState(false);
  const searchParams = useSearchParams();
  const galleryRef = useRef(null);
  const router = useRouter();

  useEffect(() => {
    const slug = searchParams.get("activeTab");
    if (slug) {
      const index = tabSlugs.indexOf(slug);
      if (index !== -1) {
        setActiveTab(index + 1);
        
        // Only scroll if this is from URL redirect (not from tab click)
        // and we haven't already scrolled from URL in this session
        if (!hasScrolledFromURL && galleryRef.current) {
          galleryRef.current.scrollIntoView({ behavior: "smooth" });
          setHasScrolledFromURL(true);
        }
      }
    }
  }, [searchParams, hasScrolledFromURL]);

  return (
    <div
      className="p-6 lg:py-8 lg:px-[3rem] lg:relative"
      ref={galleryRef}
    >
      <div className="items-baseline gap-2 mb-8 flex-col xl:flex-row">
        <div className="hidden lg:block absolute bg-dotsImage top-40 left-0 bg-center bg-no-repeat w-10 h-36" />

        <div className="py-2 md:py-4 xl:flex xl:items-center w-full">
          <h3 className="text-textBluePrimary text-xl font-bold lg:text-3xl mr-4">
            Categories : Album
          </h3>
        </div>
        {/* ✅ Card-style Category Tabs */}
        <div className="flex flex-col gap-4 w-full ">
          <div className="flex flex-wrap gap-3 justify-start">
            {tabButtons.map((tab, index) => (
              <button
                key={index}
                className={`px-2 py-1 text-sm rounded-3xl border font-semibold shadow-sm transition-all
                  ${
                    activeTab === index + 1
                      ? "bg-buttonBGPrimary text-white border-transparent"
                      : "bg-white text-textBluePrimary border-textBluePrimary hover:bg-buttonBGPrimary hover:text-white"
                  }`}
                onClick={() => {
                  const newTabIndex = index + 1;
                  setActiveTab(newTabIndex);
                  // Update URL without triggering scroll
                  router.push(
                    `/gallery?activeTab=${tabSlugs[newTabIndex - 1]}`,
                    { scroll: false }
                  );
                }}
              >
                {tab}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* ✅ Gallery Sections */}
      {activeTab === 1 && (
        <GallerySection
          data={albumData.filter((item) => item.subTab === 1)}
        />
      )}
      {activeTab === 2 && (
        <GallerySection
          data={albumData.filter((item) => item.subTab === 2)}
        />
      )}
      {activeTab === 3 && (
        <GallerySection
          data={albumData.filter((item) => item.subTab === 3)}
        />
      )}
      {activeTab === 4 && <GallerySection data={tvImageData} />}
      {activeTab === 5 && <GallerySection data={Awards} />}
      {activeTab === 6 && <GallerySection data={VVIP} />}
      {activeTab === 7 && <GallerySection data={GovermentApprovals} />}
    </div>
  );
};

// ✅ Loading fallback component
const GalleryLoading = () => (
  <div className="p-6 lg:py-8 lg:px-[3rem]">
    <div className="animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
      <div className="flex flex-wrap gap-3 mb-8">
        {Array.from({ length: 7 }).map((_, i) => (
          <div key={i} className="h-8 bg-gray-200 rounded-3xl w-24"></div>
        ))}
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-[repeat(auto-fit,_minmax(200px,_1fr))] gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="aspect-square bg-gray-200 rounded-md animate-pulse"></div>
        ))}
      </div>
    </div>
  </div>
);

const GalleryComponent = () => {
  return (
    <>
      {/* Banner */}
      <div className="relative bg-galleryBanner bg-cover bg-center h-[700px]">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]" />
        <div className="absolute left-0 right-0 bottom-12 lg:bottom-24 lg:w-4/5 lg:mx-auto mx-4 text-center">
          <h1 className="font-extrabold text-whiteOne text-5xl lg:text-6xl mb-4">
            Gallery
          </h1>
          <p className="text-whiteOne font-bold text-sm md:text-xl">
            Explore The Skyline Aviation Club&apos;s journey through our
            Gallery, featuring training moments, VVIP testimonials, and award
            recognitions. Each album showcases the achievements, milestones, and
            trust we&apos;ve built in the aviation industry.
          </p>
        </div>
      </div>

      {/* Tabs & Filters wrapped in Suspense */}
      <Suspense fallback={<GalleryLoading />}>
        <GalleryWithSearchParams />
      </Suspense>
    </>
  );
};

export default GalleryComponent;