{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/navbarLinks.jsx"], "sourcesContent": ["const mainLinks = [\r\n    {\r\n        text: \"Home\",\r\n        link: \"/\",\r\n    },\r\n    {\r\n        text: \"Gallery\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"Courses\",\r\n        link: \"/course\",\r\n    },\r\n    {\r\n        text: \"Events\",\r\n        link: \"/events\",\r\n    },\r\n    {\r\n        text: \"About Us\",\r\n        link: \"/aboutus\",\r\n    },\r\n    {\r\n        text: \"Contact Us\",\r\n        link: \"/enquire\",\r\n    },\r\n]\r\n\r\nconst galleryLInks = [\r\n    {\r\n        text: \"Albums\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"TV and Press\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"Awards\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"VVIP Testimonials\",\r\n        link: \"/gallery\",\r\n    },\r\n    {\r\n        text: \"Government Approvals\",\r\n        link: \"/gallery\",\r\n    },\r\n]\r\n\r\nconst coursesLinks = [\r\n    {\r\n        text:\"Indian Commercial Pilot\",\r\n        link: `indian-commercial-pilot-licence-course`,\r\n    },\r\n    {\r\n        text:\"American Commercial Pilot\",\r\n        link: `american-commercial-pilots-licence`,\r\n    },\r\n    {\r\n        text:\"CPL Package Program\",\r\n        link: `commercial-pilot-licence-package-program`,\r\n    },\r\n    {\r\n        text:\"Foreign CPL Conversion Course\",\r\n        link: `foreign-commercial-pilot-licence-conversion-course`,\r\n    },\r\n    {\r\n        text:\"Helicopter Pilot Training\",\r\n        link: `helicopter-commercial-pilot-licence-course`,\r\n    },\r\n    {\r\n        text:\"American Flight Dispatcher\",\r\n        link: `aircraft-flight-dispatcher-licence-course`,\r\n    },\r\n    {\r\n        text:\"Radio Telephony Licence\",\r\n        link: `radio-telephony-r-aeromobile-frtol-licence`,\r\n    },\r\n    {\r\n        text:\"Airhostess/Flight Purser\",\r\n        link: `airhostess-flight-purser-training-course`,\r\n    },\r\n    {\r\n        text:\"Airport Ground Staff\",\r\n        link: `airport-ground-staff-course`,\r\n    },\r\n    {\r\n        text:\"Aviation Foundation Course\",\r\n        link: `aviation-foundation-course`,\r\n    },\r\n    {\r\n        text:\"Aeroplane/Helicopter Training Workshop\",\r\n        link: `two-day-aeroplane-or-helicopter-training-workshop`,\r\n    },\r\n]\r\n\r\nexport { mainLinks, galleryLInks, coursesLinks };"], "names": [], "mappings": ";;;;;AAAA,MAAM,YAAY;IACd;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;CACH;AAED,MAAM,eAAe;IACjB;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;IACA;QACI,MAAM;QACN,MAAM;IACV;CACH;AAED,MAAM,eAAe;IACjB;QACI,MAAK;QACL,MAAM,CAAC,sCAAsC,CAAC;IAClD;IACA;QACI,MAAK;QACL,MAAM,CAAC,kCAAkC,CAAC;IAC9C;IACA;QACI,MAAK;QACL,MAAM,CAAC,wCAAwC,CAAC;IACpD;IACA;QACI,MAAK;QACL,MAAM,CAAC,kDAAkD,CAAC;IAC9D;IACA;QACI,MAAK;QACL,MAAM,CAAC,0CAA0C,CAAC;IACtD;IACA;QACI,MAAK;QACL,MAAM,CAAC,yCAAyC,CAAC;IACrD;IACA;QACI,MAAK;QACL,MAAM,CAAC,0CAA0C,CAAC;IACtD;IACA;QACI,MAAK;QACL,MAAM,CAAC,wCAAwC,CAAC;IACpD;IACA;QACI,MAAK;QACL,MAAM,CAAC,2BAA2B,CAAC;IACvC;IACA;QACI,MAAK;QACL,MAAM,CAAC,0BAA0B,CAAC;IACtC;IACA;QACI,MAAK;QACL,MAAM,CAAC,iDAAiD,CAAC;IAC7D;CACH"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/courseDetailData.jsx"], "sourcesContent": ["// const eligibilityData = [\r\n//   {\r\n//     image: \"/assets/ageCriteriaImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Age Criteria\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         Minimum age - 17 Years <br />\r\n//         Maximum age - 35 Years\r\n//       </p>\r\n//     ),\r\n//   },\r\n//   {\r\n//     image: \"/assets/educationImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Education\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         10+2 with Maths and Physics <br /> or{\" \"}\r\n//         <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n//       </p>\r\n//     ),\r\n//   },\r\n//   {\r\n//     image: \"/assets/personalityImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Personality\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         Be able to read, write, speak and understand the English language\r\n//       </p>\r\n//     ),\r\n//   },\r\n//   {\r\n//     image: \"/assets/medicalImage.png\",\r\n//     class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n//     title: \"Medical\",\r\n//     desc: (\r\n//       <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n//         DGCA Medical Class 2 Certificate (CA35) (Note: We Will arrange Medical\r\n//         Examination with our panel doctor who is DGCA approved Class II Medical\r\n//         Examiner)\r\n//       </p>\r\n//     ),\r\n//   },\r\n// ];\r\n\r\n// const jobAssistanceData = [\r\n//   {\r\n//     title: \"Comprehensive Career Grooming\",\r\n//     desc: \"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement\",\r\n//   },\r\n//   {\r\n//     title: \"Global Job Opportunities\",\r\n//     desc: \" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry.\",\r\n//   },\r\n//   {\r\n//     title: \"Proven Success\",\r\n//     desc: \"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !\",\r\n//   },\r\n// ];\r\n\r\nconst courseData = [\r\n  {\r\n    id: 1,\r\n    slug: \"indian-commercial-pilot-licence-course\",\r\n    courseName: \"DGCA\",\r\n    courseDesc: \"Indian Commercial Pilot Licence Course\",\r\n    introduction: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Exciting and Dynamic Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Prestige and Respect\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Travel the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"DGCA Commercial Pilot Licence Course – Airplane\",\r\n        courseDuration: \"2 years\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Course\",\r\n            duration: \"Theory Course - 4 Months (Full Time)\",\r\n          },\r\n          {\r\n            title:\r\n              \"Part II - Practical Flight Training of 200 Flight hours (185 Hours on Single Engine Land + 15 Hours on Multi Engine Lnad)\",\r\n            duration:\r\n              \"1 Year & 8 Months at our Affiliated Flight Training Organization (FTO)\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    subCourseStructure: [\r\n      {\r\n        title: \"DGCA Commercial Pilot Licence Written Exams\",\r\n        subjects: [\r\n          \"Air Navigation\",\r\n          \"Aviation Meteorology\",\r\n          \"Air Regulations\",\r\n          \"Aircraft & Engine (Technical) General & Specific\",\r\n        ],\r\n      },\r\n      {\r\n        title: \"WPC, Ministry of Telecommunication Exam\",\r\n        licence:\r\n          \"Radio Telephony (Restricted) Licence / Flight Radio Telephone Licence\",\r\n        parts: [\r\n          \"Part I: Radio Communication Procedures\",\r\n          \"Part II: Radio Theory\",\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 17 Years <br />\r\n            Maximum age - 35 Years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35)\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Comprehensive Career Grooming\",\r\n        desc: \"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement\",\r\n      },\r\n      {\r\n        title: \"Global Job Opportunities\",\r\n        desc: \" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry.\",\r\n      },\r\n      {\r\n        title: \"Proven Success\",\r\n        desc: \"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 2,\r\n    slug: \"american-commercial-pilots-licence\",\r\n    courseName: \"FAA USA\",\r\n    courseDesc: \"American Commercial Pilot's Licence\",\r\n    introduction: \"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Exciting and Dynamic Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Prestige and Respect\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Travel the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"American FAA Commercial Pilot Licence Course - Airplane\",\r\n        courseDuration: \"1 year\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Course (in India)\",\r\n            duration: \"Theory Course - 3 Months (inclusive of PPC, IRC, CPC)\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training (in USA)\",\r\n            duration:\r\n              \"Practical Flight Training - 9 Months (inclusive of 254 hrs with 25 hrs MEL) at our affiliated flight school Avel Flight School, Illinois, Chicago, USA\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 17 Years <br />\r\n            Maximum age - 35 Years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35)\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Career Grooming & Job Assistance\",\r\n        desc: \"Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad.\",\r\n      },\r\n      {\r\n        title: \"Proven Placement Record\",\r\n        desc: \"So far, we have helped 398 students secure pilot jobs in various airlines worldwide.\",\r\n      },\r\n    ],\r\n\r\n    shortNote: \"Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad. So far we have helped 398 students to get Pilot Jobs in various airlines\",\r\n    visaAsistant: \"After completion of theory course, students have the option to choose a practical training location in India or United Stated of America. Students interested in going to USA will be assisted in Admission, I-20, DS-160, SEVIS and M1/F1 US Student Visa. So far we have helped 506 students procuring various students visas\",\r\n  },\r\n  {\r\n    id: 3,\r\n    slug: \"aircraft-flight-dispatcher-licence-course\",\r\n    courseName: \"FAA USA\",\r\n    courseDesc: \"Aircraft/Flight Dispatcher Licence Course\",\r\n    introduction: \"An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline. We Offer FAA(USA) Aircraft Dispatcher Licence Courses which will prepare you for entry into all Airlines worldwide.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn 1.5 Lacs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Challenging and Demanding Career\",\r\n        description:\r\n          \"This career demands a high level of precision, focus, and resilience under pressure, making it both challenging and highly rewarding for those passionate about aviation.\",\r\n      },\r\n      {\r\n        title: \"Hot Seat Job\",\r\n        description:\r\n          \"The term perfectly describes the role of an Aircraft Dispatcher because of the intense pressure, accountability, and critical decision-making required in real time\",\r\n      },\r\n      {\r\n        title: \"High Responsibities maintaining Multiple Aircrafts\",\r\n        description:\r\n          \"An Aircraft Dispatcher carries high responsibilities in maintaining multiple aircraft operations, including Simultaneous Flight Oversight, Real-Time Monitoring, Safety Assurance, Collaboration Across Teams. Prioritization Under Pressure\",\r\n      },\r\n      {\r\n        title: \"Future Progress Quickly\",\r\n        description:\r\n          \"The Aircraft Dispatcher role offers opportunities for quick future progress due to: High Demand in Aviation, Career Advancement, Transferable Skills, Licensing and Certification, Dynamic Work Environment.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"FAA (USA) Aircraft/Flight Dispatcher Licence Course\",\r\n        courseDuration: \"6 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Course (in India)\",\r\n            duration:\r\n              \"Theory Course - 4 Months (inclusive of CPL, RTR, IRC And ICAO Aviation English Course)\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training (in USA)\",\r\n            duration:\r\n              \"Practical Flight Training - 2 Months (inclusive of 200 hrs of Classroom Preparation )\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Training Eligibility: 21 years <br />\r\n            Licence Issuance: 23 years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-16 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14\",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Career Grooming & Job Assistance\",\r\n        desc: \"Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad.\",\r\n      },\r\n      {\r\n        title: \"Proven Placement Record\",\r\n        desc: \"So far, we have helped 398 students secure pilot jobs in various airlines worldwide.\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 4,\r\n    slug: \"radio-telephony-r-aeromobile-frtol-licence\",\r\n    courseName: \"DGCA/WPC\",\r\n    courseDesc: \"Radio Telephony (R) Aeromobile/FRTOL Licence\",\r\n    introduction: \"This professional course is structured in line with international radio regulations governing the aeronautical mobile service. It meets global standards, and candidates who achieve the required level of performance are awarded a government-recognized licence to operate.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Start your aviation journey with a competitive salary of ₹25,000 as an FRTOL holder, paving the way for a rewarding career.\",\r\n      },\r\n      {\r\n        title: \"Career Opportunities as an FRTOL\",\r\n        description:\r\n          \"Student completing this course and on passing examination and acquiring RT (R) Licence issued by wireless Planning & Co-Ordination Wing, Ministry of Communication, Government of India, can seek employment in the various Indian as well as Foreign Airlines, Airport authorities, Aviation Companies, Oil rigs etc. as Aeronautical Radio Communication Officer\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"FRTOL\",\r\n        courseDuration: \"2 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Radio Telephony and Aviation Phonetics\",\r\n            duration:\r\n              \"\",\r\n          },\r\n          {\r\n            title: \"Part II - Technical Knowledge\",\r\n            duration:\r\n              \"\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    theoryTrainingData: [],\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum Age: 18 years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            12th Pass/Appeared (Any Stream) <br /> or{\" \"}\r\n            any <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Job Placement for Radio Operators\",\r\n        desc: \"Successful students holding requisite aviation qualifications, including a valid RTR (Radio Telephony Restricted) licence issued by the Government of India, will be groomed and assisted for job placement as Radio Officers on oil rigs and in aviation companies abroad.\",\r\n      },\r\n      {\r\n        title: \"Proven Track Record\",\r\n        desc: \"So far, we have successfully helped 21 students secure Radio Operator jobs in various airlines and aviation organizations worldwide.\",\r\n      },\r\n    ],\r\n\r\n  },\r\n  {\r\n    id: 5,\r\n    slug: \"commercial-pilot-licence-package-program\",\r\n    courseName: \"Inclusive of Indian CPL + American CPL or Canadian CPL\",\r\n    courseDesc: \"Commercial Pilot Licence Package Program\",\r\n    introduction: \"The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"All-in-One Aviation Training\",\r\n        description:\r\n          \"Students receive all required aviation training under one roof, streamlining the learning process and eliminating the need for multiple institutions.\",\r\n      },\r\n      {\r\n        title: \"Economical Package Deal\",\r\n        description:\r\n          \"The bundled course structure is cost-effective for parents, offering maximum value across various aviation domains.\",\r\n      },\r\n      {\r\n        title: \"Versatile Career Preparation\",\r\n        description:\r\n          \"This program prepares students for multiple aviation roles, including Commercial Pilot, Flight Dispatcher, and Radio Telephony Officer, increasing job flexibility.\",\r\n      },\r\n      {\r\n        title: \"In-depth & Holistic Training\",\r\n        description:\r\n          \"The course delivers a comprehensive understanding of aviation, ensuring students are ready for the challenges of the aviation industry.\",\r\n      },\r\n      {\r\n        title: \"Recognized Certification & Logbook Endorsement\",\r\n        description:\r\n          \"Upon successful completion, students receive a course completion certificate and official logbook endorsement, boosting credibility for airline recruitment.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Complete Aviation Training Package – CPL, RTR, ATPL, Dispatcher & ELP\",\r\n        courseDuration: \"1 Year\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Training (in India)\",\r\n            duration:\r\n              \"Theory Course – 3 Months (Full-time in Mumbai, covering Commercial Pilot Training, RTR, Aviation English, ATPL/Dispatcher, and ELP)\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training (in USA)\",\r\n            duration:\r\n              \"Practical Training – 9 Months (Flight Training at our partnered facility in the USA)\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    subCourseStructure: [\r\n      {\r\n        title: \"FAA Subject Coverage\",\r\n        subjects: [\r\n          \"Air Regulation\",\r\n          \"Aviation Meteorology\",\r\n          \"Air Navigation\",\r\n          \"Audio Visual Training\",\r\n          \"Aircraft and Engine\",\r\n          \"Radio Communication\",\r\n          \"Aviation English\",\r\n          \"English Language Proficiency (ELP)\",\r\n        ],\r\n      },\r\n      {\r\n        title: \"FAA Training Levels\",\r\n        subjects: [\r\n          \"Student Pilot\",\r\n          \"Private Pilot\",\r\n          \"Instrument Rating\",\r\n          \"Commercial Pilot\",\r\n          \"RTR Licence\",\r\n          \"Aviation English\",\r\n          \"Airline Transport Pilot\",\r\n          \"Aircraft Dispatcher\",\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 17 Years <br />\r\n            Maximum age - 35 Years\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35)\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 6,\r\n    slug: \"airhostess-flight-purser-training-course\",\r\n    courseName: \"Cabin Crew Training Program\",\r\n    courseDesc: \"Airhostess/Flight Purser Training Course\",\r\n    introduction: \"Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines.\",\r\n      },\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines.\",\r\n      },\r\n      {\r\n        title: \"High Flying Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Respect among Peers\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Fly all over the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"25% Scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n      // {\r\n      //   title: \"Job Placement Assistance\",\r\n      //   description:\r\n      //     \"Successful Students holding requisite aviation qualification i.e Diploma in In-Flight Management for Cabin Crew received from The Skyline Aviaiton Club will be groomed and assisted for Airlilne Job Interviews and Written Tests for Domestic airlines in India and International Airlines Abroad\",\r\n      // },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Cabin Crew Course\",\r\n        courseDuration: \"4 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Swimming\",\r\n            description: \"Essential life-saving skill for emergencies.\",\r\n          },\r\n          {\r\n            title: \"Yoga\",\r\n            description: \"Enhances flexibility and mental calmness.\",\r\n          },\r\n          {\r\n            title: \"Meditation\",\r\n            description: \"Improves focus and stress management.\",\r\n          },\r\n          {\r\n            title: \"Aviation Security\",\r\n            description: \"Trains in airport and aircraft security measures.\",\r\n          },\r\n          {\r\n            title: \"Reading Body Language\",\r\n            description:\r\n              \"Interpreting passenger behavior for better service and safety.\",\r\n          },\r\n          {\r\n            title: \"In-Flight Services\",\r\n            description: \"Handling passenger needs and service during flights.\",\r\n          },\r\n          {\r\n            title: \"In-Flight Food\",\r\n            description: \"Understanding catering and food management.\",\r\n          },\r\n          {\r\n            title: \"In-Flight Entertainment\",\r\n            description: \"Providing and managing onboard entertainment.\",\r\n          },\r\n          {\r\n            title: \"Communication Skills\",\r\n            description: \"Effective interaction with passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Aircraft Technical Knowledge\",\r\n            description:\r\n              \"Basic understanding of aircraft mechanics and systems.\",\r\n          },\r\n          {\r\n            title: \"Emergency Equipment on Board the Aircraft\",\r\n            description: \"Knowledge of emergency tools and their use.\",\r\n          },\r\n          {\r\n            title: \"The Brace Position\",\r\n            description:\r\n              \"Training on proper brace positions during emergencies.\",\r\n          },\r\n          {\r\n            title: \"Aircraft Evacuation\",\r\n            description: \"Procedures for safely evacuating an aircraft.\",\r\n          },\r\n          {\r\n            title: \"Survival (Sea, Jungle, Desert)\",\r\n            description: \"Techniques for survival in various terrains.\",\r\n          },\r\n          {\r\n            title: \"Security Measures to Check Hijacking and Bomb\",\r\n            description: \"Protocols to manage security threats.\",\r\n          },\r\n          {\r\n            title: \"Beauty and Perfect Airhostess or Flight Purser\",\r\n            description: \"Training on grooming and professionalism.\",\r\n          },\r\n          {\r\n            title: \"First Aid\",\r\n            description: \"Basic medical assistance for passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Etiquette and Manners (Finishing School)\",\r\n            description:\r\n              \"Refinement of interpersonal and professional behavior.\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    // subCourseStructure: [\r\n    //   {\r\n    //     title: \"Course Syllabus\",\r\n    //     subjects: [\r\n    //       \"Basic Aeronautical Knowledge\",\r\n    //       \"Aviation Physiology\",\r\n    //       \"Ophthalmology\",\r\n    //       \"Otorhinolaryngology\",\r\n    //       \"Cardiovascular System\",\r\n    //       \"Gynaecology & Obstetrics\",\r\n    //       \"Psychiatry in Aviation\",\r\n    //       \"Legislation, Rules and Regulations\",\r\n    //       \"Air Ambulance Operations\",\r\n    //       \"Medical Tourism\",\r\n    //     ],\r\n    //   },\r\n    // ],\r\n\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age & Height Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Age: 18–27 Years <br />\r\n            Girls: Min 5'2\" (Air Hostess)<br />\r\n            Boys: Min 5'7\" (Flight Purser)\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Educational Qualification\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 (Any Stream) – For Domestic<br />\r\n            Graduate (Any Stream) – For International\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Language Skills\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Must be able to read, speak, write, and understand English & Hindi{\"\\n\"}\r\n            Foreign Language: Added Advantage (For International Airlines)\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical Fitness\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center mt-2\">\r\n            Candidate must be medically fit as per airline standards.\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Job Placement Assistance for Cabin Crew\",\r\n        desc: \"Successful students holding the Diploma in In-Flight Management for Cabin Crew awarded by The Skyline Aviation Club will be groomed and assisted for airline job interviews and written tests. This includes placement support for both domestic airlines in India and international airlines abroad.\",\r\n      },\r\n    ],\r\n\r\n  },\r\n  {\r\n    id: 7,\r\n    slug: \"airport-ground-staff-course\",\r\n    courseName: \"Airport Ground Services\",\r\n    courseDesc: \"Airport Ground Staff Course\",\r\n    introduction: \"If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn up to ₹20,000 per month with domestic airlines and up to ₹50,000 per month with international airlines.\",\r\n      },\r\n      {\r\n        title: \"High Flying Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Respect among Peers\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Fly all over the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"25% Scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Job Placement Assistance\",\r\n        description:\r\n          \"Successful Students holding requisite aviation qualification i.e Diploma in In-Flight Management for Cabin Crew received from The Skyline Aviaiton Club will be groomed and assisted for Airlilne Job Interviews and Written Tests for Domestic airlines in India and International Airlines Abroad\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Airport Ground Staff\",\r\n        courseDuration: \"3 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Yoga\",\r\n            description: \"Enhances flexibility and mental calmness.\",\r\n          },\r\n          {\r\n            title: \"Meditation\",\r\n            description: \"Improves focus and stress management.\",\r\n          },\r\n          {\r\n            title:\r\n              \"Aviation Security, Geography with respect to all airports in india. Airline and airport codes.\",\r\n            description: \"Trains in airport and aircraft security measures.\",\r\n          },\r\n          {\r\n            title: \"Reading Body Language\",\r\n            description:\r\n              \"Interpreting passenger behavior for better service and safety.\",\r\n          },\r\n          {\r\n            title: \"Communication Skills\",\r\n            description: \"Effective interaction with passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Aircraft Technical Knowledge\",\r\n            description:\r\n              \"Basic understanding of aircraft mechanics and systems.\",\r\n          },\r\n          {\r\n            title: \"Emergency Equipment on Board the Aircraft\",\r\n            description: \"Knowledge of emergency tools and their use.\",\r\n          },\r\n          {\r\n            title: \"Security Measures to Check Hijacking and Bomb\",\r\n            description: \"Protocols to manage security threats.\",\r\n          },\r\n          {\r\n            title: \"Beauty and Perfect Airhostess or Flight Purser\",\r\n            description: \"Training on grooming and professionalism.\",\r\n          },\r\n          {\r\n            title:\r\n              \"First Aid Airport Announcements, Passenger handling, issue of boarding pass.\",\r\n            description: \"Basic medical assistance for passengers and crew.\",\r\n          },\r\n          {\r\n            title: \"Etiquette and Manners (Finishing School)\",\r\n            description:\r\n              \"Refinement of interpersonal and professional behavior.\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age & Height Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum Age: 18 Years{\"\\n\"}\r\n            Maximum Age: 27 Years{\"\\n\"}\r\n            Girls: Min 5'2\" (Air Hostess){\"\\n\"}\r\n            Boys: Min 5'7\" (Flight Purser)\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Educational Qualification\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 (Any Stream) – Domestic Airlines{\"\\n\"}\r\n            Graduate (Any Stream) – International Airlines\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Language Proficiency\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Must be able to read, write, speak & understand{\"\\n\"}\r\n            English and Hindi{\"\\n\"}\r\n            Foreign Language – Added Advantage for Graduates\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical & Other Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Medically Fit{\"\\n\"}\r\n            Must understand liability for surface or collision-related damages\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 8,\r\n    slug: \"aviation-foundation-course\",\r\n    courseName: \"SUMMER VACATION TRAINING PROGRAM\",\r\n    courseDesc: \"Aviation Foundation Course\",\r\n    introduction: \"Aviation Foundation Course is specially designed for school and college students to create foundation for pursuing airline careers. Students will be guided towards various airline opportunities available i.e. Airline Pilot, Aircraft/Flight Dispatcher, Air hostess, Flight Purser, Ground Hostess, Traffic Assistant, Aeronautical Engineer, Aircraft Maintenance Engineer, Air Traffic Control Officer, Radio Officer.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Benefits\",\r\n        description:\r\n          \"Young students have big dreams which change every day. This is a chance to get your child focused on what he or she loves. Students need not wait till 12th to decide a career path. One-stop complete aviation solution from zero to airline pilot. Early exposure to the aviation sector makes them much better prepared than other candidates.\"\r\n      },\r\n      {\r\n        title: \"Rewards\",\r\n        description:\r\n          \"Students will be issued a certificate of achievement on the completion of the course. Group photograph. Individual photograph with instructor and helicopter or aeroplane. Memento as Future Pilot. Airline Career Guidance Booklet.\"\r\n      }\r\n    ],\r\n    theoryTrainingData: [],\r\n    courseStructure: [\r\n      {\r\n        title: \"Student & Private Pilot Licence Training\",\r\n        courseDuration: \"Flexible (Introductory Program)\",\r\n        parts: [\r\n          {\r\n            title: \"Part I – Theory Training (DGCA Syllabus)\",\r\n            duration: \"Covers core subjects as per DGCA (Govt. of India) guidelines\",\r\n          },\r\n          {\r\n            title: \"Subjects Covered\",\r\n            duration: [\r\n              \"Air Regulation\",\r\n              \"Aviation Meteorology\",\r\n              \"Air Navigation\",\r\n              \"Audio Visual Training\",\r\n              \"Aircraft and Engine\",\r\n              \"Radio Communication\"\r\n            ].join(\", \"),\r\n          },\r\n          {\r\n            title: \"Part II – Practical Orientation\",\r\n            duration:\r\n              \"Includes familiarization briefing + discovery flight in helicopter/airplane for 15 minutes\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    subCourseStructure: [\r\n      {\r\n        title: \"Admission Procedure\",\r\n        subjects: [\r\n          \"Fill out the online registration form.\",\r\n          \"Send the following documents by courier or submit personally:\",\r\n          \"– Two passport-size photographs\",\r\n          \"– Certified true copy of your school ID card\",\r\n        ],\r\n      },\r\n    ],\r\n    practicalTrainingData: [],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Who Can Apply?\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Students of Schools & Colleges{\"\\n\"}\r\n            Hobby Flyers Welcome{\"\\n\"}\r\n            Age: No Bar\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Course Duration\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            3 Weeks Duration{\"\\n\"}\r\n            3 Hours per Day\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Batch Timings\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Morning Batch: 10 AM – 1 PM{\"\\n\"}\r\n            Noon Batch: 2 PM – 5 PM\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Batch Size\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            20 Students per Batch\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 9,\r\n    slug: \"two-day-aeroplane-or-helicopter-training-workshop\",\r\n    courseName: \"Aeroplane/Helicopter Orientation Training\",\r\n    courseDesc: \"Two Day Aeroplane/Helicopter Training Workshop\",\r\n    introduction: \"Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter. They will be exposed at an early age to experience what it feels like to fly amongst the clouds. This course will open multiple career oppurtunities in the field of aviation.\",\r\n    careerProspects: [],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of ₹30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      }\r\n    ],\r\n\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of ₹25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated Banks for General Category Students.\",\r\n      },\r\n      {\r\n        title: \"Success Track Record\",\r\n        description:\r\n          \"So far, 54 students have been successful in procuring these benefits.\",\r\n      }\r\n    ]\r\n    ,\r\n    courseStructure: [\r\n      {\r\n        title: \"AEROPLANE/HELICOPTER TRAINING WORKSHOP\",\r\n        courseDuration: \"2 Day\",\r\n        parts: [\r\n          {\r\n            title: \"Theory Training\",\r\n            duration: (\r\n              <span className=\"text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n                • How helicopters fly<br />\r\n                • Parts of a helicopter and their functions<br />\r\n                • Cockpit orientation<br />\r\n                • Live ATC communication monitoring<br />\r\n                • Audio-visual guide on Principles of Flight and History of Aviation<br />\r\n                • Aviation career guidance<br />\r\n              </span>\r\n            )\r\n            // \"• How helicopters fly\\n• Parts of a helicopter and their functions\\n• Cockpit orientation\\n• Live ATC communication monitoring\\n• Audio-visual guide on Principles of Flight and History of Aviation\\n• Aviation career guidance\",\r\n          },\r\n          {\r\n            title: \"Practical Experience\",\r\n            duration:\r\n              (\r\n                <span className=\"text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n                  • 15-minute Helicopter Discovery Flight at Juhu Aerodrome<br />\r\n                  • Full-time tour director<br />\r\n                  • Travel insurance<br />\r\n                  • Breakfast & Lunch included\r\n                </span>\r\n              )\r\n          }\r\n        ]\r\n      }\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Eligibility Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age - 12 Years <br />\r\n            Minimum Class - VI onwards\r\n          </p>\r\n        )\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Dress Code\",\r\n        desc: \"School Uniform with valid School ID\",\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Batch Size\",\r\n        desc: \"Minimum 74 students (batches may be combined if minimum not met)\",\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Parental Consent\",\r\n        desc: \"Discovery Flight Consent Form signed by parent\",\r\n      }\r\n    ],\r\n\r\n  },\r\n  {\r\n    id: 10,\r\n    slug: \"foreign-commercial-pilot-licence-conversion-course\",\r\n    courseName: \"INDIAN COMMERCIAL PILOT LICENCE (DGCA)\",\r\n    courseDesc: \"Foreign Commercial Pilot Licence Conversion Course\",\r\n    introduction: \"The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Competitive Salaries\",\r\n        description:\r\n          \"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies.\",\r\n      },\r\n      {\r\n        title: \"Exciting and Dynamic Career\",\r\n        description:\r\n          \"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity.\",\r\n      },\r\n      {\r\n        title: \"Prestige and Respect\",\r\n        description:\r\n          \"Gain recognition and admiration among peers and society as a skilled and accomplished aviator.\",\r\n      },\r\n      {\r\n        title: \"Travel the World\",\r\n        description:\r\n          \"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies.\",\r\n      },\r\n    ],\r\n    // theoryTrainingData: [\r\n    //   {\r\n    //     title: \"Skyline UDAN Scholarship\",\r\n    //     description:\r\n    //       \"For economically weak students. Those students who are brilliant, capable and eligible to become Pilot but economically weak and are eligible for other Scholarship or Education Loan scheme of Government or Charitable Trusts for practical flight training course fees can be considered for grant of this scholarship.\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Skyline Women Empowerment Scholarship\",\r\n    //     description:\r\n    //       \"The Skyline Aviation Club is Corporate Member of Indian Women Pilot Association and supports IWPA and Government of India to promote “Beti Bachav, Beti Padhav” Campaign. To encourage young girls to take up aviation as career we have introduced this scholarship. This Scholarship is exclusively for Female Trainee Pilots who are members of Indian Women Pilots Association (IWPA)\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Skyline Jai Jawan Scholarship\",\r\n    //     description:\r\n    //       \"Founder of The Skyline Aviation Club Capt.AD Manek is ex NCC Cadet completed NCC training with rank of Sr. Under Officer in the year 1982 and even today he takes part in activities of NCC. To honor services of Capt.AD Manek Maharashtra State NCC Directorate have declared “Capt.AD Manek Scholarship” for Best Cadets of Maharashtra State from the year 2023. To encourage NCC Cadets and Sons /Daughters of Defence Personnels we have introduced this scholarship. This Scholarship is exclusively for NCC Cadets holding “B” or “C” Certificates and Sons, Daughters of Defence Personnels including Indian Army, Indian Navy, Indian Airforce, Coast Guard, and Border Security Force. (Note: Scholarship for Theory (Ground Training) Course will be granted on merit basis subject to score in Skyline Scholarship Examinations.)\",\r\n    //   },\r\n    // ],\r\n    // practicalTrainingData: [\r\n    //   {\r\n    //     title: \"Educational Loan from Bank (Open for all)\",\r\n    //     description:\r\n    //       \"Educational Loan of 85 % of Total Course fees can be arranged through our affiliated bank viz. Axis Bank subject to eligibility criteria of student and parents\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Scholarship from TATA Trusts (Open for all)\",\r\n    //     description:\r\n    //       \"Students completing Ground Training successfully and passes all DGCA CPL Written Examinations and complete initial flight training may be considered for partial Scholarship by TATA Trusts to encourage youth to join and make career in aviation\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Indian Women Pilot Association (IWPA) Financial Assistance (Reserved for female trainee pilots)\",\r\n    //     description:\r\n    //       \"Female Students completing Ground Training successfully and passes all DGCA CPL Written Examinations and complete initial flight training may be considered for partial Scholarship by IWPA to encourage young girls to join and make career in aviation\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Post Matric Scholarship by Department of Social Justice & Empowerment, Government of India (Reserved for students of all states of India and belonging to Scheduled Caste.)\",\r\n    //     description:\r\n    //       \"Scholarship of Rs.45 Lacs available under Post Matric Scholarship Scheme of Department of Social Justice & Empowerment, Govt. of India for Students belonging to scheduled caste.\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Commercial Pilot Licence Educational Loan by Government of Gujarat (Reserved for students of Gujarat State and belonging to Scheduled Caste or Scheduled Tribe or Other Backward Class)\",\r\n    //     description:\r\n    //       \"Educational loan of Rs.25 Lacs available under Commercial Pilot Licence loan Scheme of Department of Social Justice & Empowerment, Government of Gujarat\",\r\n    //   },\r\n    //   {\r\n    //     title: \"Videsh Abhiyash Yojna Educational Loan by Government of Gujarat (Reserved for students of Gujarat State and belonging to Scheduled Caste or Scheduled Tribe or Other Backward Class)\",\r\n    //     description:\r\n    //       \"Educational loan of Rs.15 Lacs available under Videsh Abhiyash Yojna loan Scheme of Department of Social Justice & Empowerment, Government of Gujarat. (This loan can be used for FAA Flight / Aircraft Dispatcher Course in USA)\",\r\n    //   },\r\n    // ],\r\n    theoryTrainingData: [\r\n      {\r\n        title: \"Women Empowerment Scheme\",\r\n        description:\r\n          \"50% scholarship for girls & PWFA members by The Skyline Aviation Club\",\r\n      },\r\n      {\r\n        title: \"Jai Jawan Scheme\",\r\n        description:\r\n          \"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club\",\r\n      },\r\n    ],\r\n    practicalTrainingData: [\r\n      {\r\n        title: \"Scholarship - Govt. of India\",\r\n        description:\r\n          \"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Govt. of Gujarat\",\r\n        description:\r\n          \"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students.\",\r\n      },\r\n      {\r\n        title: \"Educational Loan - Banks\",\r\n        description:\r\n          \"Up to 85% of course fees from affiliated banks for general category students.\",\r\n      },\r\n    ],\r\n    courseStructure: [\r\n      {\r\n        title: \"Foreign Pilot Licence Conversion Course (DGCA) - Theory Course\",\r\n        courseDuration: \"2 months\",\r\n        parts: [\r\n          {\r\n            title:\r\n              \"Prepares for DGCA Commercial Pilot Licence conversion Exams\",\r\n            duration: \"\",\r\n            papers: [\r\n              \"Paper 1 - Composite Paper (Air navigation & Air Meteorology)\",\r\n              \"Paper 2 - Air Regulations\",\r\n            ],\r\n          },\r\n          {\r\n            title: \"Prepare for WPC, Ministry of Telecommunication Exam\",\r\n            duration: \"\",\r\n            papers: [\r\n              \"Part I: Radio Communication Procedures\",\r\n              \"Part II: Radio Theory\",\r\n            ],\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Minimum age: Age 12+ years <br />\r\n            Minimum class: VI onwards\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            10+2 with Maths and Physics <br /> or{\" \"}\r\n            <span className=\"text-textBluePrimary\">Higher Education*</span>\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Personality\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            Be able to read, write, speak and understand the English language\r\n          </p>\r\n        ),\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: (\r\n          <p className=\"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2\">\r\n            DGCA Medical Class 2 Certificate (CA35)\r\n          </p>\r\n        ),\r\n      },\r\n    ],\r\n    shortNote: \"Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad under Airline Prep Course.  So far we have helped 720 plus students to get Pilot Jobs in various airlines\",\r\n    jobAssistanceData: [\r\n      {\r\n        title: \"Comprehensive Career Grooming\",\r\n        desc: \"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement\",\r\n      },\r\n      {\r\n        title: \"Global Job Opportunities\",\r\n        desc: \" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry.\",\r\n      },\r\n      {\r\n        title: \"Proven Success\",\r\n        desc: \"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: 11,\r\n    slug: \"helicopter-commercial-pilot-licence-course\",\r\n    courseName: \"HCPL\",\r\n    courseDesc: \"Helicopter Commercial Pilot Licence Course\",\r\n    introduction:\r\n      \"The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.\",\r\n    careerProspects: [\r\n      {\r\n        title: \"Professional Opportunities\",\r\n        description:\r\n          \"Work with private helicopter operators, tourism companies, charter services, and emergency medical services (HEMS).\",\r\n      },\r\n      {\r\n        title: \"Government and Defense Roles\",\r\n        description:\r\n          \"Opportunities in government agencies, paramilitary forces, and search & rescue operations.\",\r\n      },\r\n      {\r\n        title: \"Corporate and VIP Transport\",\r\n        description:\r\n          \"Serve as pilot for high-profile individuals, corporate executives, and political dignitaries.\",\r\n      },\r\n    ],\r\n    theoryTrainingData: [],\r\n    practicalTrainingData: [],\r\n    courseStructure: [\r\n      {\r\n        title: \"HCPL Training Program\",\r\n        courseDuration: \"12 to 18 Months\",\r\n        parts: [\r\n          {\r\n            title: \"Part I - Theory Training\",\r\n            duration:\r\n              \"Covers DGCA syllabus including Air Navigation, Aviation Meteorology, Air Regulations, Aircraft & Engines (Technical), and Radio Telephony.\",\r\n          },\r\n          {\r\n            title: \"Part II - Practical Flight Training\",\r\n            duration:\r\n              \"Minimum of 150 hours of helicopter flying at an approved FTO under instructor supervision.\",\r\n          },\r\n        ],\r\n      },\r\n    ],\r\n    eligibilityData: [\r\n      {\r\n        image: \"/assets/ageCriteriaImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Age Criteria\",\r\n        desc: \"Minimum 18 years of age at the time of licence issue\",\r\n      },\r\n      {\r\n        image: \"/assets/educationImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Education\",\r\n        desc: \"10+2 with Physics and Mathematics or equivalent (NIOS acceptable)\",\r\n      },\r\n      {\r\n        image: \"/assets/medicalImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Medical\",\r\n        desc: \"Class II Medical for training & Class I Medical for licence issuance\",\r\n      },\r\n      {\r\n        image: \"/assets/personalityImage.png\",\r\n        class: \"w-16 h-20 md:w-20 md:h-14 \",\r\n        title: \"Language Proficiency\",\r\n        desc: \"Must be able to read, write, speak and understand English\",\r\n      },\r\n    ],\r\n  }\r\n\r\n];\r\n\r\nexport { courseData };\r\n"], "names": [], "mappings": "AAAA,4BAA4B;AAC5B,MAAM;AACN,6CAA6C;AAC7C,2CAA2C;AAC3C,6BAA6B;AAC7B,cAAc;AACd,iHAAiH;AACjH,wCAAwC;AACxC,iCAAiC;AACjC,aAAa;AACb,SAAS;AACT,OAAO;AACP,MAAM;AACN,2CAA2C;AAC3C,2CAA2C;AAC3C,0BAA0B;AAC1B,cAAc;AACd,iHAAiH;AACjH,qDAAqD;AACrD,0EAA0E;AAC1E,aAAa;AACb,SAAS;AACT,OAAO;AACP,MAAM;AACN,6CAA6C;AAC7C,2CAA2C;AAC3C,4BAA4B;AAC5B,cAAc;AACd,iHAAiH;AACjH,4EAA4E;AAC5E,aAAa;AACb,SAAS;AACT,OAAO;AACP,MAAM;AACN,yCAAyC;AACzC,2CAA2C;AAC3C,wBAAwB;AACxB,cAAc;AACd,iHAAiH;AACjH,iFAAiF;AACjF,kFAAkF;AAClF,oBAAoB;AACpB,aAAa;AACb,SAAS;AACT,OAAO;AACP,KAAK;AAEL,8BAA8B;AAC9B,MAAM;AACN,8CAA8C;AAC9C,gMAAgM;AAChM,OAAO;AACP,MAAM;AACN,yCAAyC;AACzC,8LAA8L;AAC9L,OAAO;AACP,MAAM;AACN,+BAA+B;AAC/B,sJAAsJ;AACtJ,OAAO;AACP,KAAK;;;;;;AAEL,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,OACE;wBACF,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,SACE;gBACF,OAAO;oBACL;oBACA;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,8OAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,8OAAC;;;;;wBAAK;wBAAI;sCACtC,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,8OAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,8OAAC;;;;;wBAAK;wBAAI;sCACtC,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;QAED,WAAW;QACX,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCACvE,8OAAC;;;;;wBAAK;;;;;;;YAI3C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,8OAAC;;;;;wBAAK;wBAAI;sCACtC,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB,EAAE;QACtB,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCACtE,8OAAC;;;;;wBAAK;wBAAI;wBAAI;sCAC1C,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAGjD;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IAEH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,8OAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,8OAAC;;;;;wBAAK;wBAAI;sCACtC,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SAMD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;iBACD;YACH;SACD;QACD,wBAAwB;QACxB,MAAM;QACN,gCAAgC;QAChC,kBAAkB;QAClB,wCAAwC;QACxC,+BAA+B;QAC/B,yBAAyB;QACzB,+BAA+B;QAC/B,iCAAiC;QACjC,oCAAoC;QACpC,kCAAkC;QAClC,8CAA8C;QAC9C,oCAAoC;QACpC,2BAA2B;QAC3B,SAAS;QACT,OAAO;QACP,KAAK;QAEL,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCACrF,8OAAC;;;;;wBAAK;sCACM,8OAAC;;;;;wBAAK;;;;;;;YAIzC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCACtE,8OAAC;;;;;wBAAK;;;;;;;YAI5C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBACnC;wBAAK;;;;;;;YAI9E;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAAuE;;;;;;YAIxF;SACD;QACD,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IAEH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OACE;wBACF,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aAAa;oBACf;oBACA;wBACE,OACE;wBACF,aAAa;oBACf;oBACA;wBACE,OAAO;wBACP,aACE;oBACJ;iBACD;YACH;SACD;QACD,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBAChF;wBAAK;wBACL;wBAAK;wBACG;wBAAK;;;;;;;YAIzC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBAChE;wBAAK;;;;;;;YAIjD;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBACtD;wBAAK;wBACnC;wBAAK;;;;;;;YAI7B;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBACxF;wBAAK;;;;;;;YAIzB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB,EAAE;QACtB,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,OAAO;wBACP,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD,CAAC,IAAI,CAAC;oBACT;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,oBAAoB;YAClB;gBACE,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;SACD;QACD,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBACvE;wBAAK;wBACf;wBAAK;;;;;;;YAIhC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBACrF;wBAAK;;;;;;;YAI5B;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;wBAC1E;wBAAK;;;;;;;YAIvC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB,EAAE;QACnB,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QAED,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QAED,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,wBACE,8OAAC;4BAAK,WAAU;;gCAA2F;8CACpF,8OAAC;;;;;gCAAK;8CACgB,8OAAC;;;;;gCAAK;8CAC5B,8OAAC;;;;;gCAAK;8CACQ,8OAAC;;;;;gCAAK;8CAC2B,8OAAC;;;;;gCAAK;8CAChD,8OAAC;;;;;;;;;;;oBAIjC;oBACA;wBACE,OAAO;wBACP,wBAEI,8OAAC;4BAAK,WAAU;;gCAA2F;8CAChD,8OAAC;;;;;gCAAK;8CACtC,8OAAC;;;;;gCAAK;8CACb,8OAAC;;;;;gCAAK;;;;;;;oBAIhC;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC/E,8OAAC;;;;;wBAAK;;;;;;;YAInC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;SACD;IAEH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,wBAAwB;QACxB,MAAM;QACN,yCAAyC;QACzC,mBAAmB;QACnB,sUAAsU;QACtU,OAAO;QACP,MAAM;QACN,sDAAsD;QACtD,mBAAmB;QACnB,qYAAqY;QACrY,OAAO;QACP,MAAM;QACN,8CAA8C;QAC9C,mBAAmB;QACnB,0zBAA0zB;QAC1zB,OAAO;QACP,KAAK;QACL,2BAA2B;QAC3B,MAAM;QACN,0DAA0D;QAC1D,mBAAmB;QACnB,2KAA2K;QAC3K,OAAO;QACP,MAAM;QACN,4DAA4D;QAC5D,mBAAmB;QACnB,8PAA8P;QAC9P,OAAO;QACP,MAAM;QACN,gHAAgH;QAChH,mBAAmB;QACnB,oQAAoQ;QACpQ,OAAO;QACP,MAAM;QACN,4LAA4L;QAC5L,mBAAmB;QACnB,6LAA6L;QAC7L,OAAO;QACP,MAAM;QACN,wMAAwM;QACxM,mBAAmB;QACnB,oKAAoK;QACpK,OAAO;QACP,MAAM;QACN,qMAAqM;QACrM,mBAAmB;QACnB,6OAA6O;QAC7O,OAAO;QACP,KAAK;QACL,oBAAoB;YAClB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,uBAAuB;YACrB;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OACE;wBACF,UAAU;wBACV,QAAQ;4BACN;4BACA;yBACD;oBACH;oBACA;wBACE,OAAO;wBACP,UAAU;wBACV,QAAQ;4BACN;4BACA;yBACD;oBACH;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC3E,8OAAC;;;;;wBAAK;;;;;;;YAIvC;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;;wBAA2F;sCAC1E,8OAAC;;;;;wBAAK;wBAAI;sCACtC,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;YAG7C;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,oBACE,8OAAC;oBAAE,WAAU;8BAA2F;;;;;;YAI5G;SACD;QACD,WAAW;QACX,mBAAmB;YACjB;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,cACE;QACF,iBAAiB;YACf;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;YACA;gBACE,OAAO;gBACP,aACE;YACJ;SACD;QACD,oBAAoB,EAAE;QACtB,uBAAuB,EAAE;QACzB,iBAAiB;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,OAAO;oBACL;wBACE,OAAO;wBACP,UACE;oBACJ;oBACA;wBACE,OAAO;wBACP,UACE;oBACJ;iBACD;YACH;SACD;QACD,iBAAiB;YACf;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;SACD;IACH;CAED"}}, {"offset": {"line": 1884, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/common/Footer.jsx"], "sourcesContent": ["import { coursesLinks } from \"@/constant/navbarLinks\";\r\nimport { Facebook, Instagram, Linkedin, Youtube } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport React from \"react\";\r\nimport { courseData } from \"@/constant/courseDetailData\";\r\n\r\nconst Footer = () => {\r\n  const currentYear = new Date().getFullYear();\r\n\r\n  return (\r\n    <footer className=\"bg-footerBG px-2 py-4 md:px-6 lg:px-12 xl:px-16\">\r\n      <div className=\"flex flex-col xl:flex-row gap-8\">\r\n\r\n        {/* Logo + Description + Socials */}\r\n        <div className=\"flex flex-col items-start lg:items-start text-start lg:text-start w-full xl:w-1/4\">\r\n          <Image\r\n            src=\"/skyline_logo.png\"\r\n            alt=\"The Skyline Aviation Club Logo\"\r\n            width={140}\r\n            height={130}\r\n            className=\"mb-4\"\r\n          />\r\n          <h2 className=\"text-xl font-bold text-footerPrimaryText mb-2\">\r\n            The Skyline Aviation Club <span className=\"align-super text-sm\">&reg;</span>\r\n          </h2>\r\n          <p className=\"text-xs font-bold text-footerPrimaryText\">\r\n            Since 1987, An Aviation Educational & Charitable Trust. <br />\r\n            Registered By Government.\r\n          </p>\r\n          <div className=\"flex gap-3 mt-4\">\r\n            <Link href=\"https://www.instagram.com/skylineaviationofficial/profilecard/?igsh=MTNuZzZiODdjc3NrNQ==\" className=\"bg-gray-300 p-3 rounded-full\" aria-label=\"Instagram\">\r\n              <Image src=\"/instagram.png\" alt=\"Instagram\" width={20} height={20} />\r\n            </Link>\r\n            <Link href=\"https://youtube.com/@captadmanek?feature=shared\" className=\"bg-gray-300 p-3 rounded-full\" aria-label=\"YouTube\">\r\n              <Image src=\"/youtube.png\" alt=\"YouTube\" width={20} height={20} />\r\n            </Link>\r\n            <Link href=\"https://www.linkedin.com/in/capt-dr-ad-manek-547722134?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app\" className=\"bg-gray-300 p-3 rounded-full\" aria-label=\"LinkedIn\">\r\n              <Image src=\"/linkedin.png\" alt=\"LinkedIn\" width={20} height={20} />\r\n            </Link>\r\n            <Link href=\"https://www.facebook.com/share/1CJ9ntLvHa/?mibextid=wwXIfr\" className=\"bg-gray-300 p-3 rounded-full\" aria-label=\"Facebook\">\r\n              <Image src=\"/facebook.png\" alt=\"Facebook\" width={20} height={20} />\r\n            </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Course + Contact + Address Section (flex changes based on screen) */}\r\n        <div className=\"flex flex-col md:flex-row lg:flex-row xl:flex-row gap-8 w-full xl:w-3/4\">\r\n\r\n          {/* Courses */}\r\n          <div className=\"flex-1\">\r\n            <h3 className=\"text-lg font-bold mb-2 text-footerPrimaryText\">Courses</h3>\r\n            <ul className=\"space-y-1\">\r\n              {coursesLinks.map((item, index) => (\r\n                <li key={index} className=\"capitalize\">\r\n                  <Link href={`/course/${item.link}`} className=\"text-black text-sm font-semibold capitalize\">\r\n                   {item.text}\r\n                  </Link>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Contact Info */}\r\n          <div className=\"flex-1\">\r\n            <h3 className=\"text-lg font-bold mb-2 text-footerPrimaryText\">Contact Info</h3>\r\n            <ul className=\"space-y-1\">\r\n              <li>\r\n                <Link href=\"tel:+919820891262\" className=\"text-black text-sm font-semibold\">\r\n                  +91 9820891262\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"tel:+919820896262\" className=\"text-black text-sm font-semibold\">\r\n                  +91 9820896262\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"tel:+912228983516\" className=\"text-black text-sm font-semibold\">\r\n                  +91 2228983516\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  href=\"mailto:<EMAIL>\"\r\n                  className=\"text-black text-sm font-semibold whitespace-nowrap\"\r\n                >\r\n                  Email: <EMAIL>\r\n                </Link>\r\n              </li>\r\n              <li className=\"text-black text-sm font-semibold whitespace-nowrap\">\r\n                Amateur Radio Call Sign: VU2 ADO\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Address - hidden on md, shown below instead */}\r\n          <div className=\"flex-1 hidden md:block\">\r\n            <h3 className=\"text-lg font-bold mb-2 text-footerPrimaryText\">Address</h3>\r\n            <p className=\"text-black text-sm font-bold\">\r\n              The Skyline Aviation Club, <br />\r\n              1st Floor, Shivam Apartment,<br />\r\n              Diagonally Opp. Joggers Park,<br />\r\n              Beside Simpoli Metro Station, Chikuwadi,<br />\r\n              Borivali West, Mumbai - 400092.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Address for md & below only */}\r\n        <div className=\"md:hidden mt-6\">\r\n          <h3 className=\"text-lg font-bold mb-2 text-footerPrimaryText\">Address</h3>\r\n          <p className=\"text-black text-sm font-bold\">\r\n            The Skyline Aviation Club, <br />\r\n            1st Floor, Shivam Apartment,<br />\r\n            Diagonally Opp. Joggers Park,<br />\r\n            Beside Simpoli Metro Station, Chikuwadi,<br />\r\n            Borivali West, Mumbai - 400092.\r\n          </p>\r\n        </div>\r\n      </div >\r\n\r\n      {/* Bottom Copyright */}\r\n      <div div className=\"mt-6 text-center\" >\r\n        <p className=\"text-footerBottomText text-xs font-medium\">\r\n          © {currentYear} The Skyline Aviation Club. All Rights Reserved.\r\n        </p>\r\n      </div >\r\n    </footer >\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,SAAS;IACb,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCAAG,WAAU;;oCAAgD;kDAClC,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAElE,8OAAC;gCAAE,WAAU;;oCAA2C;kDACE,8OAAC;;;;;oCAAK;;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAA2F,WAAU;wCAA+B,cAAW;kDACxJ,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAI;4CAAiB,KAAI;4CAAY,OAAO;4CAAI,QAAQ;;;;;;;;;;;kDAEjE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAkD,WAAU;wCAA+B,cAAW;kDAC/G,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAI;4CAAe,KAAI;4CAAU,OAAO;4CAAI,QAAQ;;;;;;;;;;;kDAE7D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAwI,WAAU;wCAA+B,cAAW;kDACrM,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAI;4CAAgB,KAAI;4CAAW,OAAO;4CAAI,QAAQ;;;;;;;;;;;kDAE/D,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAA6D,WAAU;wCAA+B,cAAW;kDAC1H,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CAAC,KAAI;4CAAgB,KAAI;4CAAW,OAAO;4CAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAMnE,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAG,WAAU;kDACX,+HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;gDAAe,WAAU;0DACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;oDAAE,WAAU;8DAC5C,KAAK,IAAI;;;;;;+CAFJ;;;;;;;;;;;;;;;;0CAUf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAoB,WAAU;8DAAmC;;;;;;;;;;;0DAI9E,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAoB,WAAU;8DAAmC;;;;;;;;;;;0DAI9E,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAoB,WAAU;8DAAmC;;;;;;;;;;;0DAI9E,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;;;;;;;;;;;;;0CAOvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;kDAC9D,8OAAC;wCAAE,WAAU;;4CAA+B;0DACf,8OAAC;;;;;4CAAK;0DACL,8OAAC;;;;;4CAAK;0DACL,8OAAC;;;;;4CAAK;0DACK,8OAAC;;;;;4CAAK;;;;;;;;;;;;;;;;;;;kCAOpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAC9D,8OAAC;gCAAE,WAAU;;oCAA+B;kDACf,8OAAC;;;;;oCAAK;kDACL,8OAAC;;;;;oCAAK;kDACL,8OAAC;;;;;oCAAK;kDACK,8OAAC;;;;;oCAAK;;;;;;;;;;;;;;;;;;;0BAOpD,8OAAC;gBAAI,GAAG;gBAAC,WAAU;0BACjB,cAAA,8OAAC;oBAAE,WAAU;;wBAA4C;wBACpD;wBAAY;;;;;;;;;;;;;;;;;;AAKzB;uCAEe"}}, {"offset": {"line": 2332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2338, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/common/Navbar.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/common/Navbar.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/Navbar.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA"}}, {"offset": {"line": 2346, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/common/Navbar.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/common/Navbar.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/Navbar.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA"}}, {"offset": {"line": 2360, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2370, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2376, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/toaster.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/toaster.jsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA"}}, {"offset": {"line": 2384, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/toaster.jsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/toaster.jsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA"}}, {"offset": {"line": 2398, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/app/layout.jsx"], "sourcesContent": ["import \"./globals.css\";\r\nimport Footer from \"@/components/common/Footer\";\r\nimport Navbar from \"@/components/common/Navbar\";\r\nimport { Toaster } from \"@/components/ui/toaster\"; \r\n\r\nexport const metadata = {\r\n  title: \"The Skyline Aviation Club\",\r\n  description: \"Your Pilot Journey Begins Here !\",\r\n   icons: {\r\n    icon: \"/assets/Favicon.png\",\r\n  },\r\n};\r\n\r\nexport default function RootLayout({ children }) {\r\n  return (\r\n    <html lang=\"en\">\r\n    <head>\r\n       <link\r\n          rel=\"icon\"\r\n          type=\"image/png\"\r\n          href=\"/assets/Favicon.png\"\r\n        ></link>\r\n    </head> \r\n      <body className={`relative antialiased      overflow-x-hidden`}>\r\n        <Navbar />\r\n        {children}\r\n        <Toaster />\r\n        <Footer />\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACZ,OAAO;QACN,MAAM;IACR;AACF;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;;0BACX,8OAAC;0BACE,cAAA,8OAAC;oBACE,KAAI;oBACJ,MAAK;oBACL,MAAK;;;;;;;;;;;0BAGT,8OAAC;gBAAK,WAAW,CAAC,2CAA2C,CAAC;;kCAC5D,8OAAC,sIAAA,CAAA,UAAM;;;;;oBACN;kCACD,8OAAC,mIAAA,CAAA,UAAO;;;;;kCACR,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;AAIf"}}, {"offset": {"line": 2485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}