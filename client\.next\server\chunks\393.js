exports.id=393,exports.ids=[393],exports.modules={8128:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,9607,23)),Promise.resolve().then(i.t.bind(i,1066,23)),Promise.resolve().then(i.bind(i,1451)),Promise.resolve().then(i.bind(i,3164))},7856:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,8531,23)),Promise.resolve().then(i.t.bind(i,1902,23)),Promise.resolve().then(i.bind(i,1087)),Promise.resolve().then(i.bind(i,2652))},2273:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,3219,23)),Promise.resolve().then(i.t.bind(i,4863,23)),Promise.resolve().then(i.t.bind(i,5155,23)),Promise.resolve().then(i.t.bind(i,802,23)),Promise.resolve().then(i.t.bind(i,9350,23)),Promise.resolve().then(i.t.bind(i,8530,23)),Promise.resolve().then(i.t.bind(i,8921,23))},8721:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6959,23)),Promise.resolve().then(i.t.bind(i,3875,23)),Promise.resolve().then(i.t.bind(i,1284,23)),Promise.resolve().then(i.t.bind(i,7174,23)),Promise.resolve().then(i.t.bind(i,4178,23)),Promise.resolve().then(i.t.bind(i,7190,23)),Promise.resolve().then(i.t.bind(i,1365,23))},1087:(e,t,i)=>{"use strict";i.d(t,{default:()=>P});var a=i(5512),r=i(8009),s=i(5103),n=i(8755),o=i(5907),l=i(4269),c=i(6438),d=i(8638),m=i(8531),u=i.n(m),p=i(6098),h=i(8739),g=i(7983),f=i(9905),x=i(4849),y=i(3689),b=i(6645);let v=g.bL,w=g.l9;g.YJ,g.ZL,g.Pb,g.z6,r.forwardRef(({className:e,inset:t,children:i,...r},s)=>(0,a.jsxs)(g.ZP,{ref:s,className:(0,b.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",e),...r,children:[i,(0,a.jsx)(f.A,{className:"ml-auto"})]})).displayName=g.ZP.displayName,r.forwardRef(({className:e,...t},i)=>(0,a.jsx)(g.G5,{ref:i,className:(0,b.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=g.G5.displayName;let A=r.forwardRef(({className:e,sideOffset:t=4,...i},r)=>(0,a.jsx)(g.ZL,{children:(0,a.jsx)(g.UC,{ref:r,sideOffset:t,className:(0,b.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...i})}));A.displayName=g.UC.displayName;let C=r.forwardRef(({className:e,inset:t,...i},r)=>(0,a.jsx)(g.q7,{ref:r,className:(0,b.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",t&&"pl-8",e),...i}));C.displayName=g.q7.displayName,r.forwardRef(({className:e,children:t,checked:i,...r},s)=>(0,a.jsxs)(g.H_,{ref:s,className:(0,b.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:i,...r,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(g.VF,{children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})}),t]})).displayName=g.H_.displayName,r.forwardRef(({className:e,children:t,...i},r)=>(0,a.jsxs)(g.hN,{ref:r,className:(0,b.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(g.VF,{children:(0,a.jsx)(y.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=g.hN.displayName,r.forwardRef(({className:e,inset:t,...i},r)=>(0,a.jsx)(g.JU,{ref:r,className:(0,b.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...i})).displayName=g.JU.displayName,r.forwardRef(({className:e,...t},i)=>(0,a.jsx)(g.wv,{ref:i,className:(0,b.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=g.wv.displayName;let P=()=>{let[e,t]=(0,r.useState)(!1),[i,m]=(0,r.useState)(!1),g=()=>m(e=>!e),f=()=>{t(!1),m(!1)};return(0,a.jsxs)("div",{className:"absolute left-0 right-0 top-8 md:w-4/5 mx-4 md:mx-auto lg:mx-auto z-20",children:[(0,a.jsx)("nav",{className:"bg-transparent bg-navbarBGPrimary rounded-full backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary px-3 py-2.5 flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsx)(u(),{href:"/",children:(0,a.jsx)(s.default,{src:"/skyline_logo.png",alt:"The Skyline Aviation Club Logo",width:50,height:50,className:"rounded-full"})}),(0,a.jsx)("div",{className:"hidden lg:flex items-center md:gap-3 lg:gap-5 xl:gap-16",children:p.pw.map((e,t)=>"Courses"===e.text?(0,a.jsxs)(v,{onOpenChange:e=>m(e),children:[(0,a.jsxs)(w,{className:"text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2",children:[e.text,(0,a.jsx)(n.A,{className:`w-5 h-5 transition-transform ${i?"rotate-180":"rotate-0"}`})]}),(0,a.jsx)(A,{className:"bg-navbarBGPrimary backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary flex flex-col w-42 gap-3 p-4 rounded-xl",children:p.U8.map((e,t)=>(0,a.jsx)(C,{asChild:!0,className:"p-0 focus:bg-transparent hover:text-textBluePrimary",children:(0,a.jsx)(u(),{className:"text-customBlack font-semibold hover:text-textBluePrimary transition block",href:`/course/${e.link}`,children:e.text})},t))})]},t):(0,a.jsx)(u(),{href:e.link,className:"text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2",children:e.text},t))}),(0,a.jsxs)(u(),{href:"/enquire",className:"hidden lg:flex items-center gap-2 bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 md:py-2 md:pr-2 md:pl-6 rounded-full border border-transparent",children:["Enquire Now",(0,a.jsx)(o.A,{size:18,className:"ml-2 w-8 h-8 p-1 bg-white rounded-full text-arrowIconColor"})]}),(0,a.jsx)("div",{className:"lg:hidden flex",children:(0,a.jsx)("button",{onClick:()=>{t(e=>!e),m(!1)},type:"button",children:e?(0,a.jsx)(l.A,{size:28}):(0,a.jsx)(c.A,{size:28})})})]})}),(0,a.jsx)("div",{className:`w-full flex flex-col absolute right-0 top-[4.5rem] bg-navbarBGPrimary border-navbarBGPrimary shadow-dropdownShadow backdrop-blur-[20px] rounded-lg transition-all duration-300 ease-in-out overflow-hidden transform ${e?"scale-100 opacity-100 max-h-[600px]":"scale-95 opacity-0 max-h-0 pointer-events-none"}`,children:p.pw.map((e,t)=>"Courses"===e.text?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:g,className:"p-3 text-customBlack font-semibold w-full flex items-center justify-between",children:["Courses",i?(0,a.jsx)(d.A,{}):(0,a.jsx)(n.A,{})]}),(0,a.jsx)("div",{className:`transition-all duration-300 ease-in-out overflow-hidden ${i?"max-h-96 opacity-100":"max-h-0 opacity-0"}`,children:(0,a.jsx)("ul",{className:"ml-4",children:h.W.map((e,t)=>(0,a.jsx)("li",{children:(0,a.jsx)(u(),{href:`/course/${e.slug}`,className:"block text-customBlack font-semibold hover:text-textBluePrimary transition p-2",onClick:f,children:e.courseDesc})},t))})})]},t):(0,a.jsx)(u(),{href:e.link,className:"p-3 text-customBlack font-semibold hover:text-textBluePrimary transition",onClick:f,children:e.text},t))})]})}},2652:(e,t,i)=>{"use strict";i.d(t,{Toaster:()=>x});var a=i(5512),r=i(1766),s=i(8009),n=i(8952),o=i(1643),l=i(4269),c=i(6645);let d=n.Kq,m=s.forwardRef(({className:e,...t},i)=>(0,a.jsx)(n.LM,{ref:i,className:(0,c.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));m.displayName=n.LM.displayName;let u=(0,o.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground",success:"success group border-green-500 bg-green-500 text-neutral-50"}},defaultVariants:{variant:"default"}}),p=s.forwardRef(({className:e,variant:t,...i},r)=>(0,a.jsx)(n.bL,{ref:r,className:(0,c.cn)(u({variant:t}),e),...i}));p.displayName=n.bL.displayName,s.forwardRef(({className:e,...t},i)=>(0,a.jsx)(n.rc,{ref:i,className:(0,c.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.rc.displayName;let h=s.forwardRef(({className:e,...t},i)=>(0,a.jsx)(n.bm,{ref:i,className:(0,c.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}));h.displayName=n.bm.displayName;let g=s.forwardRef(({className:e,...t},i)=>(0,a.jsx)(n.hE,{ref:i,className:(0,c.cn)("text-sm font-semibold [&+div]:text-xs",e),...t}));g.displayName=n.hE.displayName;let f=s.forwardRef(({className:e,...t},i)=>(0,a.jsx)(n.VY,{ref:i,className:(0,c.cn)("text-sm opacity-90",e),...t}));function x(){let{toasts:e}=(0,r.dj)();return(0,a.jsxs)(d,{children:[e.map(function({id:e,title:t,description:i,action:r,...s}){return(0,a.jsxs)(p,{...s,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&(0,a.jsx)(g,{children:t}),i&&(0,a.jsx)(f,{children:i})]}),r,(0,a.jsx)(h,{})]},e)}),(0,a.jsx)(m,{})]})}f.displayName=n.VY.displayName},8739:(e,t,i)=>{"use strict";i.d(t,{W:()=>r});var a=i(5512);let r=[{id:1,slug:"indian-commercial-pilot-licence-course",courseName:"DGCA",courseDesc:"Indian Commercial Pilot Licence Course",introduction:"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.",careerProspects:[{title:"Competitive Salaries",description:"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies."},{title:"Exciting and Dynamic Career",description:"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."},{title:"Prestige and Respect",description:"Gain recognition and admiration among peers and society as a skilled and accomplished aviator."},{title:"Travel the World",description:"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."}],theoryTrainingData:[{title:"Women Empowerment Scheme",description:"50% scholarship for girls & PWFA members by The Skyline Aviation Club"},{title:"Jai Jawan Scheme",description:"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"}],practicalTrainingData:[{title:"Scholarship - Govt. of India",description:"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."},{title:"Educational Loan - Govt. of Gujarat",description:"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."},{title:"Educational Loan - Banks",description:"Up to 85% of course fees from affiliated banks for general category students."}],courseStructure:[{title:"DGCA Commercial Pilot Licence Course – Airplane",courseDuration:"2 years",parts:[{title:"Part I - Theory Course",duration:"Theory Course - 4 Months (Full Time)"},{title:"Part II - Practical Flight Training of 200 Flight hours (185 Hours on Single Engine Land + 15 Hours on Multi Engine Lnad)",duration:"1 Year & 8 Months at our Affiliated Flight Training Organization (FTO)"}]}],subCourseStructure:[{title:"DGCA Commercial Pilot Licence Written Exams",subjects:["Air Navigation","Aviation Meteorology","Air Regulations","Aircraft & Engine (Technical) General & Specific"]},{title:"WPC, Ministry of Telecommunication Exam",licence:"Radio Telephony (Restricted) Licence / Flight Radio Telephone Licence",parts:["Part I: Radio Communication Procedures","Part II: Radio Theory"]}],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Age Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Minimum age - 17 Years ",(0,a.jsx)("br",{}),"Maximum age - 35 Years"]})},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Education",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["10+2 with Maths and Physics ",(0,a.jsx)("br",{})," or"," ",(0,a.jsx)("span",{className:"text-textBluePrimary",children:"Higher Education*"})]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Personality",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"Be able to read, write, speak and understand the English language"})},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Medical",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"DGCA Medical Class 2 Certificate (CA35)"})}],jobAssistanceData:[{title:"Comprehensive Career Grooming",desc:"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement"},{title:"Global Job Opportunities",desc:" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry."},{title:"Proven Success",desc:"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !"}]},{id:2,slug:"american-commercial-pilots-licence",courseName:"FAA USA",courseDesc:"American Commercial Pilot's Licence",introduction:"If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.",careerProspects:[{title:"Competitive Salaries",description:"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies."},{title:"Exciting and Dynamic Career",description:"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."},{title:"Prestige and Respect",description:"Gain recognition and admiration among peers and society as a skilled and accomplished aviator."},{title:"Travel the World",description:"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."}],theoryTrainingData:[{title:"Women Empowerment Scheme",description:"50% scholarship for girls & PWFA members by The Skyline Aviation Club"},{title:"Jai Jawan Scheme",description:"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"}],practicalTrainingData:[{title:"Scholarship - Govt. of India",description:"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."},{title:"Educational Loan - Govt. of Gujarat",description:"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."},{title:"Educational Loan - Banks",description:"Up to 85% of course fees from affiliated banks for general category students."}],courseStructure:[{title:"American FAA Commercial Pilot Licence Course - Airplane",courseDuration:"1 year",parts:[{title:"Part I - Theory Course (in India)",duration:"Theory Course - 3 Months (inclusive of PPC, IRC, CPC)"},{title:"Part II - Practical Flight Training (in USA)",duration:"Practical Flight Training - 9 Months (inclusive of 254 hrs with 25 hrs MEL) at our affiliated flight school Avel Flight School, Illinois, Chicago, USA"}]}],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-16 md:w-20 md:h-14 ",title:"Age Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Minimum age - 17 Years ",(0,a.jsx)("br",{}),"Maximum age - 35 Years"]})},{image:"/assets/educationImage.png",class:"w-16 h-16 md:w-20 md:h-14 ",title:"Education",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["10+2 with Maths and Physics ",(0,a.jsx)("br",{})," or"," ",(0,a.jsx)("span",{className:"text-textBluePrimary",children:"Higher Education*"})]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Personality",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"Be able to read, write, speak and understand the English language"})},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Medical",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"DGCA Medical Class 2 Certificate (CA35)"})}],jobAssistanceData:[{title:"Career Grooming & Job Assistance",desc:"Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad."},{title:"Proven Placement Record",desc:"So far, we have helped 398 students secure pilot jobs in various airlines worldwide."}],shortNote:"Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad. So far we have helped 398 students to get Pilot Jobs in various airlines",visaAsistant:"After completion of theory course, students have the option to choose a practical training location in India or United Stated of America. Students interested in going to USA will be assisted in Admission, I-20, DS-160, SEVIS and M1/F1 US Student Visa. So far we have helped 506 students procuring various students visas"},{id:3,slug:"aircraft-flight-dispatcher-licence-course",courseName:"FAA USA",courseDesc:"Aircraft/Flight Dispatcher Licence Course",introduction:"An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline. We Offer FAA(USA) Aircraft Dispatcher Licence Courses which will prepare you for entry into all Airlines worldwide.",careerProspects:[{title:"Competitive Salaries",description:"Earn 1.5 Lacs per month as you progress in your career with top airlines and private aviation companies."},{title:"Challenging and Demanding Career",description:"This career demands a high level of precision, focus, and resilience under pressure, making it both challenging and highly rewarding for those passionate about aviation."},{title:"Hot Seat Job",description:"The term perfectly describes the role of an Aircraft Dispatcher because of the intense pressure, accountability, and critical decision-making required in real time"},{title:"High Responsibities maintaining Multiple Aircrafts",description:"An Aircraft Dispatcher carries high responsibilities in maintaining multiple aircraft operations, including Simultaneous Flight Oversight, Real-Time Monitoring, Safety Assurance, Collaboration Across Teams. Prioritization Under Pressure"},{title:"Future Progress Quickly",description:"The Aircraft Dispatcher role offers opportunities for quick future progress due to: High Demand in Aviation, Career Advancement, Transferable Skills, Licensing and Certification, Dynamic Work Environment."}],theoryTrainingData:[{title:"Women Empowerment Scheme",description:"50% scholarship for girls & PWFA members by The Skyline Aviation Club"},{title:"Jai Jawan Scheme",description:"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"}],practicalTrainingData:[{title:"Educational Loan - Banks",description:"Up to 85% of course fees from affiliated banks for general category students."}],courseStructure:[{title:"FAA (USA) Aircraft/Flight Dispatcher Licence Course",courseDuration:"6 Months",parts:[{title:"Part I - Theory Course (in India)",duration:"Theory Course - 4 Months (inclusive of CPL, RTR, IRC And ICAO Aviation English Course)"},{title:"Part II - Practical Flight Training (in USA)",duration:"Practical Flight Training - 2 Months (inclusive of 200 hrs of Classroom Preparation )"}]}],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-16 md:w-20 md:h-14 ",title:"Age Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Training Eligibility: 21 years ",(0,a.jsx)("br",{}),"Licence Issuance: 23 years"]})},{image:"/assets/educationImage.png",class:"w-16 h-16 md:w-20 md:h-14 ",title:"Education",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["10+2 with Maths and Physics ",(0,a.jsx)("br",{})," or"," ",(0,a.jsx)("span",{className:"text-textBluePrimary",children:"Higher Education*"})]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14",title:"Personality",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"Be able to read, write, speak and understand the English language"})}],jobAssistanceData:[{title:"Career Grooming & Job Assistance",desc:"Successful students holding requisite aviation qualifications i.e CPL, IR, ME, FRTOL, RTR, Valid Medical Class 1, and Aviation English Level 4 will be groomed and assisted for job placement in India and abroad."},{title:"Proven Placement Record",desc:"So far, we have helped 398 students secure pilot jobs in various airlines worldwide."}]},{id:4,slug:"radio-telephony-r-aeromobile-frtol-licence",courseName:"DGCA/WPC",courseDesc:"Radio Telephony (R) Aeromobile/FRTOL Licence",introduction:"This professional course is structured in line with international radio regulations governing the aeronautical mobile service. It meets global standards, and candidates who achieve the required level of performance are awarded a government-recognized licence to operate.",careerProspects:[{title:"Competitive Salaries",description:"Start your aviation journey with a competitive salary of ₹25,000 as an FRTOL holder, paving the way for a rewarding career."},{title:"Career Opportunities as an FRTOL",description:"Student completing this course and on passing examination and acquiring RT (R) Licence issued by wireless Planning & Co-Ordination Wing, Ministry of Communication, Government of India, can seek employment in the various Indian as well as Foreign Airlines, Airport authorities, Aviation Companies, Oil rigs etc. as Aeronautical Radio Communication Officer"}],courseStructure:[{title:"FRTOL",courseDuration:"2 Months",parts:[{title:"Part I - Radio Telephony and Aviation Phonetics",duration:""},{title:"Part II - Technical Knowledge",duration:""}]}],theoryTrainingData:[],practicalTrainingData:[],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Age Criteria",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"Minimum Age: 18 years"})},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Education",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["12th Pass/Appeared (Any Stream) ",(0,a.jsx)("br",{})," or"," ","any ",(0,a.jsx)("span",{className:"text-textBluePrimary",children:"Higher Education*"})]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Personality",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"Be able to read, write, speak and understand the English language"})}],jobAssistanceData:[{title:"Job Placement for Radio Operators",desc:"Successful students holding requisite aviation qualifications, including a valid RTR (Radio Telephony Restricted) licence issued by the Government of India, will be groomed and assisted for job placement as Radio Officers on oil rigs and in aviation companies abroad."},{title:"Proven Track Record",desc:"So far, we have successfully helped 21 students secure Radio Operator jobs in various airlines and aviation organizations worldwide."}]},{id:5,slug:"commercial-pilot-licence-package-program",courseName:"Inclusive of Indian CPL + American CPL or Canadian CPL",courseDesc:"Commercial Pilot Licence Package Program",introduction:"The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.",careerProspects:[{title:"All-in-One Aviation Training",description:"Students receive all required aviation training under one roof, streamlining the learning process and eliminating the need for multiple institutions."},{title:"Economical Package Deal",description:"The bundled course structure is cost-effective for parents, offering maximum value across various aviation domains."},{title:"Versatile Career Preparation",description:"This program prepares students for multiple aviation roles, including Commercial Pilot, Flight Dispatcher, and Radio Telephony Officer, increasing job flexibility."},{title:"In-depth & Holistic Training",description:"The course delivers a comprehensive understanding of aviation, ensuring students are ready for the challenges of the aviation industry."},{title:"Recognized Certification & Logbook Endorsement",description:"Upon successful completion, students receive a course completion certificate and official logbook endorsement, boosting credibility for airline recruitment."}],theoryTrainingData:[{title:"Women Empowerment Scheme",description:"50% scholarship for girls & PWFA members by The Skyline Aviation Club"},{title:"Jai Jawan Scheme",description:"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"}],practicalTrainingData:[{title:"Scholarship - Govt. of India",description:"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."},{title:"Educational Loan - Govt. of Gujarat",description:"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."},{title:"Educational Loan - Banks",description:"Up to 85% of course fees from affiliated banks for general category students."}],courseStructure:[{title:"Complete Aviation Training Package – CPL, RTR, ATPL, Dispatcher & ELP",courseDuration:"1 Year",parts:[{title:"Part I - Theory Training (in India)",duration:"Theory Course – 3 Months (Full-time in Mumbai, covering Commercial Pilot Training, RTR, Aviation English, ATPL/Dispatcher, and ELP)"},{title:"Part II - Practical Flight Training (in USA)",duration:"Practical Training – 9 Months (Flight Training at our partnered facility in the USA)"}]}],subCourseStructure:[{title:"FAA Subject Coverage",subjects:["Air Regulation","Aviation Meteorology","Air Navigation","Audio Visual Training","Aircraft and Engine","Radio Communication","Aviation English","English Language Proficiency (ELP)"]},{title:"FAA Training Levels",subjects:["Student Pilot","Private Pilot","Instrument Rating","Commercial Pilot","RTR Licence","Aviation English","Airline Transport Pilot","Aircraft Dispatcher"]}],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Age Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Minimum age - 17 Years ",(0,a.jsx)("br",{}),"Maximum age - 35 Years"]})},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Education",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["10+2 with Maths and Physics ",(0,a.jsx)("br",{})," or"," ",(0,a.jsx)("span",{className:"text-textBluePrimary",children:"Higher Education*"})]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Personality",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"Be able to read, write, speak and understand the English language"})},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Medical",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"DGCA Medical Class 2 Certificate (CA35)"})}]},{id:6,slug:"airhostess-flight-purser-training-course",courseName:"Cabin Crew Training Program",courseDesc:"Airhostess/Flight Purser Training Course",introduction:"Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.",careerProspects:[{title:"Competitive Salaries",description:"Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines."},{title:"Competitive Salaries",description:"Earn up to ₹50,000 per month with domestic airlines and up to ₹1,00,000 per month with international airlines."},{title:"High Flying Career",description:"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."},{title:"Respect among Peers",description:"Gain recognition and admiration among peers and society as a skilled and accomplished aviator."},{title:"Fly all over the World",description:"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."}],theoryTrainingData:[{title:"Women Empowerment Scheme",description:"25% Scholarship for girls & PWFA members by The Skyline Aviation Club"},{title:"Jai Jawan Scheme",description:"25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"}],courseStructure:[{title:"Cabin Crew Course",courseDuration:"4 Months",parts:[{title:"Swimming",description:"Essential life-saving skill for emergencies."},{title:"Yoga",description:"Enhances flexibility and mental calmness."},{title:"Meditation",description:"Improves focus and stress management."},{title:"Aviation Security",description:"Trains in airport and aircraft security measures."},{title:"Reading Body Language",description:"Interpreting passenger behavior for better service and safety."},{title:"In-Flight Services",description:"Handling passenger needs and service during flights."},{title:"In-Flight Food",description:"Understanding catering and food management."},{title:"In-Flight Entertainment",description:"Providing and managing onboard entertainment."},{title:"Communication Skills",description:"Effective interaction with passengers and crew."},{title:"Aircraft Technical Knowledge",description:"Basic understanding of aircraft mechanics and systems."},{title:"Emergency Equipment on Board the Aircraft",description:"Knowledge of emergency tools and their use."},{title:"The Brace Position",description:"Training on proper brace positions during emergencies."},{title:"Aircraft Evacuation",description:"Procedures for safely evacuating an aircraft."},{title:"Survival (Sea, Jungle, Desert)",description:"Techniques for survival in various terrains."},{title:"Security Measures to Check Hijacking and Bomb",description:"Protocols to manage security threats."},{title:"Beauty and Perfect Airhostess or Flight Purser",description:"Training on grooming and professionalism."},{title:"First Aid",description:"Basic medical assistance for passengers and crew."},{title:"Etiquette and Manners (Finishing School)",description:"Refinement of interpersonal and professional behavior."}]}],practicalTrainingData:[],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Age & Height Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Age: 18–27 Years ",(0,a.jsx)("br",{}),"Girls: Min 5'2\" (Air Hostess)",(0,a.jsx)("br",{}),"Boys: Min 5'7\" (Flight Purser)"]})},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Educational Qualification",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["10+2 (Any Stream) – For Domestic",(0,a.jsx)("br",{}),"Graduate (Any Stream) – For International"]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Language Skills",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Must be able to read, speak, write, and understand English & Hindi","\n","Foreign Language: Added Advantage (For International Airlines)"]})},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Medical Fitness",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center mt-2",children:"Candidate must be medically fit as per airline standards."})}],jobAssistanceData:[{title:"Job Placement Assistance for Cabin Crew",desc:"Successful students holding the Diploma in In-Flight Management for Cabin Crew awarded by The Skyline Aviation Club will be groomed and assisted for airline job interviews and written tests. This includes placement support for both domestic airlines in India and international airlines abroad."}]},{id:7,slug:"airport-ground-staff-course",courseName:"Airport Ground Services",courseDesc:"Airport Ground Staff Course",introduction:"If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.",careerProspects:[{title:"Competitive Salaries",description:"Earn up to ₹20,000 per month with domestic airlines and up to ₹50,000 per month with international airlines."},{title:"High Flying Career",description:"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."},{title:"Respect among Peers",description:"Gain recognition and admiration among peers and society as a skilled and accomplished aviator."},{title:"Fly all over the World",description:"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."}],theoryTrainingData:[{title:"Women Empowerment Scheme",description:"25% Scholarship for girls & PWFA members by The Skyline Aviation Club"},{title:"Jai Jawan Scheme",description:"25% Scholarship scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"},{title:"Job Placement Assistance",description:"Successful Students holding requisite aviation qualification i.e Diploma in In-Flight Management for Cabin Crew received from The Skyline Aviaiton Club will be groomed and assisted for Airlilne Job Interviews and Written Tests for Domestic airlines in India and International Airlines Abroad"}],courseStructure:[{title:"Airport Ground Staff",courseDuration:"3 Months",parts:[{title:"Yoga",description:"Enhances flexibility and mental calmness."},{title:"Meditation",description:"Improves focus and stress management."},{title:"Aviation Security, Geography with respect to all airports in india. Airline and airport codes.",description:"Trains in airport and aircraft security measures."},{title:"Reading Body Language",description:"Interpreting passenger behavior for better service and safety."},{title:"Communication Skills",description:"Effective interaction with passengers and crew."},{title:"Aircraft Technical Knowledge",description:"Basic understanding of aircraft mechanics and systems."},{title:"Emergency Equipment on Board the Aircraft",description:"Knowledge of emergency tools and their use."},{title:"Security Measures to Check Hijacking and Bomb",description:"Protocols to manage security threats."},{title:"Beauty and Perfect Airhostess or Flight Purser",description:"Training on grooming and professionalism."},{title:"First Aid Airport Announcements, Passenger handling, issue of boarding pass.",description:"Basic medical assistance for passengers and crew."},{title:"Etiquette and Manners (Finishing School)",description:"Refinement of interpersonal and professional behavior."}]}],practicalTrainingData:[],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Age & Height Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Minimum Age: 18 Years","\n","Maximum Age: 27 Years","\n","Girls: Min 5'2\" (Air Hostess)","\n","Boys: Min 5'7\" (Flight Purser)"]})},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Educational Qualification",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["10+2 (Any Stream) – Domestic Airlines","\n","Graduate (Any Stream) – International Airlines"]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Language Proficiency",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Must be able to read, write, speak & understand","\n","English and Hindi","\n","Foreign Language – Added Advantage for Graduates"]})},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Medical & Other Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Medically Fit","\n","Must understand liability for surface or collision-related damages"]})}]},{id:8,slug:"aviation-foundation-course",courseName:"SUMMER VACATION TRAINING PROGRAM",courseDesc:"Aviation Foundation Course",introduction:"Aviation Foundation Course is specially designed for school and college students to create foundation for pursuing airline careers. Students will be guided towards various airline opportunities available i.e. Airline Pilot, Aircraft/Flight Dispatcher, Air hostess, Flight Purser, Ground Hostess, Traffic Assistant, Aeronautical Engineer, Aircraft Maintenance Engineer, Air Traffic Control Officer, Radio Officer.",careerProspects:[{title:"Benefits",description:"Young students have big dreams which change every day. This is a chance to get your child focused on what he or she loves. Students need not wait till 12th to decide a career path. One-stop complete aviation solution from zero to airline pilot. Early exposure to the aviation sector makes them much better prepared than other candidates."},{title:"Rewards",description:"Students will be issued a certificate of achievement on the completion of the course. Group photograph. Individual photograph with instructor and helicopter or aeroplane. Memento as Future Pilot. Airline Career Guidance Booklet."}],theoryTrainingData:[],courseStructure:[{title:"Student & Private Pilot Licence Training",courseDuration:"Flexible (Introductory Program)",parts:[{title:"Part I – Theory Training (DGCA Syllabus)",duration:"Covers core subjects as per DGCA (Govt. of India) guidelines"},{title:"Subjects Covered",duration:"Air Regulation, Aviation Meteorology, Air Navigation, Audio Visual Training, Aircraft and Engine, Radio Communication"},{title:"Part II – Practical Orientation",duration:"Includes familiarization briefing + discovery flight in helicopter/airplane for 15 minutes"}]}],subCourseStructure:[{title:"Admission Procedure",subjects:["Fill out the online registration form.","Send the following documents by courier or submit personally:","– Two passport-size photographs","– Certified true copy of your school ID card"]}],practicalTrainingData:[],eligibilityData:[{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Who Can Apply?",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Students of Schools & Colleges","\n","Hobby Flyers Welcome","\n","Age: No Bar"]})},{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Course Duration",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["3 Weeks Duration","\n","3 Hours per Day"]})},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Batch Timings",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Morning Batch: 10 AM – 1 PM","\n","Noon Batch: 2 PM – 5 PM"]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Batch Size",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"20 Students per Batch"})}]},{id:9,slug:"two-day-aeroplane-or-helicopter-training-workshop",courseName:"Aeroplane/Helicopter Orientation Training",courseDesc:"Two Day Aeroplane/Helicopter Training Workshop",introduction:"Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter. They will be exposed at an early age to experience what it feels like to fly amongst the clouds. This course will open multiple career oppurtunities in the field of aviation.",careerProspects:[],theoryTrainingData:[{title:"Scholarship - Govt. of India",description:"Scholarship of ₹30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."}],practicalTrainingData:[{title:"Educational Loan - Govt. of Gujarat",description:"Educational Loan of ₹25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."},{title:"Educational Loan - Banks",description:"Up to 85% of course fees from affiliated Banks for General Category Students."},{title:"Success Track Record",description:"So far, 54 students have been successful in procuring these benefits."}],courseStructure:[{title:"AEROPLANE/HELICOPTER TRAINING WORKSHOP",courseDuration:"2 Day",parts:[{title:"Theory Training",duration:(0,a.jsxs)("span",{className:"text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["• How helicopters fly",(0,a.jsx)("br",{}),"• Parts of a helicopter and their functions",(0,a.jsx)("br",{}),"• Cockpit orientation",(0,a.jsx)("br",{}),"• Live ATC communication monitoring",(0,a.jsx)("br",{}),"• Audio-visual guide on Principles of Flight and History of Aviation",(0,a.jsx)("br",{}),"• Aviation career guidance",(0,a.jsx)("br",{})]})},{title:"Practical Experience",duration:(0,a.jsxs)("span",{className:"text-md md:text-md font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["• 15-minute Helicopter Discovery Flight at Juhu Aerodrome",(0,a.jsx)("br",{}),"• Full-time tour director",(0,a.jsx)("br",{}),"• Travel insurance",(0,a.jsx)("br",{}),"• Breakfast & Lunch included"]})}]}],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Eligibility Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Minimum age - 12 Years ",(0,a.jsx)("br",{}),"Minimum Class - VI onwards"]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Dress Code",desc:"School Uniform with valid School ID"},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Batch Size",desc:"Minimum 74 students (batches may be combined if minimum not met)"},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Parental Consent",desc:"Discovery Flight Consent Form signed by parent"}]},{id:10,slug:"foreign-commercial-pilot-licence-conversion-course",courseName:"INDIAN COMMERCIAL PILOT LICENCE (DGCA)",courseDesc:"Foreign Commercial Pilot Licence Conversion Course",introduction:"The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.",careerProspects:[{title:"Competitive Salaries",description:"Earn between ₹3 Lakhs to ₹7 Lakhs per month as you progress in your career with top airlines and private aviation companies."},{title:"Exciting and Dynamic Career",description:"Embark on a high-flying profession that keeps you at the forefront of innovation and global connectivity."},{title:"Prestige and Respect",description:"Gain recognition and admiration among peers and society as a skilled and accomplished aviator."},{title:"Travel the World",description:"Explore international destinations, cultures, and landscapes, all while building a rewarding career in the skies."}],theoryTrainingData:[{title:"Women Empowerment Scheme",description:"50% scholarship for girls & PWFA members by The Skyline Aviation Club"},{title:"Jai Jawan Scheme",description:"50% scholarship for sons/daughters of Army, Navy, IAF personnel by The Skyline Aviation Club"}],practicalTrainingData:[{title:"Scholarship - Govt. of India",description:"Scholarship of Rs.30 Lacs available under Post Matric Scholarship Scheme of Govt. of India for SC/ST/OBC Students."},{title:"Educational Loan - Govt. of Gujarat",description:"Educational Loan of Rs.25 Lacs from Govt. of Gujarat for SC/ST/OBC Students."},{title:"Educational Loan - Banks",description:"Up to 85% of course fees from affiliated banks for general category students."}],courseStructure:[{title:"Foreign Pilot Licence Conversion Course (DGCA) - Theory Course",courseDuration:"2 months",parts:[{title:"Prepares for DGCA Commercial Pilot Licence conversion Exams",duration:"",papers:["Paper 1 - Composite Paper (Air navigation & Air Meteorology)","Paper 2 - Air Regulations"]},{title:"Prepare for WPC, Ministry of Telecommunication Exam",duration:"",papers:["Part I: Radio Communication Procedures","Part II: Radio Theory"]}]}],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Age Criteria",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["Minimum age: Age 12+ years ",(0,a.jsx)("br",{}),"Minimum class: VI onwards"]})},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Education",desc:(0,a.jsxs)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:["10+2 with Maths and Physics ",(0,a.jsx)("br",{})," or"," ",(0,a.jsx)("span",{className:"text-textBluePrimary",children:"Higher Education*"})]})},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Personality",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"Be able to read, write, speak and understand the English language"})},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Medical",desc:(0,a.jsx)("p",{className:"text-xs md:text-sm font-normal text-neutralGrayText text-center whitespace-pre-line mt-2",children:"DGCA Medical Class 2 Certificate (CA35)"})}],shortNote:"Successful Students holding requisite aviation qualification i.e CPL/IR/ME/FRTOL/RTR/Valid Medical Class 1/Aviation English Level 4; will be groomed and assisted for Job Placement in India and Abroad under Airline Prep Course.  So far we have helped 720 plus students to get Pilot Jobs in various airlines",jobAssistanceData:[{title:"Comprehensive Career Grooming",desc:"Students holding qualifications such as CPL, IR, ME, FRTOL, RTR, Class 1 Medical Certification, and Aviation English Level 4 are meticulously groomed and prepared for placement"},{title:"Global Job Opportunities",desc:" We provide guidance and assistance for pilot job placements in both domestic and international airlines, ensuring you are fully equipped to thrive in a competitive industry."},{title:"Proven Success",desc:"So far, we've proudly assisted 398 students in securing pilot positions with leading airlines worldwide. Your success is our mission !"}]},{id:11,slug:"helicopter-commercial-pilot-licence-course",courseName:"HCPL",courseDesc:"Helicopter Commercial Pilot Licence Course",introduction:"The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.",careerProspects:[{title:"Professional Opportunities",description:"Work with private helicopter operators, tourism companies, charter services, and emergency medical services (HEMS)."},{title:"Government and Defense Roles",description:"Opportunities in government agencies, paramilitary forces, and search & rescue operations."},{title:"Corporate and VIP Transport",description:"Serve as pilot for high-profile individuals, corporate executives, and political dignitaries."}],theoryTrainingData:[],practicalTrainingData:[],courseStructure:[{title:"HCPL Training Program",courseDuration:"12 to 18 Months",parts:[{title:"Part I - Theory Training",duration:"Covers DGCA syllabus including Air Navigation, Aviation Meteorology, Air Regulations, Aircraft & Engines (Technical), and Radio Telephony."},{title:"Part II - Practical Flight Training",duration:"Minimum of 150 hours of helicopter flying at an approved FTO under instructor supervision."}]}],eligibilityData:[{image:"/assets/ageCriteriaImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Age Criteria",desc:"Minimum 18 years of age at the time of licence issue"},{image:"/assets/educationImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Education",desc:"10+2 with Physics and Mathematics or equivalent (NIOS acceptable)"},{image:"/assets/medicalImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Medical",desc:"Class II Medical for training & Class I Medical for licence issuance"},{image:"/assets/personalityImage.png",class:"w-16 h-20 md:w-20 md:h-14 ",title:"Language Proficiency",desc:"Must be able to read, write, speak and understand English"}]}]},6098:(e,t,i)=>{"use strict";i.d(t,{U8:()=>r,pw:()=>a});let a=[{text:"Home",link:"/"},{text:"Gallery",link:"/gallery"},{text:"Courses",link:"/course"},{text:"Events",link:"/events"},{text:"About Us",link:"/aboutus"},{text:"Contact Us",link:"/enquire"}],r=[{text:"Indian Commercial Pilot",link:"indian-commercial-pilot-licence-course"},{text:"American Commercial Pilot",link:"american-commercial-pilots-licence"},{text:"CPL Package Program",link:"commercial-pilot-licence-package-program"},{text:"Foreign CPL Conversion Course",link:"foreign-commercial-pilot-licence-conversion-course"},{text:"Helicopter Pilot Training",link:"helicopter-commercial-pilot-licence-course"},{text:"American Flight Dispatcher",link:"aircraft-flight-dispatcher-licence-course"},{text:"Radio Telephony Licence",link:"radio-telephony-r-aeromobile-frtol-licence"},{text:"Airhostess/Flight Purser",link:"airhostess-flight-purser-training-course"},{text:"Airport Ground Staff",link:"airport-ground-staff-course"},{text:"Aviation Foundation Course",link:"aviation-foundation-course"},{text:"Aeroplane/Helicopter Training Workshop",link:"two-day-aeroplane-or-helicopter-training-workshop"}]},1766:(e,t,i)=>{"use strict";i.d(t,{dj:()=>u});var a=i(8009);let r=0,s=new Map,n=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:i}=t;return i?n(i):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=o(c,e),l.forEach(e=>{e(c)})}function m({...e}){let t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),i=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||i()}}}),{id:t,dismiss:i,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=a.useState(c);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},6645:(e,t,i)=>{"use strict";i.d(t,{cn:()=>s});var a=i(2281),r=i(4805);function s(...e){return(0,r.QP)((0,a.$)(e))}},927:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u,metadata:()=>m});var a=i(2740);i(1135);let r=[{text:"Indian Commercial Pilot",link:"indian-commercial-pilot-licence-course"},{text:"American Commercial Pilot",link:"american-commercial-pilots-licence"},{text:"CPL Package Program",link:"commercial-pilot-licence-package-program"},{text:"Foreign CPL Conversion Course",link:"foreign-commercial-pilot-licence-conversion-course"},{text:"Helicopter Pilot Training",link:"helicopter-commercial-pilot-licence-course"},{text:"American Flight Dispatcher",link:"aircraft-flight-dispatcher-licence-course"},{text:"Radio Telephony Licence",link:"radio-telephony-r-aeromobile-frtol-licence"},{text:"Airhostess/Flight Purser",link:"airhostess-flight-purser-training-course"},{text:"Airport Ground Staff",link:"airport-ground-staff-course"},{text:"Aviation Foundation Course",link:"aviation-foundation-course"},{text:"Aeroplane/Helicopter Training Workshop",link:"two-day-aeroplane-or-helicopter-training-workshop"}];var s=i(5635),n=i(9607),o=i.n(n);i(6301);let l=()=>{let e=new Date().getFullYear();return(0,a.jsxs)("footer",{className:"bg-footerBG px-2 py-4 md:px-6 lg:px-12 xl:px-16",children:[(0,a.jsxs)("div",{className:"flex flex-col xl:flex-row gap-8",children:[(0,a.jsxs)("div",{className:"flex flex-col items-start lg:items-start text-start lg:text-start w-full xl:w-1/4",children:[(0,a.jsx)(s.default,{src:"/skyline_logo.png",alt:"The Skyline Aviation Club Logo",width:140,height:130,className:"mb-4"}),(0,a.jsxs)("h2",{className:"text-xl font-bold text-footerPrimaryText mb-2",children:["The Skyline Aviation Club ",(0,a.jsx)("span",{className:"align-super text-sm",children:"\xae"})]}),(0,a.jsxs)("p",{className:"text-xs font-bold text-footerPrimaryText",children:["Since 1987, An Aviation Educational & Charitable Trust. ",(0,a.jsx)("br",{}),"Registered By Government."]}),(0,a.jsxs)("div",{className:"flex gap-3 mt-4",children:[(0,a.jsx)(o(),{href:"https://www.instagram.com/skylineaviationofficial/profilecard/?igsh=MTNuZzZiODdjc3NrNQ==",className:"bg-gray-300 p-3 rounded-full","aria-label":"Instagram",children:(0,a.jsx)(s.default,{src:"/instagram.png",alt:"Instagram",width:20,height:20})}),(0,a.jsx)(o(),{href:"https://youtube.com/@captadmanek?feature=shared",className:"bg-gray-300 p-3 rounded-full","aria-label":"YouTube",children:(0,a.jsx)(s.default,{src:"/youtube.png",alt:"YouTube",width:20,height:20})}),(0,a.jsx)(o(),{href:"https://www.linkedin.com/in/capt-dr-ad-manek-547722134?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app",className:"bg-gray-300 p-3 rounded-full","aria-label":"LinkedIn",children:(0,a.jsx)(s.default,{src:"/linkedin.png",alt:"LinkedIn",width:20,height:20})}),(0,a.jsx)(o(),{href:"https://www.facebook.com/share/1CJ9ntLvHa/?mibextid=wwXIfr",className:"bg-gray-300 p-3 rounded-full","aria-label":"Facebook",children:(0,a.jsx)(s.default,{src:"/facebook.png",alt:"Facebook",width:20,height:20})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row lg:flex-row xl:flex-row gap-8 w-full xl:w-3/4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 text-footerPrimaryText",children:"Courses"}),(0,a.jsx)("ul",{className:"space-y-1",children:r.map((e,t)=>(0,a.jsx)("li",{className:"capitalize",children:(0,a.jsx)(o(),{href:`/course/${e.link}`,className:"text-black text-sm font-semibold capitalize",children:e.text})},t))})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 text-footerPrimaryText",children:"Contact Info"}),(0,a.jsxs)("ul",{className:"space-y-1",children:[(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"tel:+919820891262",className:"text-black text-sm font-semibold",children:"+91 9820891262"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"tel:+919820896262",className:"text-black text-sm font-semibold",children:"+91 9820896262"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"tel:+912228983516",className:"text-black text-sm font-semibold",children:"+91 2228983516"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"mailto:<EMAIL>",className:"text-black text-sm font-semibold whitespace-nowrap",children:"Email: <EMAIL>"})}),(0,a.jsx)("li",{className:"text-black text-sm font-semibold whitespace-nowrap",children:"Amateur Radio Call Sign: VU2 ADO"})]})]}),(0,a.jsxs)("div",{className:"flex-1 hidden md:block",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 text-footerPrimaryText",children:"Address"}),(0,a.jsxs)("p",{className:"text-black text-sm font-bold",children:["The Skyline Aviation Club, ",(0,a.jsx)("br",{}),"1st Floor, Shivam Apartment,",(0,a.jsx)("br",{}),"Diagonally Opp. Joggers Park,",(0,a.jsx)("br",{}),"Beside Simpoli Metro Station, Chikuwadi,",(0,a.jsx)("br",{}),"Borivali West, Mumbai - 400092."]})]})]}),(0,a.jsxs)("div",{className:"md:hidden mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-bold mb-2 text-footerPrimaryText",children:"Address"}),(0,a.jsxs)("p",{className:"text-black text-sm font-bold",children:["The Skyline Aviation Club, ",(0,a.jsx)("br",{}),"1st Floor, Shivam Apartment,",(0,a.jsx)("br",{}),"Diagonally Opp. Joggers Park,",(0,a.jsx)("br",{}),"Beside Simpoli Metro Station, Chikuwadi,",(0,a.jsx)("br",{}),"Borivali West, Mumbai - 400092."]})]})]}),(0,a.jsx)("div",{div:!0,className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-footerBottomText text-xs font-medium",children:["\xa9 ",e," The Skyline Aviation Club. All Rights Reserved."]})})]})};var c=i(1451),d=i(3164);let m={title:"The Skyline Aviation Club",description:"Your Pilot Journey Begins Here !",icons:{icon:"/assets/Favicon.png"}};function u({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsx)("head",{children:(0,a.jsx)("link",{rel:"icon",type:"image/png",href:"/assets/Favicon.png"})}),(0,a.jsxs)("body",{className:"relative antialiased      overflow-x-hidden",children:[(0,a.jsx)(c.default,{}),e,(0,a.jsx)(d.Toaster,{}),(0,a.jsx)(l,{})]})]})}},1451:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Skyline-Aviation\\\\client\\\\src\\\\components\\\\common\\\\Navbar.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\common\\Navbar.jsx","default")},3164:(e,t,i)=>{"use strict";i.d(t,{Toaster:()=>a});let a=(0,i(6760).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\Skyline-Aviation\\client\\src\\components\\ui\\toaster.jsx","Toaster")},1135:()=>{}};