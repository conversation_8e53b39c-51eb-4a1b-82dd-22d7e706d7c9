"use client";
import Image from "next/image";
import {
  courseData,
} from "../constant/courseDetailData";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useState, useRef, useEffect } from "react";
import { coursesData } from "../constant/landingPageData";

const CourseDetailComponent = () => {

  const [modalType, setModalType] = useState(null);
  const modalRef = useRef(null);
  const [currentCourse, setCurrentCourse] = useState(null);
  const { slug } = useParams();
  const course = courseData.find((course) => course.slug === slug);

  if (!course) {
    return <div>Course Not Found</div>;
  }


  useEffect(() => {
    if (slug) {
      const course = coursesData.find(course => course.slug === slug);
      setCurrentCourse(course);
      console.log(course);
      console.log(course.image);
    }
  }, [slug]);



  useEffect(() => {
    function handleClickOutside(event) {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setModalType(false);
      }
    }

    if (modalType) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [modalType]);

  // console.log(course);


  return (
    <>
      <div style={{
        backgroundImage: currentCourse ? `url(${currentCourse?.bgImage})` : 'none'
      }} className="relative bg-cover bg-no-repeat  bg-center h-[500px] md:h-[700px]">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]"></div>
        {course ? (
          <div className="absolute left-0 right-0 bottom-28 md:bottom-32 mx-4 md:mx-20 text-center">
            <h1
              className="font-extrabold text-whiteOne text-2xl md:text-6xl lg:text-6xl xl:text-6xl mb-4"
              style={{ textShadow: "2px 2px 4px black" }}
            >
              {course.courseDesc.toUpperCase()}
            </h1>

            <p className="text-formBG font-bold md:font-light text-xl md:text-3xl lg:text-2xl lg:w-[32rem] lg:mx-auto"
              style={{ textShadow: "2px 2px 4px black" }}
            >
              {(course.courseName).toUpperCase()}
            </p>
          </div>
        ) : (
          ""
        )}
      </div>
      <div className="relative z-0 bg-white">
        <div className="hidden lg:block z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute top-80 left-0 right-0 w-full h-[300px]"></div>
        <div className="hidden lg:block absolute bg-dotsImage top-36 right-0 bg-center bg-no-repeat w-10 h-36"></div>
        <div className="z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-0 lg:bottom-60 left-0 right-0 w-full h-[300px]"></div>
        <div className="hidden lg:block absolute bg-dotsImage top-[48rem] left-0 bg-center bg-no-repeat w-10 h-36"></div>
        <div className="py-4 px-4 lg:px-12 xl:px-32 relative z-10">
          <div className="absolute left-0 right-0 -top-20 mx-4 text-center p-3 md:p-4 lg:p-8 lg:mx-36 xl:p-8 rounded-xl shadow-courseCardShadow bg-white">
            <h3 className="text-textBluePrimary mb-2 font-bold text-xl md:text-2xl xl:text-3xl lg:text-3xl">
              Introduction
            </h3>
            <p className="descLightBlack text-xs leading-5
             lg:text-base xl:text-base font-medium lg:mx-32">
              {course.introduction}
            </p>
          </div>
          <div className=" lg:mt-32 xl:mt-44 md:mt-32 mt-24 p-4 md:shadow-none mb-8">
            <h3 className="text-textBluePrimary text-center mb-4 md:mb-6 font-bold text-xl md:text-2xl lg:text-3xl">
              Eligibility
            </h3>
            <div
              className={`grid gap-2 md:gap-4 lg:gap-6 ${course.eligibilityData.length === 3
                ? 'grid-cols-1 sm:grid-cols-3'
                : 'grid-cols-2 md:grid-cols-4'
                } w-full`}
            >
              {/* <div className="flex flex-wrap w-full gap-2 md:gap-4 lg:gap-6"> */}
              {course.eligibilityData.map((item, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center p-2 md:p-4 rounded-xl shadow-xl bg-white "
                >
                  <Image
                    src={item.image}
                    alt={`${item.title} Icon`}
                    className={`${item.class} mb-2 md:lg-4 h-20`}
                    width={100}
                    height={100}
                  />
                  {/* <div> */}
                    <h3 className="font-semibold text-center text-sm md:text-base text-neutralGrayText">
                      {item.title}
                    </h3>
                    {item.title === 'Education' ? (
                      <>
                        <span className="text-center text-sm md:text-base text-neutralGrayText">
                          {item.desc}
                        </span>
                        <button
                          onClick={() => setModalType("Education")}
                          className="mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm"
                        >
                          Note
                        </button>
                      </>
                    ) : item.title === 'Medical' ? (
                      <>
                        <span className="text-center text-sm md:text-base text-neutralGrayText">
                          {item.desc}
                        </span>
                        <button
                          onClick={() => setModalType("Medical")}
                          className="mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm"
                        >
                          Note
                        </button>
                      </>
                    ) : (
                      <p className="text-center text-sm md:text-base text-neutralGrayText">
                        {item.desc}
                      </p>
                    )}
                  {/* </div> */}
                </div>
              ))}
              {modalType && (
                <div className="fixed inset-0 flex items-center justify-center z-50 px-4 md:px-6" style={{ background: "rgba(0,0,0,0.3)" }}>
                  <div
                    ref={modalRef}
                    className={`bg-white rounded-xl p-4 md:p-6 shadow-lg relative ${modalType === "Scholarship"
                      ? "max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                      : "max-w-md"
                      }`}
                  >
                    <button
                      onClick={() => setModalType(null)}
                      className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 text-xl font-bold z-10"
                    >
                      ×
                    </button>

                    {modalType === "Scholarship" ? (
                      <div>
                        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary mb-4 md:mb-6 text-center md:text-left">
                          Loan Details
                        </h2>

                        {/* Only show loan section (practicalTrainingData) for specific courses */}
                        {["indian-commercial-pilot", "american-commercial-pilot", "cpl-package-program", "helicopter-pilot-training", "american-flight-dispatcher"].includes(course.slug) &&
                          course.practicalTrainingData.length > 0 && (
                            <div>
                              <h3 className="font-bold text-lg md:text-xl text-customBlack mb-3">
                                Loan for Practical Flight Training
                              </h3>
                              <ul className="space-y-3">
                                {course.practicalTrainingData.map((item, index) => (
                                  <li key={index} className="border-b border-gray-200 pb-3 last:border-b-0">
                                    <h4 className="text-base md:text-lg font-semibold text-customBlack mb-2">
                                      {item.title}
                                    </h4>
                                    <p className="font-medium text-sm md:text-base text-gray-700">
                                      {item.description}
                                    </p>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                      </div>
                    ) : (
                      <>
                        <h2 className="text-lg font-semibold mb-2">Important Note</h2>
                        {modalType === "Education" && (
                          <p className="text-sm text-gray-700">
                            If not from science background, then you require to appear and pass
                            Physics and Mathematics subjects of 12th Level conducted by NIOS, New
                            Delhi. Students can pursue our training program and simultaneously
                            appear for above mentioned exams. Please contact us for further
                            information.
                          </p>
                        )}

                        {modalType === "Medical" && (
                          <p className="text-sm text-gray-700">
                            We Will arrange Medical Examination with our panel doctor who is DGCA approved Class II Medical Examiner
                          </p>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}

            </div>
          </div>
          <div className="flex gap-4 mb-8 flex-col md:flex-row">
            <div className="bg-white shadow-courseCardShadow p-4 md:p-6 rounded-[20px] md:basis-2/5">
              <h2 className="text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center md:text-left lg:text-left xl:text-left font-bold text-textBluePrimary mb-4">
                Course Structure
              </h2>
              <ul className="space-y-4 md:pl-3 md:marker:text-2xl marker:font-bold md:list-decimal">
                {course?.courseStructure && course.courseStructure.map((item, index) => (
                  <li key={`course-${index}`} className="md:ml-4">
                    <h3 className="font-bold text-base lg:text-2xl text-customBlack mb-2">
                      {item.title}
                    </h3>
                    <p className="font-bold text-base lg:text-xl text-customBlack flex  gap-1 mb-2">
                      Course Duration:
                      <span className="font-medium text-base md:text-xl">
                        {item.courseDuration}
                      </span>
                    </p>
                    {item.parts.map((item, index) => (
                      <>
                        <p
                          key={index}
                          className="font-normal text-baxe lg:text-lg text-customBlack flex flex-col gap-1 mb-2"
                        >
                          {item.title}
                          <span className="font-medium text-base md:text-lg">
                            {item.duration}
                          </span>
                        </p>
                        {item.papers && (
                          <ul className="space-y-1">
                            {item.papers.map((paper, idx) => (
                              <li
                                key={idx}
                                className="font-small text-base md:text-lg"
                              >
                                {paper}
                              </li>
                            ))}
                          </ul>
                        )}
                      </>
                    ))}
                    {course.subCourseStructure &&
                      course.subCourseStructure.map((item, index) => (
                        <div key={index}>
                          <h3 className="font-bold text-base lg:text-xl text-customBlack flex flex-col gap-1 mb-2">
                            {item.title}
                          </h3>
                          {item.subjects && (
                            <ul className="space-y-1">
                              {item.subjects.map((subject, idx) => (
                                <li
                                  key={idx}
                                  className="font-medium text-base md:text-lg"
                                >
                                  {subject}
                                </li>
                              ))}
                            </ul>
                          )}
                          {item.licence && (
                            <h4 className="font-bold text-base lg:text-lg text-customBlack flex flex-col gap-1 mb-2">
                              {item.licence}
                            </h4>
                          )}
                          {item.parts && (
                            <ul className="space-y-1">
                              {item.parts.map((part, idx) => (
                                <li
                                  key={idx}
                                  className="font-medium text-base md:text-lg"
                                >
                                  {part}
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      ))}

                  </li>
                ))}
              </ul>
              {course.visaAsistant && (
                <>
                  <h3 className="font-bold text-base text-customBlack mb-2">
                    Student Visa Assistant
                  </h3>
                  <p className="mt-4 font-medium text-md text-customBlack mb-4">
                    {course.visaAsistant}
                  </p>
                </>
              )}
              {course.shortNote && (
                <p className="mt-4 font-medium text-md text-customBlack mb-4">
                  {course.shortNote}
                </p>
              )}
              {/* <p className="mt-4 font-medium text-md text-customBlack italic">
                **If not from science background, then you require to appear and
                pass Physics and Mathematics subjects of 12th Level conducted by
                NIOS, New Delhi. Students can pursue our training program and
                simultaneously appear for above mentioned exams. Please contact
                us for further information.
              </p> */}
              <Link
                href="/enquire"
                className="block text-center bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 mt-3 lg:text-lg rounded-full border border-transparent"
              >
                Enquire Now
              </Link>
            </div>



            {/* Scholarship */}
            <div className="flex flex-col gap-6 md:basis-3/5">
              {(course.theoryTrainingData.length ||
                course.practicalTrainingData.length) > 0 && (
                  <div className="bg-white shadow-lg p-6 rounded-[20px]">
                    <h2 className="text-center md:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4">
                      Loan and Scholarship
                    </h2>
                    <div>
                      {course.theoryTrainingData.length > 0 && (
                        <>
                          <h3 className="font-bold text-lg text-customBlack mb-2">
                            Scholarships for Theory Training
                          </h3>
                          <ul className="space-y-2">
                            {course.theoryTrainingData.slice(0, 2).map((item, index) => (
                              <li key={index}>
                                <h3 className=" text-base font-semibold text-customBlack mb-1">
                                  {item.title}
                                </h3>
                                <p className="font-medium text-xs lg:text-sm">
                                  {item.description}
                                </p>
                              </li>
                            ))}
                          </ul>
                        </>
                      )}
                    </div>
                    {course.practicalTrainingData.length > 0 && (
                      <div className="mt-4">
                        <h3 className="font-bold text-lg text-customBlack mb-2">
                          Loan for Practical Flight Training
                        </h3>
                        <ul className="space-y-2">
                          {course.practicalTrainingData.slice(0, 2).map((item, index) => (
                            <li key={index}>
                              <h3 className=" text-base font-semibold text-customBlack mb-1">
                                {item.title}
                              </h3>
                              <p className="font-medium text-xs lg:text-sm">
                                {item.description}
                              </p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* More button - only show for specific courses */}
                    {(["indian-commercial-pilot", "american-commercial-pilot", "cpl-package-program", "helicopter-pilot-training", "american-flight-dispatcher"].includes(course.slug) &&
                      course.practicalTrainingData.length > 2) && (
                        <div className="mt-4 text-center">
                          <button
                            onClick={() => setModalType("Scholarship")}
                            className="text-textBluePrimary hover:text-blue-700 font-semibold text-sm md:text-base underline"
                          >
                            ...more
                          </button>
                        </div>
                      )}
                  </div>
                )}

              {/* Career Prospects */}
              {course.careerProspects.length > 0 && (
                <div className="bg-white shadow-lg p-6 rounded-[20px] col-span-full">
                  <h2 className="text-center md:text-left lg:text-left xl:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4">
                    Career Prospects
                  </h2>
                  <ul className="space-y-1 md:space-y-2">
                    {course.careerProspects.map((item, index) => (
                      <li key={index}>
                        <h3 className="font-semibold text-base lg:text-xl text-customBlack mb-1 md:mb-2">
                          {item.title}
                        </h3>
                        <p className="font-medium text-xs lg:text-sm">
                          {item.description}
                        </p>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

          </div>
          {/* Job Placement */}
          {
            course.jobAssistanceData &&
            <div className="bg-white shadow-courseCardShadow rounded-lg p-4 md:p-6">
              <h3 className="mb-2 md:mb-6 text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center">
                Job Placement Assistance
              </h3>
              <div className="flex mx-auto flex-col gap-4">
                {course.jobAssistanceData && course.jobAssistanceData.map((item, index) => (
                  <div className="flex flex-col gap-1" key={`job-${index}`}>
                    <h4 className="text-sm md:text-base lg:text-xl text-customBlack font-bold">
                      {item.title}
                    </h4>
                    <p className="text-xs md:text-sm lg:text-base text-customBlack font-semibold">
                      {item.desc}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          }
        </div>
      </div>
    </>
  );
};

export default CourseDetailComponent;
