module.exports = {

"[project]/src/constant/landingPageData.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// Calculate age statically - will be updated when needed
__turbopack_esm__({
    "aboutUs": (()=>aboutUs),
    "alumniData": (()=>alumniData),
    "companyImagesArray": (()=>companyImagesArray),
    "coursesData": (()=>coursesData),
    "tableData": (()=>tableData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function calculateAge() {
    const dob = new Date("1987-10-15");
    const now = new Date();
    let age = now.getFullYear() - dob.getFullYear();
    if (now.getMonth() < dob.getMonth() || now.getMonth() === dob.getMonth() && now.getDate() < dob.getDate()) {
        age--;
    }
    return age;
}
const age = calculateAge();
const aboutUs = [
    {
        title: "World-Class Training Facilities",
        desc: "At The Skyline Aviation Club, we pride ourselves on providing cutting-edge facilities that mirror real-world aviation environments. Train with state-of-the-art flight simulators, a fleet of modern training aircraft, and classrooms equipped with the latest learning technologies. Our facilities are designed to ensure you are prepared for every challenge the skies may bring.",
        image: "/assets/about3.jpg",
        width: 456,
        height: 239
    },
    {
        title: "Proven Track Records",
        desc: `Since last ${age} years, The Skyline Aviation Club has maintained an outstanding track record of graduate success rate, with alumni now flying for leading global airlines. Our hands-on training approach, experienced instructors, and tailored curriculum ensure you not only pass your certifications but also excel in competitive aviation careers.`,
        image: "/About_Us.jpg",
        width: 456,
        height: 239
    },
    {
        title: "Comprehensive Student Support",
        desc: "From the moment you enroll to the day you land your dream job, we're here for you. Our dedicated student support team helps with admissions, financial planning, and career placement, ensuring your journey is as smooth as your takeoff. With The Skyline Aviation Club, you are mentored throughout your aviation and airline career.",
        image: "/assets/about1.jpg",
        width: 456,
        height: 239
    },
    {
        title: "Globally Recognised Certifications",
        desc: "The Skyline Aviation Club offers certifications and licences that are recognised under International Civil Aviation Organization (ICAO) and respected worldwide. From Private Pilot licences (PPL) to Instrument Rating (IR) to Commercial Pilot licences (CPL) and more, our programs meet the highest international standards, opening doors to a future in aviation, no matter where you want to fly.",
        image: "/assets/about2.jpg",
        width: 456,
        height: 239
    }
];
const tableData = [
    {
        startDate: "15th April",
        endDate: "5th April"
    },
    {
        startDate: "1st May",
        endDate: "20th April"
    },
    {
        startDate: "15th June",
        endDate: "5th June"
    },
    {
        startDate: "15th August",
        endDate: "5th August"
    },
    {
        startDate: "15th October",
        endDate: "5th October"
    },
    {
        startDate: "15th December",
        endDate: "5th December"
    }
];
const coursesData = [
    {
        id: 1,
        slug: "indian-commercial-pilot-licence-course",
        title: "DGCA",
        subTitle: "Indian Commercial Pilot Licence Course",
        desc: "If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer DGCA(India) Commercial Pilot Licence Course which will prepare you for entry into all Airlines in India.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 85,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 88,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 91,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aircraft & Engine (Technical) General & Specific"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 94,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-1 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Radio Telephonic"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 84,
            columnNumber: 7
        }, this),
        image: "/cptManek2.jpg",
        bgImage: "/assets/courses/IndianCommercialPilot.jpg"
    },
    {
        id: 2,
        slug: "american-commercial-pilots-licence",
        title: "FAA USA",
        subTitle: "American Commercial Pilot Licence Course",
        desc: "If your Dream is to become an Aiplane Pilot and make that a career, this is the first step you will take in that direction. A Commercial Pilot is an Authorised, Certified and vital Crew Member for any Airline. We Offer FAA (USA) Commercial Pilot Licence Courses which will prepare you for entry into all Airlines worldwide.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 113,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 116,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 119,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 122,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 112,
            columnNumber: 7
        }, this),
        image: "/assets/courseTwoImage.png",
        bgImage: "/assets/courses/AmericanCommercialPilot.jpg"
    },
    {
        id: 3,
        slug: "aircraft-flight-dispatcher-licence-course",
        title: "FAA USA",
        subTitle: "Aircraft/Flight Dispatcher Licence Course",
        desc: "An Aircraft Dispatcher (also known as an Flight dispatcher, Airline Dispatcher or Flight Operations Officer) assists in planning flight paths, taking into account wind speed, storms, aircraft performance and loading, and other conditions. They usually work in the Operations or Disptach Control Center of the airline.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 138,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 144,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Flight Training"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 147,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 150,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 137,
            columnNumber: 7
        }, this),
        image: "/assets/dispatch.jpg",
        bgImage: "/assets/courses/flightDispatcher.jpg"
    },
    {
        id: 4,
        slug: "radio-telephony-r-aeromobile-frtol-licence",
        title: "DGCA/WPC",
        subTitle: "Radio Telephony (R) Aeromobile/FRTOL Licence Course",
        desc: "This is a professional course of international standards as per the general guidelines prescribed under international radio regulations applicable to the aeronautical mobile service.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "mb-1",
                    children: "Part 1: Practical Test in Regulation and Procedure"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 166,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "mb-1",
                    children: "Part 2: Oral Examination"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 167,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                    className: "flex flex-wrap gap-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                            children: "Regulation & Procedure"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/landingPageData.jsx",
                            lineNumber: 169,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                            children: "Radio Principles & Practice"
                        }, void 0, false, {
                            fileName: "[project]/src/constant/landingPageData.jsx",
                            lineNumber: 172,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 168,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true),
        image: "/assets/courseFourImage.png",
        bgImage: "/assets/courses/RadioTelephony.jpg"
    },
    {
        id: 5,
        slug: "commercial-pilot-licence-package-program",
        title: "Inclusive of Indian CPL + American CPL or Canadian CPL",
        subTitle: "Commercial Pilot Licence Package Program",
        desc: "The Commercial Pilot Licence Package Program is designed to provide comprehensive training for obtaining both Indian and American or Canadian Commercial Pilot Licences (CPL). This program is tailored for students who aspire to become professional pilots and seek opportunities in the global aviation industry.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 189,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 192,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 195,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical Aviation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 198,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 188,
            columnNumber: 7
        }, this),
        image: "/assets/albumImageTwo.png",
        bgImage: "/assets/courses/CPLProgram.jpg"
    },
    {
        id: 6,
        slug: "airhostess-flight-purser-training-course",
        title: "Cabin Crew Training Program",
        subTitle: "Airhostess/Flight Purser Training Course",
        desc: "Airhostess/flight purser training course will prepare you for most rewarding career in any airline either in India or aboard. If offers Big Pay, Free Travel and it is adventourus too.",
        topics: null,
        image: "/assets/courseSixImage.png",
        bgImage: "/assets/courses/airhostess.jpg"
    },
    {
        id: 7,
        slug: "airport-ground-staff-course",
        title: "Airport Ground Services",
        subTitle: "Airport Ground Staff Course",
        desc: "If your Dream is to become a member of Airport Ground Staff and make that a career, this training program will be the first step you will take in that direction.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 224,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 227,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 230,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical Aviation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 233,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 223,
            columnNumber: 7
        }, this),
        image: "/assets/groundStaffHome.jpg",
        bgImage: "/assets/courses/AirportGroundStaff.jpg"
    },
    {
        id: 8,
        slug: "aviation-foundation-course",
        title: "Summer Vacation Training Program",
        subTitle: "Aviation Foundation Course",
        desc: "This course builds a strong foundation for students aspiring to airline careers like Pilot, Dispatcher, Air Hostess, Ground Staff, AME, ATC Officer, and more.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 250,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 253,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical Aviation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 259,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 249,
            columnNumber: 7
        }, this),
        image: "/assets/foundationCourse.jpg",
        bgImage: "/assets/courses/AviationFoundation.jpg"
    },
    {
        id: 9,
        slug: "two-day-aeroplane-or-helicopter-training-workshop",
        title: "Aeroplane/Helicopter Orientation Training",
        subTitle: "Two Day Aeroplane/Helicopter Training Workshop",
        desc: "Two Day Aeroplane/Helicopter Training Workshop provides an excellent exposure to Careers in Aviation and the Airline Industry. Students will be given class room theory training on how Helicopters fly and an actual flight in a helicopter.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 275,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 278,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 281,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical Aviation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 284,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 274,
            columnNumber: 7
        }, this),
        image: "/assets/courseDetailBanner.png"
    },
    {
        id: 10,
        slug: "foreign-commercial-pilot-licence-conversion-course",
        title: "Indian Commercial Pilot Licence (DGCA)",
        subTitle: "Foreign Commercial Pilot Licence Conversion Course",
        desc: "The Foreign Pilot Licence Conversion Course (FPLCC) is a training program that enables pilots with foreign licences to meet the regulatory requirements for obtaining a local pilot licence in a specific country.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 299,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 302,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Regulation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 305,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical Aviation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 308,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 298,
            columnNumber: 7
        }, this),
        image: "/assets/courseThreeImage.png",
        bgImage: "/assets/courses/ForeignCplConversion.jpg"
    },
    {
        id: 11,
        slug: "helicopter-commercial-pilot-licence-course",
        title: "HCPL",
        subTitle: "Helicopter Commercial Pilot Licence Course",
        desc: "The Helicopter Commercial Pilot Licence (HCPL) course is designed to train aspiring pilots to operate helicopters professionally. It covers essential theory and flight training required to obtain a commercial helicopter licence and pursue a career in rotary-wing aviation.",
        topics: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
            className: "flex flex-wrap gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Helicopter Aerodynamics"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 324,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Air Navigation"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 327,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Aviation Meteorology"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 330,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Helicopter Flight Rules & Regulations"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 333,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                    className: "py-2 px-3 md:p-2 lg:py-2 lg:px-3 text-xs md:text-sm font-normal rounded-full md:text-xs text-textBluePrimary border border-textBluePrimary",
                    children: "Technical General (Helicopter)"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 336,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/constant/landingPageData.jsx",
            lineNumber: 323,
            columnNumber: 7
        }, this),
        image: "/assets/helicopterImage.webp",
        bgImage: "/assets/courses/HelicopterCourses.jpg"
    }
];
const alumniData = [
    {
        title: "Captain Monalisa Parmar",
        subTitle: "Flight Dispatcher",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“I am very happy to say that I am selected as FLIGHT DISPATCHER by SPICE JET Airline. Currently I am posted at Surat Airport. Your extra ordinary training in Flight Dispatch has really helped me in getting job. Thank you very much.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 352,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Monalisa.png",
        rating: 5.0
    },
    {
        title: "Captain Himanshu Patel",
        subTitle: "Flight Dispatcher",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“Capt. A. D. Manek Sir, It was my pleasure to be a part of The Skyline Aviation Club, Mumbai. It played a role of a stepping stone for me to be a part of Aviation Industry today. I am glad that we have such clubs in India that encourages aviation enthusiasts to rise & fly high in their professions. An initiative to begin with something new & efficient is where you begin you way to Skyline. I am pleased to be the part of your team Capt. A. D. Manek. I wish a very good luck to the future generation of trainees at The Skyline Aviation Club.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 368,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Himanshu.png",
        rating: 5.0
    },
    {
        title: "Captain Mukarram Electricwala",
        subTitle: "Flight Dispatcher",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“I am grateful to Sir and The Skyline Aviation Club for giving me the wonderful opportunity to teach at Skyline under Manek sirs guidance. Lastly I would like to thank Manek Sir, The Skyline Aviation Club and its support staff for just being there whenever I required help I wish Sir and The Skyline Aviation Club all the happiness and best of luck for the future!”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 388,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Mukarram.png",
        rating: 5.0
    },
    {
        title: "Captain Jignesh Devadhia",
        subTitle: " F.A.A. Commercial Pilot",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“I have completed my training and qualified as F.A.A. Commercial Pilot and I am about to get my Indian Commercial Pilot Licence. With your prefect training I was able to complete my Flight Training and passing written exams in shortest possible time and hence I was able to break record of my Flight School. Thank you very much for all.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 406,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Jignesh.png",
        rating: 5.0
    },
    {
        title: "Captain Niraj Patel",
        subTitle: "First Indian Women Helicopter Pilot",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“I have completed my training and qualified as F.A.A. Commercial Pilot and I am about to get my Indian Commercial Pilot Licence. With your prefect training I was able to complete my Flight Training and passing written exams in shortest possible time and hence I was able to break record of my Flight School. Thank you very much for all.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 423,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Niraj.png",
        rating: 5.0
    },
    {
        title: "Captain Aruna Kandarpa",
        subTitle: "First Indian Women Helicopter Pilot",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                    children: "“I have completed my training and qualified as F.A.A. Commercial Pilot and I am about to get my Indian Commercial Pilot Licence. With your prefect training I was able to complete my Flight Training and passing written exams in shortest possible time and hence I was able to break record of my Flight School. Thank you very much for all.”"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 440,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                    children: "“The club and it's faculty have always been dear to my heart and I congratulate them on their 20th year of success and achievement.”"
                }, void 0, false, {
                    fileName: "[project]/src/constant/landingPageData.jsx",
                    lineNumber: 447,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true),
        image: "/assets/alumni/Aruna.png",
        rating: 5.0
    },
    {
        title: "Captain Kritika Parmar",
        subTitle: "First Indian Women Helicopter Pilot",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“I am proud to be student of The Skyline Aviation Club. With right information and guidance and training I am able to complete 130 hrs of flight training at Fresno, CA, U.S.A. I am really thankful to Capt. A. D. Manek for personally guiding me to obtain education loan of Rs.20,00,000/- from Government of Gujarat.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 461,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Kritika.png",
        rating: 5.0
    },
    {
        title: "Captain Bradley Verughese Matthew",
        subTitle: "FAA Flight Dispatcher Licence",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“Respected Sir, I am glad to tell you that my studies and trip to USA was a success with your guidance and inspiration ! As you know I have received my FAA Flight Dispatcher Licence. So right now I am hungry to get into job and prove and serve myself for Aviation”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 478,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Bradley.png",
        rating: 5.0
    },
    {
        title: "Captain Mervyn Mascarenhas",
        subTitle: "FAA Aire Line Transport Pilot",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“I began studying at The Skyline Aviation Club detailed way of instructions and the ease with which he explained my quries is remarkable. His extensive knowledge of the FAA instructions and examinations is admirable. A great passion for what he do is visible all around and his teaching standards are excellent. I am now confident I will do very well in my FAA Aire Line Transport Pilot Licence exams”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 494,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Mervyn.png",
        rating: 5.0
    },
    {
        title: "Mihir Mistry",
        subTitle: "Flight Dispatcher",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“Dear Capt.A.D.Manek, It's great to be a part of The Skyline Aviation Club family and would specially like to thank you for being a father figure and blend my life professionally. My success is only because of the hardest efforts put in by you.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 513,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Mihir.png",
        rating: 5.0
    },
    {
        title: "Captain Sagar K Karnik",
        subTitle: "Flight Dispatcher",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“Dear Capt.A.D.Manek, It's great to be a part of The Skyline Aviation Club family and would specially like to thank you for being a father figure and blend my life professionally. My success is only because of the hardest efforts put in by you.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 529,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Sagar.png",
        rating: 5.0
    },
    {
        title: "Neelam Patel",
        subTitle: "Flight Management",
        desc: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "font-normal text-sm text-customBlack text-sm italic mb-3",
                children: "“I, Miss Neelam M.Patel, after graduation joined The Skyline Aviation Club for In Flight Management Course, and completed the same within the stipulated time. During the tenure of the course, I received precious informative global knowledge, instruction resulting in my selection as an Air Hostess of Air India Ltd. I would like o thank Capt.A.D.Manek and all the instructors for their co-operation and valuable informative knowledge passed on to me for which I am grateful to The Skyline Aviation Club.”"
            }, void 0, false, {
                fileName: "[project]/src/constant/landingPageData.jsx",
                lineNumber: 545,
                columnNumber: 9
            }, this)
        }, void 0, false),
        image: "/assets/alumni/Neelam.png",
        rating: 5.0
    }
];
const companyImagesArray = [
    "/assets/air_asia_logo.png",
    "/assets/Air_India.svg",
    "/assets/spicejet-logo.png",
    "/assets/indigo.png",
    "/assets/qatar.png",
    "/assets/emirates.png"
];
;
}}),
"[project]/src/components/CourseDetailComponent.jsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$courseDetailData$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/constant/courseDetailData.jsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$landingPageData$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/constant/landingPageData.jsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
const CourseDetailComponent = ()=>{
    const [modalType, setModalType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const modalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [currentCourse, setCurrentCourse] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const { slug } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useParams"])();
    const course = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$courseDetailData$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["courseData"].find((course)=>course.slug === slug);
    if (!course) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            children: "Course Not Found"
        }, void 0, false, {
            fileName: "[project]/src/components/CourseDetailComponent.jsx",
            lineNumber: 20,
            columnNumber: 12
        }, this);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (slug) {
            const course = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constant$2f$landingPageData$2e$jsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["coursesData"].find((course)=>course.slug === slug);
            setCurrentCourse(course);
            console.log(course);
            console.log(course.image);
        }
    }, [
        slug
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        function handleClickOutside(event) {
            if (modalRef.current && !modalRef.current.contains(event.target)) {
                setModalType(false);
            }
        }
        if (modalType) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [
        modalType
    ]);
    // console.log(course);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    backgroundImage: currentCourse ? `url(${currentCourse?.bgImage})` : 'none'
                },
                className: "relative bg-cover bg-no-repeat  bg-center h-[500px] md:h-[700px]",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]"
                    }, void 0, false, {
                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                        lineNumber: 59,
                        columnNumber: 9
                    }, this),
                    course ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute left-0 right-0 bottom-28 md:bottom-32 mx-4 md:mx-20 text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "font-extrabold text-whiteOne text-2xl md:text-6xl lg:text-6xl xl:text-6xl mb-4",
                                children: course.courseDesc.toUpperCase()
                            }, void 0, false, {
                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                lineNumber: 62,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-formBG font-bold md:font-light text-xl md:text-3xl lg:text-2xl lg:w-[32rem] lg:mx-auto",
                                children: course.courseName.toUpperCase()
                            }, void 0, false, {
                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                lineNumber: 65,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                        lineNumber: 61,
                        columnNumber: 11
                    }, this) : ""
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                lineNumber: 56,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-0 bg-white",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "hidden lg:block z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute top-80 left-0 right-0 w-full h-[300px]"
                    }, void 0, false, {
                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "hidden lg:block absolute bg-dotsImage top-36 right-0 bg-center bg-no-repeat w-10 h-36"
                    }, void 0, false, {
                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-0 lg:bottom-60 left-0 right-0 w-full h-[300px]"
                    }, void 0, false, {
                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "hidden lg:block absolute bg-dotsImage top-[48rem] left-0 bg-center bg-no-repeat w-10 h-36"
                    }, void 0, false, {
                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                        lineNumber: 77,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "py-4 px-4 lg:px-12 xl:px-32 relative z-10",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute left-0 right-0 -top-20 mx-4 text-center p-3 md:p-4 lg:p-8 lg:mx-36 xl:p-8 rounded-xl shadow-courseCardShadow bg-white",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-textBluePrimary mb-2 font-bold text-xl md:text-2xl xl:text-3xl lg:text-3xl",
                                        children: "Introduction"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 80,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "descLightBlack text-xs leading-5   lg:text-base xl:text-base font-medium lg:mx-32",
                                        children: course.introduction
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 83,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                lineNumber: 79,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "shadow-cardButtonShadow lg:mt-32 xl:mt-44 md:mt-32 mt-24 p-4 md:shadow-none mb-8",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-textBluePrimary text-center mb-4 md:mb-6 font-bold text-xl md:text-2xl lg:text-3xl",
                                        children: "Eligibility"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 89,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `grid gap-2 md:gap-4 lg:gap-6 ${course.eligibilityData.length === 3 ? 'grid-cols-1 sm:grid-cols-3' : 'grid-cols-2 md:grid-cols-4'} w-full`,
                                        children: [
                                            course.eligibilityData.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex flex-col items-center p-2 md:p-4 rounded-xl shadow-xl bg-white",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            src: item.image,
                                                            alt: `${item.title} Icon`,
                                                            className: `${item.class} mb-2 md:lg-4`,
                                                            width: 100,
                                                            height: 100
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                            lineNumber: 104,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "font-semibold text-center text-sm md:text-base text-neutralGrayText",
                                                            children: item.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                            lineNumber: 111,
                                                            columnNumber: 19
                                                        }, this),
                                                        item.title === 'Education' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-center text-sm md:text-base text-neutralGrayText",
                                                                    children: item.desc
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 116,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: ()=>setModalType("Education"),
                                                                    className: "mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm",
                                                                    children: "Note"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 119,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true) : item.title === 'Medical' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "text-center text-sm md:text-base text-neutralGrayText",
                                                                    children: item.desc
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 128,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: ()=>setModalType("Medical"),
                                                                    className: "mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm",
                                                                    children: "Note"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 131,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-center text-sm md:text-base text-neutralGrayText",
                                                            children: item.desc
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                            lineNumber: 139,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, index, true, {
                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                    lineNumber: 100,
                                                    columnNumber: 17
                                                }, this)),
                                            modalType && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "fixed inset-0 flex items-center justify-center z-50 px-4 md:px-6",
                                                style: {
                                                    background: "rgba(0,0,0,0.3)"
                                                },
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    ref: modalRef,
                                                    className: `bg-white rounded-xl p-4 md:p-6 shadow-lg relative ${modalType === "Scholarship" ? "max-w-4xl w-full max-h-[90vh] overflow-y-auto" : "max-w-md"}`,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>setModalType(null),
                                                            className: "absolute right-4 top-4 text-gray-500 hover:text-gray-700 text-xl font-bold z-10",
                                                            children: "×"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                            lineNumber: 154,
                                                            columnNumber: 21
                                                        }, this),
                                                        modalType === "Scholarship" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                    className: "text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary mb-4 md:mb-6 text-center md:text-left",
                                                                    children: "Scholarship Details"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 163,
                                                                    columnNumber: 25
                                                                }, this),
                                                                course.theoryTrainingData.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "mb-6",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                            className: "font-bold text-lg md:text-xl text-customBlack mb-3",
                                                                            children: "Scholarships for Theory Training"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 169,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                            className: "space-y-3",
                                                                            children: course.theoryTrainingData.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    className: "border-b border-gray-200 pb-3 last:border-b-0",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                                            className: "text-base md:text-lg font-semibold text-customBlack mb-2",
                                                                                            children: item.title
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                            lineNumber: 175,
                                                                                            columnNumber: 35
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                            className: "font-medium text-sm md:text-base text-gray-700",
                                                                                            children: item.description
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                            lineNumber: 178,
                                                                                            columnNumber: 35
                                                                                        }, this)
                                                                                    ]
                                                                                }, index, true, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 174,
                                                                                    columnNumber: 33
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 172,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 168,
                                                                    columnNumber: 27
                                                                }, this),
                                                                course.practicalTrainingData.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                            className: "font-bold text-lg md:text-xl text-customBlack mb-3",
                                                                            children: "Scholarships for Practical Flight Training"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 189,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                            className: "space-y-3",
                                                                            children: course.practicalTrainingData.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    className: "border-b border-gray-200 pb-3 last:border-b-0",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                                            className: "text-base md:text-lg font-semibold text-customBlack mb-2",
                                                                                            children: item.title
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                            lineNumber: 195,
                                                                                            columnNumber: 35
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                            className: "font-medium text-sm md:text-base text-gray-700",
                                                                                            children: item.description
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                            lineNumber: 198,
                                                                                            columnNumber: 35
                                                                                        }, this)
                                                                                    ]
                                                                                }, index, true, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 194,
                                                                                    columnNumber: 33
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 192,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 188,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                            lineNumber: 162,
                                                            columnNumber: 23
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                                    className: "text-lg font-semibold mb-2",
                                                                    children: "Important Note"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 209,
                                                                    columnNumber: 25
                                                                }, this),
                                                                modalType === "Education" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-sm text-gray-700",
                                                                    children: "If not from science background, then you require to appear and pass Physics and Mathematics subjects of 12th Level conducted by NIOS, New Delhi. Students can pursue our training program and simultaneously appear for above mentioned exams. Please contact us for further information."
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 211,
                                                                    columnNumber: 27
                                                                }, this),
                                                                modalType === "Medical" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    className: "text-sm text-gray-700",
                                                                    children: "We Will arrange Medical Examination with our panel doctor who is DGCA approved Class II Medical Examiner"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 221,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                    lineNumber: 147,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 146,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 92,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                lineNumber: 88,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-4 mb-8 flex-col md:flex-row",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-white shadow-courseCardShadow p-4 md:p-6 rounded-[20px] md:basis-2/5",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                className: "text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center md:text-left lg:text-left xl:text-left font-bold text-textBluePrimary mb-4",
                                                children: "Course Structure"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 235,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: "space-y-4 md:pl-3 md:marker:text-2xl marker:font-bold md:list-decimal",
                                                children: course?.courseStructure && course.courseStructure.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: "md:ml-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "font-bold text-base lg:text-2xl text-customBlack mb-2",
                                                                children: item.title
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                lineNumber: 241,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "font-bold text-base lg:text-xl text-customBlack flex  gap-1 mb-2",
                                                                children: [
                                                                    "Course Duration:",
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "font-medium text-base md:text-xl",
                                                                        children: item.courseDuration
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                        lineNumber: 246,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                lineNumber: 244,
                                                                columnNumber: 21
                                                            }, this),
                                                            item.parts.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "font-normal text-baxe lg:text-lg text-customBlack flex flex-col gap-1 mb-2",
                                                                            children: [
                                                                                item.title,
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "font-medium text-base md:text-lg",
                                                                                    children: item.duration
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 257,
                                                                                    columnNumber: 27
                                                                                }, this)
                                                                            ]
                                                                        }, index, true, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 252,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        item.papers && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                            className: "space-y-1",
                                                                            children: item.papers.map((paper, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    className: "font-small text-base md:text-lg",
                                                                                    children: paper
                                                                                }, idx, false, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 264,
                                                                                    columnNumber: 31
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 262,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true)),
                                                            course.subCourseStructure && course.subCourseStructure.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                            className: "font-bold text-base lg:text-xl text-customBlack flex flex-col gap-1 mb-2",
                                                                            children: item.title
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 278,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        item.subjects && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                            className: "space-y-1",
                                                                            children: item.subjects.map((subject, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    className: "font-medium text-base md:text-lg",
                                                                                    children: subject
                                                                                }, idx, false, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 284,
                                                                                    columnNumber: 33
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 282,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        item.licence && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                            className: "font-bold text-base lg:text-lg text-customBlack flex flex-col gap-1 mb-2",
                                                                            children: item.licence
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 294,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        item.parts && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                            className: "space-y-1",
                                                                            children: item.parts.map((part, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                    className: "font-medium text-base md:text-lg",
                                                                                    children: part
                                                                                }, idx, false, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 301,
                                                                                    columnNumber: 33
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 299,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, index, true, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 277,
                                                                    columnNumber: 25
                                                                }, this))
                                                        ]
                                                    }, `course-${index}`, true, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 240,
                                                        columnNumber: 19
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 238,
                                                columnNumber: 15
                                            }, this),
                                            course.visaAsistant && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "font-bold text-base text-customBlack mb-2",
                                                        children: "Student Visa Assistant"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 318,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "mt-4 font-medium text-md text-customBlack mb-4",
                                                        children: course.visaAsistant
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 321,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true),
                                            course.shortNote && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "mt-4 font-medium text-md text-customBlack mb-4",
                                                children: course.shortNote
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 327,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/enquire",
                                                className: "block text-center bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 mt-3 lg:text-lg rounded-full border border-transparent",
                                                children: "Enquire Now"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 338,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 234,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-col gap-6 md:basis-3/5",
                                        children: [
                                            (course.theoryTrainingData.length || course.practicalTrainingData.length) > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white shadow-lg p-6 rounded-[20px]",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                        className: "text-center md:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4",
                                                        children: "Scholarship"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 353,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: course.theoryTrainingData.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                    className: "font-bold text-lg text-customBlack mb-2",
                                                                    children: "Scholarships for Theory Training"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 359,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                    className: "space-y-2",
                                                                    children: course.theoryTrainingData.slice(0, 2).map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                                    className: " text-base font-semibold text-customBlack mb-1",
                                                                                    children: item.title
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 365,
                                                                                    columnNumber: 33
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                    className: "font-medium text-xs lg:text-sm",
                                                                                    children: item.description
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                    lineNumber: 368,
                                                                                    columnNumber: 33
                                                                                }, this)
                                                                            ]
                                                                        }, index, true, {
                                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                            lineNumber: 364,
                                                                            columnNumber: 31
                                                                        }, this))
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                    lineNumber: 362,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 356,
                                                        columnNumber: 21
                                                    }, this),
                                                    course.practicalTrainingData.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mt-4",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "font-bold text-lg text-customBlack mb-2",
                                                                children: "Scholarships for Practical Flight Training"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                lineNumber: 379,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                className: "space-y-2",
                                                                children: course.practicalTrainingData.slice(0, 2).map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                                className: " text-base font-semibold text-customBlack mb-1",
                                                                                children: item.title
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                lineNumber: 385,
                                                                                columnNumber: 31
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "font-medium text-xs lg:text-sm",
                                                                                children: item.description
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                                lineNumber: 388,
                                                                                columnNumber: 31
                                                                            }, this)
                                                                        ]
                                                                    }, index, true, {
                                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                        lineNumber: 384,
                                                                        columnNumber: 29
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                lineNumber: 382,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 378,
                                                        columnNumber: 23
                                                    }, this),
                                                    (course.theoryTrainingData.length > 2 || course.practicalTrainingData.length > 2) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mt-4 text-center",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>setModalType("Scholarship"),
                                                            className: "text-textBluePrimary hover:text-blue-700 font-semibold text-sm md:text-base underline",
                                                            children: "...more"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                            lineNumber: 400,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 399,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 352,
                                                columnNumber: 19
                                            }, this),
                                            course.careerProspects.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-white shadow-lg p-6 rounded-[20px] col-span-full",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                        className: "text-center md:text-left lg:text-left xl:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4",
                                                        children: "Career Prospects"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 414,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                        className: "space-y-1 md:space-y-2",
                                                        children: course.careerProspects.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                        className: "font-semibold text-base lg:text-xl text-customBlack mb-1 md:mb-2",
                                                                        children: item.title
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                        lineNumber: 420,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "font-medium text-xs lg:text-sm",
                                                                        children: item.description
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                        lineNumber: 423,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, index, true, {
                                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                                lineNumber: 419,
                                                                columnNumber: 23
                                                            }, this))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 417,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 413,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 349,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                lineNumber: 233,
                                columnNumber: 11
                            }, this),
                            course.jobAssistanceData && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white shadow-courseCardShadow rounded-lg p-4 md:p-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "mb-2 md:mb-6 text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center",
                                        children: "Job Placement Assistance"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 438,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex mx-auto flex-col gap-4",
                                        children: course.jobAssistanceData && course.jobAssistanceData.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex flex-col gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "text-sm md:text-base lg:text-xl text-customBlack font-bold",
                                                        children: item.title
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 444,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-xs md:text-sm lg:text-base text-customBlack font-semibold",
                                                        children: item.desc
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                        lineNumber: 447,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, `job-${index}`, true, {
                                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                                lineNumber: 443,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                        lineNumber: 441,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                                lineNumber: 437,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/CourseDetailComponent.jsx",
                        lineNumber: 78,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/CourseDetailComponent.jsx",
                lineNumber: 73,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = CourseDetailComponent;
}}),
"[project]/src/app/course/[slug]/page.jsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=src_39dcc3._.js.map