{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n  :root {\r\n    --background: 0 0% 100%;\r\n    --foreground: 0 0% 3.9%;\r\n    --card: 0 0% 100%;\r\n    --card-foreground: 0 0% 3.9%;\r\n    --popover: 0 0% 100%;\r\n    --popover-foreground: 0 0% 3.9%;\r\n    --primary: 0 0% 9%;\r\n    --primary-foreground: 0 0% 98%;\r\n    --secondary: 0 0% 96.1%;\r\n    --secondary-foreground: 0 0% 9%;\r\n    --muted: 0 0% 96.1%;\r\n    --muted-foreground: 0 0% 45.1%;\r\n    --accent: 0 0% 96.1%;\r\n    --accent-foreground: 0 0% 9%;\r\n    --destructive: 0 84.2% 60.2%;\r\n    --destructive-foreground: 0 0% 98%;\r\n    --border: 0 0% 89.8%;\r\n    --input: 0 0% 89.8%;\r\n    --ring: 0 0% 3.9%;\r\n    --chart-1: 12 76% 61%;\r\n    --chart-2: 173 58% 39%;\r\n    --chart-3: 197 37% 24%;\r\n    --chart-4: 43 74% 66%;\r\n    --chart-5: 27 87% 67%;\r\n    --radius: 0.5rem;\r\n    --whiteOne: #FCFCF4;\r\n    --textBluePrimary: #1420CD;\r\n    --customGrey100: #7D8175;\r\n    --customBlack: #222222;\r\n    --descLightBlack: #080E5E;\r\n    --neutralGrayText: #212529;\r\n    --textGreyPrimary: #464C3B;\r\n    --boxShadow: 0px 8px 30px 0px #00000014;\r\n    --footerBG: #F3F5FF;\r\n    --footerPrimaryText: #0E1795;\r\n    --blue900: #0C1483;\r\n    --fullStarBG: #F2C94C;\r\n    --halfStarBG: #F2C94C80;\r\n    --emptyStarBG: #B2B2B2;\r\n    --whyChoosePara: #323232;\r\n    --arrowIconColor: #1865C1;\r\n    --formTextColor: #3D4852;\r\n    --formTextOptionColor: #808080;\r\n    --formBG: #F5F5F5;\r\n    --formInputColor: #ACB1B6;\r\n    --formLabelSubColor: #A81F00;\r\n    --carouselDotsBG: #E0E1DE;\r\n    --accordionBG: #D6E6FF70;\r\n    --footerBottomText: #9d9d9d;\r\n    --black: #000000;\r\n    --formPara: #616557;\r\n  }\r\n  * {\n  border-color: hsl(var(--border));\n}\r\n\r\n  body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n}\r\n.container {\n  width: 100%;\n}\r\n@media (min-width: 320px) {\n\n  .container {\n    max-width: 320px;\n  }\n}\r\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\r\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\r\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\r\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\r\n.pointer-events-none {\n  pointer-events: none;\n}\r\n.pointer-events-auto {\n  pointer-events: auto;\n}\r\n.visible {\n  visibility: visible;\n}\r\n.fixed {\n  position: fixed;\n}\r\n.absolute {\n  position: absolute;\n}\r\n.relative {\n  position: relative;\n}\r\n.inset-0 {\n  inset: 0px;\n}\r\n.-bottom-12 {\n  bottom: -3rem;\n}\r\n.-left-12 {\n  left: -3rem;\n}\r\n.-left-4 {\n  left: -1rem;\n}\r\n.-right-12 {\n  right: -3rem;\n}\r\n.-right-4 {\n  right: -1rem;\n}\r\n.-top-12 {\n  top: -3rem;\n}\r\n.-top-20 {\n  top: -5rem;\n}\r\n.bottom-0 {\n  bottom: 0px;\n}\r\n.bottom-10 {\n  bottom: 2.5rem;\n}\r\n.bottom-12 {\n  bottom: 3rem;\n}\r\n.bottom-24 {\n  bottom: 6rem;\n}\r\n.bottom-28 {\n  bottom: 7rem;\n}\r\n.bottom-4 {\n  bottom: 1rem;\n}\r\n.bottom-8 {\n  bottom: 2rem;\n}\r\n.bottom-\\[20px\\] {\n  bottom: 20px;\n}\r\n.bottom-\\[34rem\\] {\n  bottom: 34rem;\n}\r\n.bottom-\\[500px\\] {\n  bottom: 500px;\n}\r\n.bottom-\\[700px\\] {\n  bottom: 700px;\n}\r\n.left-0 {\n  left: 0px;\n}\r\n.left-1\\/2 {\n  left: 50%;\n}\r\n.left-2 {\n  left: 0.5rem;\n}\r\n.left-\\[50\\%\\] {\n  left: 50%;\n}\r\n.right-0 {\n  right: 0px;\n}\r\n.right-1 {\n  right: 0.25rem;\n}\r\n.right-2 {\n  right: 0.5rem;\n}\r\n.right-3 {\n  right: 0.75rem;\n}\r\n.right-4 {\n  right: 1rem;\n}\r\n.top-0 {\n  top: 0px;\n}\r\n.top-1 {\n  top: 0.25rem;\n}\r\n.top-1\\/2 {\n  top: 50%;\n}\r\n.top-2 {\n  top: 0.5rem;\n}\r\n.top-36 {\n  top: 9rem;\n}\r\n.top-4 {\n  top: 1rem;\n}\r\n.top-40 {\n  top: 10rem;\n}\r\n.top-60 {\n  top: 15rem;\n}\r\n.top-8 {\n  top: 2rem;\n}\r\n.top-80 {\n  top: 20rem;\n}\r\n.top-\\[-5rem\\] {\n  top: -5rem;\n}\r\n.top-\\[1px\\] {\n  top: 1px;\n}\r\n.top-\\[284px\\] {\n  top: 284px;\n}\r\n.top-\\[4\\.5rem\\] {\n  top: 4.5rem;\n}\r\n.top-\\[48rem\\] {\n  top: 48rem;\n}\r\n.top-\\[50\\%\\] {\n  top: 50%;\n}\r\n.top-\\[60\\%\\] {\n  top: 60%;\n}\r\n.top-\\[86\\%\\] {\n  top: 86%;\n}\r\n.top-full {\n  top: 100%;\n}\r\n.z-0 {\n  z-index: 0;\n}\r\n.z-10 {\n  z-index: 10;\n}\r\n.z-20 {\n  z-index: 20;\n}\r\n.z-50 {\n  z-index: 50;\n}\r\n.z-\\[-1\\] {\n  z-index: -1;\n}\r\n.z-\\[100\\] {\n  z-index: 100;\n}\r\n.z-\\[1\\] {\n  z-index: 1;\n}\r\n.col-span-full {\n  grid-column: 1 / -1;\n}\r\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\r\n.mx-1 {\n  margin-left: 0.25rem;\n  margin-right: 0.25rem;\n}\r\n.mx-4 {\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\r\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\r\n.my-4 {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\r\n.mx-10 {\n  margin-left: 2.5rem;\n  margin-right: 2.5rem;\n}\r\n.mx-5 {\n  margin-left: 1.25rem;\n  margin-right: 1.25rem;\n}\r\n.-ml-4 {\n  margin-left: -1rem;\n}\r\n.-mt-4 {\n  margin-top: -1rem;\n}\r\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\r\n.mb-1\\.5 {\n  margin-bottom: 0.375rem;\n}\r\n.mb-10 {\n  margin-bottom: 2.5rem;\n}\r\n.mb-12 {\n  margin-bottom: 3rem;\n}\r\n.mb-16 {\n  margin-bottom: 4rem;\n}\r\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\r\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\r\n.mb-4 {\n  margin-bottom: 1rem;\n}\r\n.mb-5 {\n  margin-bottom: 1.25rem;\n}\r\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\r\n.mb-7 {\n  margin-bottom: 1.75rem;\n}\r\n.mb-8 {\n  margin-bottom: 2rem;\n}\r\n.ml-1 {\n  margin-left: 0.25rem;\n}\r\n.ml-2 {\n  margin-left: 0.5rem;\n}\r\n.ml-4 {\n  margin-left: 1rem;\n}\r\n.ml-auto {\n  margin-left: auto;\n}\r\n.mr-4 {\n  margin-right: 1rem;\n}\r\n.mt-1 {\n  margin-top: 0.25rem;\n}\r\n.mt-1\\.5 {\n  margin-top: 0.375rem;\n}\r\n.mt-10 {\n  margin-top: 2.5rem;\n}\r\n.mt-2 {\n  margin-top: 0.5rem;\n}\r\n.mt-24 {\n  margin-top: 6rem;\n}\r\n.mt-28 {\n  margin-top: 7rem;\n}\r\n.mt-3 {\n  margin-top: 0.75rem;\n}\r\n.mt-4 {\n  margin-top: 1rem;\n}\r\n.mt-5 {\n  margin-top: 1.25rem;\n}\r\n.mt-6 {\n  margin-top: 1.5rem;\n}\r\n.mt-7 {\n  margin-top: 1.75rem;\n}\r\n.mt-8 {\n  margin-top: 2rem;\n}\r\n.mt-20 {\n  margin-top: 5rem;\n}\r\n.mt-32 {\n  margin-top: 8rem;\n}\r\n.line-clamp-4 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 4;\n}\r\n.block {\n  display: block;\n}\r\n.flex {\n  display: flex;\n}\r\n.inline-flex {\n  display: inline-flex;\n}\r\n.table {\n  display: table;\n}\r\n.grid {\n  display: grid;\n}\r\n.hidden {\n  display: none;\n}\r\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\r\n.aspect-video {\n  aspect-ratio: 16 / 9;\n}\r\n.\\!h-\\[90\\%\\] {\n  height: 90% !important;\n}\r\n.h-1 {\n  height: 0.25rem;\n}\r\n.h-1\\.5 {\n  height: 0.375rem;\n}\r\n.h-10 {\n  height: 2.5rem;\n}\r\n.h-12 {\n  height: 3rem;\n}\r\n.h-2 {\n  height: 0.5rem;\n}\r\n.h-24 {\n  height: 6rem;\n}\r\n.h-3 {\n  height: 0.75rem;\n}\r\n.h-3\\.5 {\n  height: 0.875rem;\n}\r\n.h-36 {\n  height: 9rem;\n}\r\n.h-4 {\n  height: 1rem;\n}\r\n.h-5 {\n  height: 1.25rem;\n}\r\n.h-6 {\n  height: 1.5rem;\n}\r\n.h-64 {\n  height: 16rem;\n}\r\n.h-7 {\n  height: 1.75rem;\n}\r\n.h-8 {\n  height: 2rem;\n}\r\n.h-9 {\n  height: 2.25rem;\n}\r\n.h-\\[139px\\] {\n  height: 139px;\n}\r\n.h-\\[200px\\] {\n  height: 200px;\n}\r\n.h-\\[230px\\] {\n  height: 230px;\n}\r\n.h-\\[300px\\] {\n  height: 300px;\n}\r\n.h-\\[330px\\] {\n  height: 330px;\n}\r\n.h-\\[411px\\] {\n  height: 411px;\n}\r\n.h-\\[500px\\] {\n  height: 500px;\n}\r\n.h-\\[573px\\] {\n  height: 573px;\n}\r\n.h-\\[60px\\] {\n  height: 60px;\n}\r\n.h-\\[653px\\] {\n  height: 653px;\n}\r\n.h-\\[700px\\] {\n  height: 700px;\n}\r\n.h-\\[740px\\] {\n  height: 740px;\n}\r\n.h-\\[750px\\] {\n  height: 750px;\n}\r\n.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {\n  height: var(--radix-navigation-menu-viewport-height);\n}\r\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\r\n.h-auto {\n  height: auto;\n}\r\n.h-fit {\n  height: fit-content;\n}\r\n.h-full {\n  height: 100%;\n}\r\n.h-px {\n  height: 1px;\n}\r\n.h-20 {\n  height: 5rem;\n}\r\n.h-\\[339px\\] {\n  height: 339px;\n}\r\n.h-\\[239px\\] {\n  height: 239px;\n}\r\n.h-\\[23px\\] {\n  height: 23px;\n}\r\n.h-\\[2300px\\] {\n  height: 2300px;\n}\r\n.max-h-0 {\n  max-height: 0px;\n}\r\n.max-h-96 {\n  max-height: 24rem;\n}\r\n.max-h-\\[600px\\] {\n  max-height: 600px;\n}\r\n.max-h-\\[60vh\\] {\n  max-height: 60vh;\n}\r\n.max-h-\\[90vh\\] {\n  max-height: 90vh;\n}\r\n.max-h-screen {\n  max-height: 100vh;\n}\r\n.min-h-\\[100vh\\] {\n  min-height: 100vh;\n}\r\n.min-h-\\[110vh\\] {\n  min-height: 110vh;\n}\r\n.min-h-\\[470px\\] {\n  min-height: 470px;\n}\r\n.min-h-screen {\n  min-height: 100vh;\n}\r\n.min-h-\\[570px\\] {\n  min-height: 570px;\n}\r\n.min-h-\\[50px\\] {\n  min-height: 50px;\n}\r\n.min-h-\\[550px\\] {\n  min-height: 550px;\n}\r\n.min-h-\\[500px\\] {\n  min-height: 500px;\n}\r\n.min-h-\\[530px\\] {\n  min-height: 530px;\n}\r\n.min-h-\\[00px\\] {\n  min-height: 00px;\n}\r\n.min-h-\\[478px\\] {\n  min-height: 478px;\n}\r\n.min-h-\\[47px\\] {\n  min-height: 47px;\n}\r\n.min-h-\\[\\] {\n  min-height: ;\n}\r\n.min-h-\\[475px\\] {\n  min-height: 475px;\n}\r\n.w-10 {\n  width: 2.5rem;\n}\r\n.w-12 {\n  width: 3rem;\n}\r\n.w-2 {\n  width: 0.5rem;\n}\r\n.w-24 {\n  width: 6rem;\n}\r\n.w-3 {\n  width: 0.75rem;\n}\r\n.w-3\\.5 {\n  width: 0.875rem;\n}\r\n.w-3\\/5 {\n  width: 60%;\n}\r\n.w-4 {\n  width: 1rem;\n}\r\n.w-5 {\n  width: 1.25rem;\n}\r\n.w-56 {\n  width: 14rem;\n}\r\n.w-6 {\n  width: 1.5rem;\n}\r\n.w-64 {\n  width: 16rem;\n}\r\n.w-7 {\n  width: 1.75rem;\n}\r\n.w-8 {\n  width: 2rem;\n}\r\n.w-9 {\n  width: 2.25rem;\n}\r\n.w-\\[1500px\\] {\n  width: 1500px;\n}\r\n.w-\\[150px\\] {\n  width: 150px;\n}\r\n.w-\\[160px\\] {\n  width: 160px;\n}\r\n.w-\\[180px\\] {\n  width: 180px;\n}\r\n.w-\\[190px\\] {\n  width: 190px;\n}\r\n.w-\\[19rem\\] {\n  width: 19rem;\n}\r\n.w-\\[356px\\] {\n  width: 356px;\n}\r\n.w-\\[60px\\] {\n  width: 60px;\n}\r\n.w-\\[815px\\] {\n  width: 815px;\n}\r\n.w-\\[90\\%\\] {\n  width: 90%;\n}\r\n.w-auto {\n  width: auto;\n}\r\n.w-fit {\n  width: fit-content;\n}\r\n.w-full {\n  width: 100%;\n}\r\n.w-max {\n  width: max-content;\n}\r\n.w-\\[170px\\] {\n  width: 170px;\n}\r\n.min-w-0 {\n  min-width: 0px;\n}\r\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\r\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\r\n.\\!max-w-\\[400px\\] {\n  max-width: 400px !important;\n}\r\n.max-w-3xl {\n  max-width: 48rem;\n}\r\n.max-w-4xl {\n  max-width: 56rem;\n}\r\n.max-w-5xl {\n  max-width: 64rem;\n}\r\n.max-w-6xl {\n  max-width: 72rem;\n}\r\n.max-w-7xl {\n  max-width: 80rem;\n}\r\n.max-w-\\[1000px\\] {\n  max-width: 1000px;\n}\r\n.max-w-\\[250px\\] {\n  max-width: 250px;\n}\r\n.max-w-\\[340px\\] {\n  max-width: 340px;\n}\r\n.max-w-\\[400px\\] {\n  max-width: 400px;\n}\r\n.max-w-\\[52rem\\] {\n  max-width: 52rem;\n}\r\n.max-w-\\[80\\%\\] {\n  max-width: 80%;\n}\r\n.max-w-full {\n  max-width: 100%;\n}\r\n.max-w-lg {\n  max-width: 32rem;\n}\r\n.max-w-max {\n  max-width: max-content;\n}\r\n.max-w-md {\n  max-width: 28rem;\n}\r\n.max-w-sm {\n  max-width: 24rem;\n}\r\n.flex-1 {\n  flex: 1 1 0%;\n}\r\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\r\n.shrink-0 {\n  flex-shrink: 0;\n}\r\n.flex-grow {\n  flex-grow: 1;\n}\r\n.grow-0 {\n  flex-grow: 0;\n}\r\n.basis-full {\n  flex-basis: 100%;\n}\r\n.caption-bottom {\n  caption-side: bottom;\n}\r\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-0 {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-45 {\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-100 {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-95 {\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\r\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\r\n.cursor-default {\n  cursor: default;\n}\r\n.cursor-pointer {\n  cursor: pointer;\n}\r\n.select-none {\n  user-select: none;\n}\r\n.resize {\n  resize: both;\n}\r\n.snap-start {\n  scroll-snap-align: start;\n}\r\n.list-none {\n  list-style-type: none;\n}\r\n.auto-rows-fr {\n  grid-auto-rows: minmax(0, 1fr);\n}\r\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\r\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\r\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\r\n.flex-row {\n  flex-direction: row;\n}\r\n.flex-col {\n  flex-direction: column;\n}\r\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\r\n.flex-wrap {\n  flex-wrap: wrap;\n}\r\n.items-start {\n  align-items: flex-start;\n}\r\n.items-end {\n  align-items: flex-end;\n}\r\n.items-center {\n  align-items: center;\n}\r\n.items-baseline {\n  align-items: baseline;\n}\r\n.justify-start {\n  justify-content: flex-start;\n}\r\n.justify-end {\n  justify-content: flex-end;\n}\r\n.justify-center {\n  justify-content: center;\n}\r\n.justify-between {\n  justify-content: space-between;\n}\r\n.justify-items-center {\n  justify-items: center;\n}\r\n.gap-1 {\n  gap: 0.25rem;\n}\r\n.gap-2 {\n  gap: 0.5rem;\n}\r\n.gap-3 {\n  gap: 0.75rem;\n}\r\n.gap-4 {\n  gap: 1rem;\n}\r\n.gap-5 {\n  gap: 1.25rem;\n}\r\n.gap-6 {\n  gap: 1.5rem;\n}\r\n.gap-7 {\n  gap: 1.75rem;\n}\r\n.gap-8 {\n  gap: 2rem;\n}\r\n.gap-x-2\\.5 {\n  column-gap: 0.625rem;\n}\r\n.gap-x-4 {\n  column-gap: 1rem;\n}\r\n.gap-y-12 {\n  row-gap: 3rem;\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\r\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\r\n.overflow-auto {\n  overflow: auto;\n}\r\n.overflow-hidden {\n  overflow: hidden;\n}\r\n.overflow-y-auto {\n  overflow-y: auto;\n}\r\n.overflow-x-hidden {\n  overflow-x: hidden;\n}\r\n.whitespace-nowrap {\n  white-space: nowrap;\n}\r\n.break-words {\n  overflow-wrap: break-word;\n}\r\n.rounded {\n  border-radius: 0.25rem;\n}\r\n.rounded-2xl {\n  border-radius: 1rem;\n}\r\n.rounded-3xl {\n  border-radius: 1.5rem;\n}\r\n.rounded-\\[15px\\] {\n  border-radius: 15px;\n}\r\n.rounded-\\[20px\\] {\n  border-radius: 20px;\n}\r\n.rounded-\\[25px\\] {\n  border-radius: 25px;\n}\r\n.rounded-\\[26px\\] {\n  border-radius: 26px;\n}\r\n.rounded-full {\n  border-radius: 9999px;\n}\r\n.rounded-lg {\n  border-radius: var(--radius);\n}\r\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\r\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\r\n.rounded-xl {\n  border-radius: 0.75rem;\n}\r\n.rounded-\\[20p\\] {\n  border-radius: 20p;\n}\r\n.rounded-b-lg {\n  border-bottom-right-radius: var(--radius);\n  border-bottom-left-radius: var(--radius);\n}\r\n.rounded-t-xl {\n  border-top-left-radius: 0.75rem;\n  border-top-right-radius: 0.75rem;\n}\r\n.rounded-tl-sm {\n  border-top-left-radius: calc(var(--radius) - 4px);\n}\r\n.border {\n  border-width: 1px;\n}\r\n.border-0 {\n  border-width: 0px;\n}\r\n.border-4 {\n  border-width: 4px;\n}\r\n.border-b {\n  border-bottom-width: 1px;\n}\r\n.border-t {\n  border-top-width: 1px;\n}\r\n.border-\\[\\#2821ff\\] {\n  --tw-border-opacity: 1;\n  border-color: rgb(40 33 255 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\r\n.border-destructive {\n  border-color: hsl(var(--destructive));\n}\r\n.border-footerBottomText {\n  border-color: var(--footerBottomText);\n}\r\n.border-green-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\r\n.border-input {\n  border-color: hsl(var(--input));\n}\r\n.border-textBluePrimary {\n  border-color: var(--textBluePrimary);\n}\r\n.border-transparent {\n  border-color: transparent;\n}\r\n.border-gray-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\r\n.border-t-transparent {\n  border-top-color: transparent;\n}\r\n.bg-\\[\\#0000004f\\] {\n  background-color: #0000004f;\n}\r\n.bg-\\[\\#080704\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(8 7 4 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#5f6f7f\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(95 111 127 / var(--tw-bg-opacity, 1));\n}\r\n.bg-accordionBG {\n  background-color: var(--accordionBG);\n}\r\n.bg-background {\n  background-color: hsl(var(--background));\n}\r\n.bg-black {\n  background-color: var(--black);\n}\r\n.bg-blue-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\n}\r\n.bg-border {\n  background-color: hsl(var(--border));\n}\r\n.bg-card {\n  background-color: hsl(var(--card));\n}\r\n.bg-carouselDotsBG {\n  background-color: var(--carouselDotsBG);\n}\r\n.bg-descLightBlack {\n  background-color: var(--descLightBlack);\n}\r\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\r\n.bg-footerBG {\n  background-color: var(--footerBG);\n}\r\n.bg-formBG {\n  background-color: var(--formBG);\n}\r\n.bg-gray-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\r\n.bg-muted {\n  background-color: hsl(var(--muted));\n}\r\n.bg-muted\\/50 {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.bg-popover {\n  background-color: hsl(var(--popover));\n}\r\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\r\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\r\n.bg-textBluePrimary {\n  background-color: var(--textBluePrimary);\n}\r\n.bg-transparent {\n  background-color: transparent;\n}\r\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-whiteOne {\n  background-color: var(--whiteOne);\n}\r\n.bg-red-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));\n}\r\n.bg-opacity-40 {\n  --tw-bg-opacity: 0.4;\n}\r\n.bg-\\[url\\(\\'\\/assets\\/faqImage\\.webp\\'\\)\\] {\n  background-image: url('/assets/faqImage.webp');\n}\r\n.bg-aboutusBG {\n  background-image: url('/About_Us.jpg');\n}\r\n.bg-buttonBGPrimary {\n  background-image: linear-gradient(90deg, #0E1795 0%, #1420CD 100%);\n}\r\n.bg-coursesBanner {\n  background-image: url(/courceBg.jpg);\n}\r\n.bg-coursesDetailBanner {\n  background-image: url(/assets/courseDetailBanner.png);\n}\r\n.bg-dotsImage {\n  background-image: url(/assets/dots.png);\n}\r\n.bg-enquireBannerTwo {\n  background-image: url(/cptManket_modeji.jpg);\n}\r\n.bg-eventBanner {\n  background-image: url(/eventBg.jpg);\n}\r\n.bg-founderBG {\n  background-image: url('/founderBg.jpg');\n}\r\n.bg-galleryBanner {\n  background-image: url(/AmitjiBanner.jpg);\n}\r\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-t {\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\r\n.bg-landingBanner {\n  background-image: url(/homeBannerImage.webp);\n}\r\n.bg-navbarBGPrimary {\n  background-image: linear-gradient(90.5deg, rgba(252, 252, 244, 0.56) 3.53%, rgba(252, 252, 244, 0.32) 104.93%);;\n}\r\n.bg-spiralImage {\n  background-image: url(/assets/spiralImage.png);\n}\r\n.bg-\\[url\\(\\$\\{coursesData\\.image\\}\\)\\] {\n  background-image: url(${coursesData.image});\n}\r\n.from-transparent {\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\r\n.bg-cover {\n  background-size: cover;\n}\r\n.bg-local {\n  background-attachment: local;\n}\r\n.bg-\\[-26rem\\] {\n  background-position: -26rem;\n}\r\n.bg-\\[-35rem\\] {\n  background-position: -35rem;\n}\r\n.bg-\\[-45rem\\] {\n  background-position: -45rem;\n}\r\n.bg-\\[90\\%_center\\] {\n  background-position: 90% center;\n}\r\n.bg-center {\n  background-position: center;\n}\r\n.bg-left {\n  background-position: left;\n}\r\n.bg-top {\n  background-position: top;\n}\r\n.bg-right {\n  background-position: right;\n}\r\n.bg-no-repeat {\n  background-repeat: no-repeat;\n}\r\n.fill-current {\n  fill: currentColor;\n}\r\n.fill-emptyStarBG {\n  fill: var(--emptyStarBG);\n}\r\n.fill-fullStarBG {\n  fill: var(--fullStarBG);\n}\r\n.fill-halfStarBG {\n  fill: var(--halfStarBG);\n}\r\n.object-contain {\n  object-fit: contain;\n}\r\n.object-cover {\n  object-fit: cover;\n}\r\n.p-0 {\n  padding: 0px;\n}\r\n.p-1 {\n  padding: 0.25rem;\n}\r\n.p-2 {\n  padding: 0.5rem;\n}\r\n.p-3 {\n  padding: 0.75rem;\n}\r\n.p-4 {\n  padding: 1rem;\n}\r\n.p-5 {\n  padding: 1.25rem;\n}\r\n.p-6 {\n  padding: 1.5rem;\n}\r\n.p-8 {\n  padding: 2rem;\n}\r\n.p-\\[1\\.4rem\\] {\n  padding: 1.4rem;\n}\r\n.p-\\[1\\.7rem\\] {\n  padding: 1.7rem;\n}\r\n.p-\\[10px\\] {\n  padding: 10px;\n}\r\n.px-0 {\n  padding-left: 0px;\n  padding-right: 0px;\n}\r\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-5 {\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\r\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\r\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\r\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\r\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\r\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-2\\.5 {\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\r\n.py-28 {\n  padding-top: 7rem;\n  padding-bottom: 7rem;\n}\r\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n.py-6 {\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\r\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\r\n.py-5 {\n  padding-top: 1.25rem;\n  padding-bottom: 1.25rem;\n}\r\n.pb-16 {\n  padding-bottom: 4rem;\n}\r\n.pb-4 {\n  padding-bottom: 1rem;\n}\r\n.pl-2 {\n  padding-left: 0.5rem;\n}\r\n.pl-3 {\n  padding-left: 0.75rem;\n}\r\n.pl-4 {\n  padding-left: 1rem;\n}\r\n.pl-6 {\n  padding-left: 1.5rem;\n}\r\n.pl-8 {\n  padding-left: 2rem;\n}\r\n.pr-12 {\n  padding-right: 3rem;\n}\r\n.pr-2 {\n  padding-right: 0.5rem;\n}\r\n.pr-6 {\n  padding-right: 1.5rem;\n}\r\n.pr-8 {\n  padding-right: 2rem;\n}\r\n.pt-0 {\n  padding-top: 0px;\n}\r\n.pt-12 {\n  padding-top: 3rem;\n}\r\n.pt-3 {\n  padding-top: 0.75rem;\n}\r\n.pt-32 {\n  padding-top: 8rem;\n}\r\n.pt-4 {\n  padding-top: 1rem;\n}\r\n.pt-64 {\n  padding-top: 16rem;\n}\r\n.pt-8 {\n  padding-top: 2rem;\n}\r\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\r\n.pb-7 {\n  padding-bottom: 1.75rem;\n}\r\n.pr-4 {\n  padding-right: 1rem;\n}\r\n.pl-5 {\n  padding-left: 1.25rem;\n}\r\n.text-left {\n  text-align: left;\n}\r\n.text-center {\n  text-align: center;\n}\r\n.text-justify {\n  text-align: justify;\n}\r\n.text-start {\n  text-align: start;\n}\r\n.text-end {\n  text-align: end;\n}\r\n.align-middle {\n  vertical-align: middle;\n}\r\n.align-super {\n  vertical-align: super;\n}\r\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\r\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\r\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1;\n}\r\n.text-\\[13px\\] {\n  font-size: 13px;\n}\r\n.text-\\[14px\\] {\n  font-size: 14px;\n}\r\n.text-\\[15px\\] {\n  font-size: 15px;\n}\r\n.text-\\[16\\.73px\\] {\n  font-size: 16.73px;\n}\r\n.text-\\[16px\\] {\n  font-size: 16px;\n}\r\n.text-\\[17px\\] {\n  font-size: 17px;\n}\r\n.text-\\[18px\\] {\n  font-size: 18px;\n}\r\n.text-\\[2\\.3rem\\] {\n  font-size: 2.3rem;\n}\r\n.text-\\[20px\\] {\n  font-size: 20px;\n}\r\n.text-\\[21px\\] {\n  font-size: 21px;\n}\r\n.text-\\[28px\\] {\n  font-size: 28px;\n}\r\n.text-\\[32px\\] {\n  font-size: 32px;\n}\r\n.text-\\[47px\\] {\n  font-size: 47px;\n}\r\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\r\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\r\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\r\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.font-bold {\n  font-weight: 700;\n}\r\n.font-extrabold {\n  font-weight: 800;\n}\r\n.font-light {\n  font-weight: 300;\n}\r\n.font-medium {\n  font-weight: 500;\n}\r\n.font-normal {\n  font-weight: 400;\n}\r\n.font-semibold {\n  font-weight: 600;\n}\r\n.capitalize {\n  text-transform: capitalize;\n}\r\n.italic {\n  font-style: italic;\n}\r\n.leading-5 {\n  line-height: 1.25rem;\n}\r\n.leading-9 {\n  line-height: 2.25rem;\n}\r\n.leading-\\[18\\.9px\\] {\n  line-height: 18.9px;\n}\r\n.leading-\\[2\\.7rem\\] {\n  line-height: 2.7rem;\n}\r\n.leading-\\[20px\\] {\n  line-height: 20px;\n}\r\n.leading-\\[21\\.2px\\] {\n  line-height: 21.2px;\n}\r\n.leading-\\[21px\\] {\n  line-height: 21px;\n}\r\n.leading-\\[22\\.2px\\] {\n  line-height: 22.2px;\n}\r\n.leading-\\[22\\.8px\\] {\n  line-height: 22.8px;\n}\r\n.leading-\\[23px\\] {\n  line-height: 23px;\n}\r\n.leading-\\[24\\.2px\\] {\n  line-height: 24.2px;\n}\r\n.leading-\\[24px\\] {\n  line-height: 24px;\n}\r\n.leading-\\[25px\\] {\n  line-height: 25px;\n}\r\n.leading-\\[35px\\] {\n  line-height: 35px;\n}\r\n.leading-\\[40px\\] {\n  line-height: 40px;\n}\r\n.leading-none {\n  line-height: 1;\n}\r\n.leading-relaxed {\n  line-height: 1.625;\n}\r\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\r\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\r\n.text-\\[\\#1420CD\\] {\n  --tw-text-opacity: 1;\n  color: rgb(20 32 205 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#464C3B\\] {\n  --tw-text-opacity: 1;\n  color: rgb(70 76 59 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[--textBluePrimary\\] {\n  color: var(--textBluePrimary);\n}\r\n.text-arrowIconColor {\n  color: var(--arrowIconColor);\n}\r\n.text-black {\n  color: var(--black);\n}\r\n.text-customBlack {\n  color: var(--customBlack);\n}\r\n.text-customGrey100 {\n  color: var(--customGrey100);\n}\r\n.text-descLightBlack {\n  color: var(--descLightBlack);\n}\r\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\r\n.text-emptyStarBG {\n  color: var(--emptyStarBG);\n}\r\n.text-footerBottomText {\n  color: var(--footerBottomText);\n}\r\n.text-footerPrimaryText {\n  color: var(--footerPrimaryText);\n}\r\n.text-foreground {\n  color: hsl(var(--foreground));\n}\r\n.text-foreground\\/50 {\n  color: hsl(var(--foreground) / 0.5);\n}\r\n.text-formBG {\n  color: var(--formBG);\n}\r\n.text-formLabelSubColor {\n  color: var(--formLabelSubColor);\n}\r\n.text-formPara {\n  color: var(--formPara);\n}\r\n.text-formTextColor {\n  color: var(--formTextColor);\n}\r\n.text-fullStarBG {\n  color: var(--fullStarBG);\n}\r\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\r\n.text-halfStarBG {\n  color: var(--halfStarBG);\n}\r\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\r\n.text-neutral-50 {\n  --tw-text-opacity: 1;\n  color: rgb(250 250 250 / var(--tw-text-opacity, 1));\n}\r\n.text-neutralGrayText {\n  color: var(--neutralGrayText);\n}\r\n.text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\r\n.text-primary {\n  color: hsl(var(--primary));\n}\r\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\r\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\r\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\r\n.text-textBluePrimary {\n  color: var(--textBluePrimary);\n}\r\n.text-textGreyPrimary {\n  color: var(--textGreyPrimary);\n}\r\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.text-whiteOne {\n  color: var(--whiteOne);\n}\r\n.underline {\n  text-decoration-line: underline;\n}\r\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\r\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\r\n.opacity-0 {\n  opacity: 0;\n}\r\n.opacity-100 {\n  opacity: 1;\n}\r\n.opacity-40 {\n  opacity: 0.4;\n}\r\n.opacity-50 {\n  opacity: 0.5;\n}\r\n.opacity-60 {\n  opacity: 0.6;\n}\r\n.opacity-70 {\n  opacity: 0.7;\n}\r\n.opacity-90 {\n  opacity: 0.9;\n}\r\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-cardButtonShadow {\n  --tw-shadow: 0px 8px 30px 0px #00000014;\n  --tw-shadow-colored: 0px 8px 30px 0px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-cardsBoxShadow {\n  --tw-shadow: 0px 5.46px 19.11px 0px #00000014;\n  --tw-shadow-colored: 0px 5.46px 19.11px 0px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-courseCardShadow {\n  --tw-shadow: 0px 10px 37px 0px #00000014;\n  --tw-shadow-colored: 0px 10px 37px 0px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-dropdownShadow {\n  --tw-shadow: 0px 4px 20px 0px #00000040;\n  --tw-shadow-colored: 0px 4px 20px 0px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.outline {\n  outline-style: solid;\n}\r\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-red-500 {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\r\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur-\\[20px\\] {\n  --tw-backdrop-blur: blur(20px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.duration-200 {\n  transition-duration: 200ms;\n}\r\n.duration-300 {\n  transition-duration: 300ms;\n}\r\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\r\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\r\n.duration-200 {\n  animation-duration: 200ms;\n}\r\n.duration-300 {\n  animation-duration: 300ms;\n}\r\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n\r\n/* Add this to your global CSS or a module */\r\n.scrollbar-custom::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.scrollbar-custom::-webkit-scrollbar-track {\r\n  background: #1a1a2e;\r\n  /* dark background */\r\n  border-top-right-radius: 4px;\r\n  border-bottom-right-radius: 4px;  \r\n}\r\n\r\n.scrollbar-custom::-webkit-scrollbar-thumb {\r\n  background-color: #3b82f6;\r\n  /* Tailwind's blue-500 */\r\n  border-top-right-radius: 4px;\r\n  border-bottom-right-radius: 4px;\r\n}\r\n\r\n.scrollbar-custom::-webkit-scrollbar-thumb:hover {\r\n  background-color: #2563eb;\r\n  /* Tailwind's blue-600 */\r\n}\r\n\r\n.marker\\:font-bold *::marker {\n  font-weight: 700;\n}\r\n\r\n.marker\\:font-bold::marker {\n  font-weight: 700;\n}\r\n\r\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.last\\:border-b-0:last-child {\n  border-bottom-width: 0px;\n}\r\n\r\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\r\n\r\n.hover\\:bg-blue-800:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));\n}\r\n\r\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\r\n\r\n.hover\\:bg-muted\\/50:hover {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n\r\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\r\n\r\n.hover\\:bg-secondary:hover {\n  background-color: hsl(var(--secondary));\n}\r\n\r\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\r\n\r\n.hover\\:bg-buttonBGPrimary:hover {\n  background-image: linear-gradient(90deg, #0E1795 0%, #1420CD 100%);\n}\r\n\r\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\r\n\r\n.hover\\:text-black:hover {\n  color: var(--black);\n}\r\n\r\n.hover\\:text-blue-300:hover {\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-blue-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-foreground:hover {\n  color: hsl(var(--foreground));\n}\r\n\r\n.hover\\:text-gray-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-textBluePrimary:hover {\n  color: var(--textBluePrimary);\n}\r\n\r\n.hover\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:text-gray-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\r\n\r\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\r\n\r\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\r\n\r\n.focus\\:bg-accent:focus {\n  background-color: hsl(var(--accent));\n}\r\n\r\n.focus\\:bg-transparent:focus {\n  background-color: transparent;\n}\r\n\r\n.focus\\:text-accent-foreground:focus {\n  color: hsl(var(--accent-foreground));\n}\r\n\r\n.focus\\:opacity-100:focus {\n  opacity: 1;\n}\r\n\r\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n\r\n.focus\\:ring-1:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus\\:ring-blue-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\r\n\r\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n\r\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\r\n\r\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n\r\n.focus-visible\\:ring-1:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n\r\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n\r\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\r\n\r\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\r\n\r\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\r\n\r\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:border-muted\\/40 {\n  border-color: hsl(var(--muted) / 0.4);\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:text-red-300 {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:hover {\n  border-color: hsl(var(--destructive) / 0.3);\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:bg-destructive:hover {\n  background-color: hsl(var(--destructive));\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:hover {\n  color: hsl(var(--destructive-foreground));\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-red-50:hover {\n  --tw-text-opacity: 1;\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-destructive:focus {\n  --tw-ring-color: hsl(var(--destructive));\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-red-400:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\n}\r\n\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:focus {\n  --tw-ring-offset-color: #dc2626;\n}\r\n\r\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\r\n\r\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"] {\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"] {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"] {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"] {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=\"cancel\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[swipe\\=end\\]\\:translate-x-\\[var\\(--radix-toast-swipe-end-x\\)\\][data-swipe=\"end\"] {\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=\"move\"] {\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\r\n\r\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"] {\n  background-color: hsl(var(--muted));\n}\r\n\r\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"] {\n  color: hsl(var(--muted-foreground));\n}\r\n\r\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\r\n\r\n.data-\\[swipe\\=move\\]\\:transition-none[data-swipe=\"move\"] {\n  transition-property: none;\n}\r\n\r\n.data-\\[motion\\^\\=from-\\]\\:animate-in[data-motion^=\"from-\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n\r\n.data-\\[state\\=visible\\]\\:animate-in[data-state=\"visible\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n\r\n.data-\\[motion\\^\\=to-\\]\\:animate-out[data-motion^=\"to-\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n\r\n.data-\\[state\\=hidden\\]\\:animate-out[data-state=\"hidden\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n\r\n.data-\\[swipe\\=end\\]\\:animate-out[data-swipe=\"end\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n\r\n.data-\\[motion\\^\\=from-\\]\\:fade-in[data-motion^=\"from-\"] {\n  --tw-enter-opacity: 0;\n}\r\n\r\n.data-\\[motion\\^\\=to-\\]\\:fade-out[data-motion^=\"to-\"] {\n  --tw-exit-opacity: 0;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:fade-out-80[data-state=\"closed\"] {\n  --tw-exit-opacity: 0.8;\n}\r\n\r\n.data-\\[state\\=hidden\\]\\:fade-out[data-state=\"hidden\"] {\n  --tw-exit-opacity: 0;\n}\r\n\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\r\n\r\n.data-\\[state\\=visible\\]\\:fade-in[data-state=\"visible\"] {\n  --tw-enter-opacity: 0;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\r\n\r\n.data-\\[state\\=open\\]\\:zoom-in-90[data-state=\"open\"] {\n  --tw-enter-scale: .9;\n}\r\n\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\r\n\r\n.data-\\[motion\\=from-end\\]\\:slide-in-from-right-52[data-motion=\"from-end\"] {\n  --tw-enter-translate-x: 13rem;\n}\r\n\r\n.data-\\[motion\\=from-start\\]\\:slide-in-from-left-52[data-motion=\"from-start\"] {\n  --tw-enter-translate-x: -13rem;\n}\r\n\r\n.data-\\[motion\\=to-end\\]\\:slide-out-to-right-52[data-motion=\"to-end\"] {\n  --tw-exit-translate-x: 13rem;\n}\r\n\r\n.data-\\[motion\\=to-start\\]\\:slide-out-to-left-52[data-motion=\"to-start\"] {\n  --tw-exit-translate-x: -13rem;\n}\r\n\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\r\n\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\r\n\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\r\n\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right-full[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"] {\n  --tw-exit-translate-y: -48%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"] {\n  --tw-enter-translate-y: -48%;\n}\r\n\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-full[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\r\n\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n@media (min-width: 640px) {\n\n  .sm\\:bottom-0 {\n    bottom: 0px;\n  }\n\n  .sm\\:right-0 {\n    right: 0px;\n  }\n\n  .sm\\:top-auto {\n    top: auto;\n  }\n\n  .sm\\:mt-44 {\n    margin-top: 11rem;\n  }\n\n  .sm\\:grid {\n    display: grid;\n  }\n\n  .sm\\:hidden {\n    display: none;\n  }\n\n  .sm\\:h-\\[300px\\] {\n    height: 300px;\n  }\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-\\[repeat\\(auto-fit\\2c _minmax\\(200px\\2c _1fr\\)\\)\\] {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:flex-col {\n    flex-direction: column;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:rounded-lg {\n    border-radius: var(--radius);\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .sm\\:text-\\[17px\\] {\n    font-size: 17px;\n  }\n\n  .sm\\:leading-\\[22\\.8px\\] {\n    line-height: 22.8px;\n  }\n\n  .data-\\[state\\=open\\]\\:sm\\:slide-in-from-bottom-full[data-state=\"open\"] {\n    --tw-enter-translate-y: 100%;\n  }\n}\r\n\r\n@media (min-width: 768px) {\n\n  .md\\:absolute {\n    position: absolute;\n  }\n\n  .md\\:bottom-32 {\n    bottom: 8rem;\n  }\n\n  .md\\:bottom-\\[300px\\] {\n    bottom: 300px;\n  }\n\n  .md\\:mx-0 {\n    margin-left: 0px;\n    margin-right: 0px;\n  }\n\n  .md\\:mx-12 {\n    margin-left: 3rem;\n    margin-right: 3rem;\n  }\n\n  .md\\:mx-20 {\n    margin-left: 5rem;\n    margin-right: 5rem;\n  }\n\n  .md\\:mx-auto {\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .md\\:mb-0 {\n    margin-bottom: 0px;\n  }\n\n  .md\\:mb-2 {\n    margin-bottom: 0.5rem;\n  }\n\n  .md\\:mb-20 {\n    margin-bottom: 5rem;\n  }\n\n  .md\\:mb-3 {\n    margin-bottom: 0.75rem;\n  }\n\n  .md\\:mb-4 {\n    margin-bottom: 1rem;\n  }\n\n  .md\\:mb-6 {\n    margin-bottom: 1.5rem;\n  }\n\n  .md\\:mb-8 {\n    margin-bottom: 2rem;\n  }\n\n  .md\\:ml-4 {\n    margin-left: 1rem;\n  }\n\n  .md\\:ml-8 {\n    margin-left: 2rem;\n  }\n\n  .md\\:mt-12 {\n    margin-top: 3rem;\n  }\n\n  .md\\:mt-32 {\n    margin-top: 8rem;\n  }\n\n  .md\\:mt-8 {\n    margin-top: 2rem;\n  }\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:grid {\n    display: grid;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:h-14 {\n    height: 3.5rem;\n  }\n\n  .md\\:h-36 {\n    height: 9rem;\n  }\n\n  .md\\:h-9 {\n    height: 2.25rem;\n  }\n\n  .md\\:h-\\[100px\\] {\n    height: 100px;\n  }\n\n  .md\\:h-\\[220px\\] {\n    height: 220px;\n  }\n\n  .md\\:h-\\[390px\\] {\n    height: 390px;\n  }\n\n  .md\\:h-\\[400px\\] {\n    height: 400px;\n  }\n\n  .md\\:h-\\[480px\\] {\n    height: 480px;\n  }\n\n  .md\\:h-\\[550px\\] {\n    height: 550px;\n  }\n\n  .md\\:h-\\[789px\\] {\n    height: 789px;\n  }\n\n  .md\\:h-full {\n    height: 100%;\n  }\n\n  .md\\:h-1 {\n    height: 0.25rem;\n  }\n\n  .md\\:h-12 {\n    height: 3rem;\n  }\n\n  .md\\:h-10 {\n    height: 2.5rem;\n  }\n\n  .md\\:h-\\[239px\\] {\n    height: 239px;\n  }\n\n  .md\\:min-h-\\[110vh\\] {\n    min-height: 110vh;\n  }\n\n  .md\\:w-1\\/2 {\n    width: 50%;\n  }\n\n  .md\\:w-1\\/3 {\n    width: 33.333333%;\n  }\n\n  .md\\:w-11\\/12 {\n    width: 91.666667%;\n  }\n\n  .md\\:w-36 {\n    width: 9rem;\n  }\n\n  .md\\:w-4\\/5 {\n    width: 80%;\n  }\n\n  .md\\:w-44 {\n    width: 11rem;\n  }\n\n  .md\\:w-9 {\n    width: 2.25rem;\n  }\n\n  .md\\:w-\\[100px\\] {\n    width: 100px;\n  }\n\n  .md\\:w-\\[1500px\\] {\n    width: 1500px;\n  }\n\n  .md\\:w-\\[180px\\] {\n    width: 180px;\n  }\n\n  .md\\:w-\\[200px\\] {\n    width: 200px;\n  }\n\n  .md\\:w-\\[208px\\] {\n    width: 208px;\n  }\n\n  .md\\:w-\\[328px\\] {\n    width: 328px;\n  }\n\n  .md\\:w-\\[554px\\] {\n    width: 554px;\n  }\n\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {\n    width: var(--radix-navigation-menu-viewport-width);\n  }\n\n  .md\\:w-auto {\n    width: auto;\n  }\n\n  .md\\:w-full {\n    width: 100%;\n  }\n\n  .md\\:w-\\[456px\\] {\n    width: 456px;\n  }\n\n  .md\\:w-\\[220px\\] {\n    width: 220px;\n  }\n\n  .md\\:max-w-\\[420px\\] {\n    max-width: 420px;\n  }\n\n  .md\\:basis-2\\/5 {\n    flex-basis: 40%;\n  }\n\n  .md\\:basis-3\\/5 {\n    flex-basis: 60%;\n  }\n\n  .md\\:basis-\\[40\\%\\] {\n    flex-basis: 40%;\n  }\n\n  .md\\:basis-\\[60\\%\\] {\n    flex-basis: 60%;\n  }\n\n  .md\\:list-decimal {\n    list-style-type: decimal;\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:flex-row-reverse {\n    flex-direction: row-reverse;\n  }\n\n  .md\\:gap-2 {\n    gap: 0.5rem;\n  }\n\n  .md\\:gap-3 {\n    gap: 0.75rem;\n  }\n\n  .md\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .md\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .md\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .md\\:space-y-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n  }\n\n  .md\\:bg-footerBG {\n    background-color: var(--footerBG);\n  }\n\n  .md\\:bg-alumniCurve {\n    background-image: url(/assets/curveImage.png);\n  }\n\n  .md\\:bg-\\[-26rem\\] {\n    background-position: -26rem;\n  }\n\n  .md\\:bg-\\[60\\%_center\\] {\n    background-position: 60% center;\n  }\n\n  .md\\:bg-center {\n    background-position: center;\n  }\n\n  .md\\:bg-top {\n    background-position: top;\n  }\n\n  .md\\:p-12 {\n    padding: 3rem;\n  }\n\n  .md\\:p-4 {\n    padding: 1rem;\n  }\n\n  .md\\:p-5 {\n    padding: 1.25rem;\n  }\n\n  .md\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .md\\:p-8 {\n    padding: 2rem;\n  }\n\n  .md\\:px-0 {\n    padding-left: 0px;\n    padding-right: 0px;\n  }\n\n  .md\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .md\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .md\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .md\\:py-12 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\n\n  .md\\:py-2 {\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n  }\n\n  .md\\:py-3 {\n    padding-top: 0.75rem;\n    padding-bottom: 0.75rem;\n  }\n\n  .md\\:py-4 {\n    padding-top: 1rem;\n    padding-bottom: 1rem;\n  }\n\n  .md\\:pb-6 {\n    padding-bottom: 1.5rem;\n  }\n\n  .md\\:pl-3 {\n    padding-left: 0.75rem;\n  }\n\n  .md\\:pl-4 {\n    padding-left: 1rem;\n  }\n\n  .md\\:pl-6 {\n    padding-left: 1.5rem;\n  }\n\n  .md\\:pr-2 {\n    padding-right: 0.5rem;\n  }\n\n  .md\\:pr-3 {\n    padding-right: 0.75rem;\n  }\n\n  .md\\:pl-5 {\n    padding-left: 1.25rem;\n  }\n\n  .md\\:pl-7 {\n    padding-left: 1.75rem;\n  }\n\n  .md\\:text-left {\n    text-align: left;\n  }\n\n  .md\\:text-center {\n    text-align: center;\n  }\n\n  .md\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .md\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .md\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-\\[40px\\] {\n    font-size: 40px;\n  }\n\n  .md\\:text-base {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .md\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .md\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:font-bold {\n    font-weight: 700;\n  }\n\n  .md\\:font-extrabold {\n    font-weight: 800;\n  }\n\n  .md\\:font-light {\n    font-weight: 300;\n  }\n\n  .md\\:font-medium {\n    font-weight: 500;\n  }\n\n  .md\\:leading-\\[44px\\] {\n    line-height: 44px;\n  }\n\n  .md\\:shadow-none {\n    --tw-shadow: 0 0 #0000;\n    --tw-shadow-colored: 0 0 #0000;\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  }\n\n  .md\\:marker\\:text-2xl *::marker {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:marker\\:text-2xl::marker {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n}\r\n\r\n@media (min-width: 1024px) {\n\n  .lg\\:relative {\n    position: relative;\n  }\n\n  .lg\\:bottom-24 {\n    bottom: 6rem;\n  }\n\n  .lg\\:bottom-60 {\n    bottom: 15rem;\n  }\n\n  .lg\\:bottom-\\[0px\\] {\n    bottom: 0px;\n  }\n\n  .lg\\:bottom-\\[300px\\] {\n    bottom: 300px;\n  }\n\n  .lg\\:mx-20 {\n    margin-left: 5rem;\n    margin-right: 5rem;\n  }\n\n  .lg\\:mx-32 {\n    margin-left: 8rem;\n    margin-right: 8rem;\n  }\n\n  .lg\\:mx-36 {\n    margin-left: 9rem;\n    margin-right: 9rem;\n  }\n\n  .lg\\:mx-auto {\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .lg\\:mx-2 {\n    margin-left: 0.5rem;\n    margin-right: 0.5rem;\n  }\n\n  .lg\\:mx-12 {\n    margin-left: 3rem;\n    margin-right: 3rem;\n  }\n\n  .lg\\:mb-16 {\n    margin-bottom: 4rem;\n  }\n\n  .lg\\:mb-2\\.5 {\n    margin-bottom: 0.625rem;\n  }\n\n  .lg\\:mb-3 {\n    margin-bottom: 0.75rem;\n  }\n\n  .lg\\:mb-4 {\n    margin-bottom: 1rem;\n  }\n\n  .lg\\:mt-12 {\n    margin-top: 3rem;\n  }\n\n  .lg\\:mt-3 {\n    margin-top: 0.75rem;\n  }\n\n  .lg\\:mt-32 {\n    margin-top: 8rem;\n  }\n\n  .lg\\:block {\n    display: block;\n  }\n\n  .lg\\:flex {\n    display: flex;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:h-48 {\n    height: 12rem;\n  }\n\n  .lg\\:h-\\[580px\\] {\n    height: 580px;\n  }\n\n  .lg\\:h-\\[696px\\] {\n    height: 696px;\n  }\n\n  .lg\\:w-1\\/2 {\n    width: 50%;\n  }\n\n  .lg\\:w-4\\/5 {\n    width: 80%;\n  }\n\n  .lg\\:w-48 {\n    width: 12rem;\n  }\n\n  .lg\\:w-\\[220px\\] {\n    width: 220px;\n  }\n\n  .lg\\:w-\\[32rem\\] {\n    width: 32rem;\n  }\n\n  .lg\\:w-\\[42rem\\] {\n    width: 42rem;\n  }\n\n  .lg\\:basis-6\\/12 {\n    flex-basis: 50%;\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n\n  .lg\\:flex-row {\n    flex-direction: row;\n  }\n\n  .lg\\:flex-row-reverse {\n    flex-direction: row-reverse;\n  }\n\n  .lg\\:items-start {\n    align-items: flex-start;\n  }\n\n  .lg\\:gap-3 {\n    gap: 0.75rem;\n  }\n\n  .lg\\:gap-5 {\n    gap: 1.25rem;\n  }\n\n  .lg\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .lg\\:bg-dotsImage {\n    background-image: url(/assets/dots.png);\n  }\n\n  .lg\\:bg-center {\n    background-position: center;\n  }\n\n  .lg\\:p-12 {\n    padding: 3rem;\n  }\n\n  .lg\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .lg\\:p-8 {\n    padding: 2rem;\n  }\n\n  .lg\\:px-12 {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .lg\\:px-20 {\n    padding-left: 5rem;\n    padding-right: 5rem;\n  }\n\n  .lg\\:px-32 {\n    padding-left: 8rem;\n    padding-right: 8rem;\n  }\n\n  .lg\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .lg\\:px-\\[3rem\\] {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .lg\\:py-12 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\n\n  .lg\\:py-28 {\n    padding-top: 7rem;\n    padding-bottom: 7rem;\n  }\n\n  .lg\\:py-8 {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n  }\n\n  .lg\\:text-left {\n    text-align: left;\n  }\n\n  .lg\\:text-start {\n    text-align: start;\n  }\n\n  .lg\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .lg\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .lg\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .lg\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-7xl {\n    font-size: 4.5rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-base {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .lg\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .lg\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .lg\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .lg\\:font-light {\n    font-weight: 300;\n  }\n\n  .lg\\:leading-tight {\n    line-height: 1.25;\n  }\n}\r\n\r\n@media (min-width: 1280px) {\n\n  .xl\\:mx-32 {\n    margin-left: 8rem;\n    margin-right: 8rem;\n  }\n\n  .xl\\:mt-44 {\n    margin-top: 11rem;\n  }\n\n  .xl\\:flex {\n    display: flex;\n  }\n\n  .xl\\:hidden {\n    display: none;\n  }\n\n  .xl\\:w-1\\/4 {\n    width: 25%;\n  }\n\n  .xl\\:w-3\\/4 {\n    width: 75%;\n  }\n\n  .xl\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .xl\\:flex-row {\n    flex-direction: row;\n  }\n\n  .xl\\:items-center {\n    align-items: center;\n  }\n\n  .xl\\:gap-16 {\n    gap: 4rem;\n  }\n\n  .xl\\:p-4 {\n    padding: 1rem;\n  }\n\n  .xl\\:p-8 {\n    padding: 2rem;\n  }\n\n  .xl\\:px-16 {\n    padding-left: 4rem;\n    padding-right: 4rem;\n  }\n\n  .xl\\:px-32 {\n    padding-left: 8rem;\n    padding-right: 8rem;\n  }\n\n  .xl\\:py-28 {\n    padding-top: 7rem;\n    padding-bottom: 7rem;\n  }\n\n  .xl\\:text-left {\n    text-align: left;\n  }\n\n  .xl\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .xl\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .xl\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .xl\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .xl\\:text-base {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n}\r\n\r\n@media (min-width: 1024px) and (max-width: 1200px) {\n\n  .desktop\\:mx-auto {\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .desktop\\:h-\\[612px\\] {\n    height: 612px;\n  }\n\n  .desktop\\:h-\\[875px\\] {\n    height: 875px;\n  }\n\n  .desktop\\:w-\\[90\\%\\] {\n    width: 90%;\n  }\n\n  .desktop\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .desktop\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .desktop\\:pt-16 {\n    padding-top: 4rem;\n  }\n\n  .desktop\\:text-left {\n    text-align: left;\n  }\n\n  .desktop\\:text-\\[16px\\] {\n    font-size: 16px;\n  }\n\n  .desktop\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n}\r\n\r\n@media (min-width: 768px) and (max-width: 1000px) {\n\n  .tablet\\:mx-12 {\n    margin-left: 3rem;\n    margin-right: 3rem;\n  }\n\n  .tablet\\:mx-6 {\n    margin-left: 1.5rem;\n    margin-right: 1.5rem;\n  }\n\n  .tablet\\:my-8 {\n    margin-top: 2rem;\n    margin-bottom: 2rem;\n  }\n\n  .tablet\\:mb-0 {\n    margin-bottom: 0px;\n  }\n\n  .tablet\\:mb-16 {\n    margin-bottom: 4rem;\n  }\n\n  .tablet\\:mb-24 {\n    margin-bottom: 6rem;\n  }\n\n  .tablet\\:mb-8 {\n    margin-bottom: 2rem;\n  }\n\n  .tablet\\:ml-0 {\n    margin-left: 0px;\n  }\n\n  .tablet\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .tablet\\:mt-4 {\n    margin-top: 1rem;\n  }\n\n  .tablet\\:mt-6 {\n    margin-top: 1.5rem;\n  }\n\n  .tablet\\:mt-8 {\n    margin-top: 2rem;\n  }\n\n  .tablet\\:flex {\n    display: flex;\n  }\n\n  .tablet\\:h-\\[366px\\] {\n    height: 366px;\n  }\n\n  .tablet\\:h-\\[492px\\] {\n    height: 492px;\n  }\n\n  .tablet\\:h-\\[589px\\] {\n    height: 589px;\n  }\n\n  .tablet\\:h-\\[780px\\] {\n    height: 780px;\n  }\n\n  .tablet\\:w-1\\/2 {\n    width: 50%;\n  }\n\n  .tablet\\:w-\\[100\\%\\] {\n    width: 100%;\n  }\n\n  .tablet\\:w-\\[368px\\] {\n    width: 368px;\n  }\n\n  .tablet\\:w-\\[700px\\] {\n    width: 700px;\n  }\n\n  .tablet\\:w-\\[85\\%\\] {\n    width: 85%;\n  }\n\n  .tablet\\:w-full {\n    width: 100%;\n  }\n\n  .tablet\\:max-w-\\[100\\%\\] {\n    max-width: 100%;\n  }\n\n  .tablet\\:max-w-full {\n    max-width: 100%;\n  }\n\n  .tablet\\:grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n\n  .tablet\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .tablet\\:flex-row {\n    flex-direction: row;\n  }\n\n  .tablet\\:flex-row-reverse {\n    flex-direction: row-reverse;\n  }\n\n  .tablet\\:flex-col {\n    flex-direction: column;\n  }\n\n  .tablet\\:flex-wrap {\n    flex-wrap: wrap;\n  }\n\n  .tablet\\:items-center {\n    align-items: center;\n  }\n\n  .tablet\\:items-stretch {\n    align-items: stretch;\n  }\n\n  .tablet\\:justify-center {\n    justify-content: center;\n  }\n\n  .tablet\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .tablet\\:p-0 {\n    padding: 0px;\n  }\n\n  .tablet\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .tablet\\:py-12 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\n\n  .tablet\\:pb-0 {\n    padding-bottom: 0px;\n  }\n\n  .tablet\\:pt-12 {\n    padding-top: 3rem;\n  }\n\n  .tablet\\:pt-16 {\n    padding-top: 4rem;\n  }\n\n  .tablet\\:text-center {\n    text-align: center;\n  }\n\n  .tablet\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .tablet\\:text-\\[14px\\] {\n    font-size: 14px;\n  }\n\n  .tablet\\:text-\\[15px\\] {\n    font-size: 15px;\n  }\n\n  .tablet\\:text-\\[16px\\] {\n    font-size: 16px;\n  }\n\n  .tablet\\:text-\\[18px\\] {\n    font-size: 18px;\n  }\n\n  .tablet\\:text-\\[20px\\] {\n    font-size: 20px;\n  }\n\n  .tablet\\:text-\\[21px\\] {\n    font-size: 21px;\n  }\n\n  .tablet\\:text-\\[26px\\] {\n    font-size: 26px;\n  }\n\n  .tablet\\:text-\\[40px\\] {\n    font-size: 40px;\n  }\n\n  .tablet\\:text-\\[43px\\] {\n    font-size: 43px;\n  }\n\n  .tablet\\:text-\\[52px\\] {\n    font-size: 52px;\n  }\n\n  .tablet\\:font-bold {\n    font-weight: 700;\n  }\n\n  .tablet\\:font-semibold {\n    font-weight: 600;\n  }\n\n  .tablet\\:leading-\\[18\\.9px\\] {\n    line-height: 18.9px;\n  }\n\n  .tablet\\:leading-\\[21px\\] {\n    line-height: 21px;\n  }\n\n  .tablet\\:leading-\\[23\\.8px\\] {\n    line-height: 23.8px;\n  }\n\n  .tablet\\:leading-\\[23px\\] {\n    line-height: 23px;\n  }\n}\r\n\r\n@media (min-width: 320px) and (max-width: 600px) {\n\n  .mobile\\:top-\\[30\\%\\] {\n    top: 30%;\n  }\n\n  .mobile\\:top-\\[65\\%\\] {\n    top: 65%;\n  }\n\n  .mobile\\:mx-0 {\n    margin-left: 0px;\n    margin-right: 0px;\n  }\n\n  .mobile\\:mx-4 {\n    margin-left: 1rem;\n    margin-right: 1rem;\n  }\n\n  .mobile\\:mx-auto {\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .mobile\\:my-2 {\n    margin-top: 0.5rem;\n    margin-bottom: 0.5rem;\n  }\n\n  .mobile\\:mb-2 {\n    margin-bottom: 0.5rem;\n  }\n\n  .mobile\\:mb-24 {\n    margin-bottom: 6rem;\n  }\n\n  .mobile\\:mb-3 {\n    margin-bottom: 0.75rem;\n  }\n\n  .mobile\\:mb-4 {\n    margin-bottom: 1rem;\n  }\n\n  .mobile\\:mb-8 {\n    margin-bottom: 2rem;\n  }\n\n  .mobile\\:mt-1 {\n    margin-top: 0.25rem;\n  }\n\n  .mobile\\:mt-2 {\n    margin-top: 0.5rem;\n  }\n\n  .mobile\\:mt-4 {\n    margin-top: 1rem;\n  }\n\n  .mobile\\:mt-8 {\n    margin-top: 2rem;\n  }\n\n  .mobile\\:mt-\\[29rem\\] {\n    margin-top: 29rem;\n  }\n\n  .mobile\\:\\!h-\\[80\\%\\] {\n    height: 80% !important;\n  }\n\n  .mobile\\:h-4 {\n    height: 1rem;\n  }\n\n  .mobile\\:min-h-\\[100vh\\] {\n    min-height: 100vh;\n  }\n\n  .mobile\\:w-4 {\n    width: 1rem;\n  }\n\n  .mobile\\:max-w-\\[100\\%\\] {\n    max-width: 100%;\n  }\n\n  .mobile\\:max-w-\\[370px\\] {\n    max-width: 370px;\n  }\n\n  .mobile\\:max-w-\\[373px\\] {\n    max-width: 373px;\n  }\n\n  .mobile\\:max-w-\\[90\\%\\] {\n    max-width: 90%;\n  }\n\n  .mobile\\:flex-col {\n    flex-direction: column;\n  }\n\n  .mobile\\:gap-2 {\n    gap: 0.5rem;\n  }\n\n  .mobile\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .mobile\\:rounded-\\[12px\\] {\n    border-radius: 12px;\n  }\n\n  .mobile\\:bg-white {\n    --tw-bg-opacity: 1;\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  }\n\n  .mobile\\:p-0 {\n    padding: 0px;\n  }\n\n  .mobile\\:p-2 {\n    padding: 0.5rem;\n  }\n\n  .mobile\\:p-4 {\n    padding: 1rem;\n  }\n\n  .mobile\\:px-12 {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .mobile\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .mobile\\:py-4 {\n    padding-top: 1rem;\n    padding-bottom: 1rem;\n  }\n\n  .mobile\\:pb-4 {\n    padding-bottom: 1rem;\n  }\n\n  .mobile\\:pb-8 {\n    padding-bottom: 2rem;\n  }\n\n  .mobile\\:pl-4 {\n    padding-left: 1rem;\n  }\n\n  .mobile\\:pr-4 {\n    padding-right: 1rem;\n  }\n\n  .mobile\\:pt-12 {\n    padding-top: 3rem;\n  }\n\n  .mobile\\:pt-32 {\n    padding-top: 8rem;\n  }\n\n  .mobile\\:pt-8 {\n    padding-top: 2rem;\n  }\n\n  .mobile\\:text-center {\n    text-align: center;\n  }\n\n  .mobile\\:text-\\[12px\\] {\n    font-size: 12px;\n  }\n\n  .mobile\\:text-\\[13px\\] {\n    font-size: 13px;\n  }\n\n  .mobile\\:text-\\[14px\\] {\n    font-size: 14px;\n  }\n\n  .mobile\\:text-\\[15px\\] {\n    font-size: 15px;\n  }\n\n  .mobile\\:text-\\[17px\\] {\n    font-size: 17px;\n  }\n\n  .mobile\\:text-\\[18px\\] {\n    font-size: 18px;\n  }\n\n  .mobile\\:text-\\[20px\\] {\n    font-size: 20px;\n  }\n\n  .mobile\\:text-\\[21px\\] {\n    font-size: 21px;\n  }\n\n  .mobile\\:text-\\[22px\\] {\n    font-size: 22px;\n  }\n\n  .mobile\\:text-\\[26px\\] {\n    font-size: 26px;\n  }\n\n  .mobile\\:text-\\[42px\\] {\n    font-size: 42px;\n  }\n\n  .mobile\\:text-\\[50px\\] {\n    font-size: 50px;\n  }\n\n  .mobile\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .mobile\\:text-\\[30px\\] {\n    font-size: 30px;\n  }\n\n  .mobile\\:text-\\[35px\\] {\n    font-size: 35px;\n  }\n\n  .mobile\\:font-bold {\n    font-weight: 700;\n  }\n\n  .mobile\\:font-medium {\n    font-weight: 500;\n  }\n\n  .mobile\\:font-semibold {\n    font-weight: 600;\n  }\n\n  .mobile\\:normal-case {\n    text-transform: none;\n  }\n\n  .mobile\\:leading-\\[16px\\] {\n    line-height: 16px;\n  }\n\n  .mobile\\:leading-\\[17px\\] {\n    line-height: 17px;\n  }\n\n  .mobile\\:leading-\\[18\\.9px\\] {\n    line-height: 18.9px;\n  }\n\n  .mobile\\:leading-\\[18px\\] {\n    line-height: 18px;\n  }\n\n  .mobile\\:leading-\\[20px\\] {\n    line-height: 20px;\n  }\n\n  .mobile\\:leading-\\[21px\\] {\n    line-height: 21px;\n  }\n\n  .mobile\\:leading-\\[22px\\] {\n    line-height: 22px;\n  }\n\n  .mobile\\:leading-\\[28px\\] {\n    line-height: 28px;\n  }\n\n  .mobile\\:leading-\\[51px\\] {\n    line-height: 51px;\n  }\n\n  .mobile\\:leading-\\[54px\\] {\n    line-height: 54px;\n  }\n\n  .mobile\\:shadow-cardsBoxShadow {\n    --tw-shadow: 0px 5.46px 19.11px 0px #00000014;\n    --tw-shadow-colored: 0px 5.46px 19.11px 0px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  }\n\n  @media (min-width: 768px) {\n\n    .mobile\\:md\\:flex {\n      display: flex;\n    }\n  }\n}\r\n\r\n.\\[\\&\\+div\\]\\:text-xs+div {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n\r\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]) {\n  padding-right: 0px;\n}\r\n\r\n.\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\]>[role=checkbox] {\n  --tw-translate-y: 2px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.\\[\\&\\>span\\]\\:line-clamp-1>span {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:size-4>svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n\r\n.\\[\\&\\>svg\\]\\:shrink-0>svg {\n  flex-shrink: 0;\n}\r\n\r\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr {\n  border-bottom-width: 0px;\n}\r\n\r\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n\r\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\r\n\r\n.\\[\\&_svg\\]\\:mx-auto svg {\n  margin-left: auto;\n  margin-right: auto;\n}\r\n\r\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n\r\n.\\[\\&_svg\\]\\:size-6 svg {\n  width: 1.5rem;\n  height: 1.5rem;\n}\r\n\r\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\r\n\r\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child {\n  border-width: 0px;\n}\r\n\r\n.\\[\\&_tr\\]\\:border-b tr {\n  border-bottom-width: 1px;\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;AAQA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAGE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA;;;;AAIA;;;;;AAIF;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAKA;;;;AAIA;;;;;;AAOA;;;;;;AAOA;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;;AAMF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EAEE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAMF;EAEE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EAEE;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;IAEE;;;;;;AAMJ;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA"}}, {"offset": {"line": 4957, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}