(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3352:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,8173,23)),Promise.resolve().then(a.t.bind(a,7970,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,5540)),Promise.resolve().then(a.bind(a,6124))},5540:(e,t,a)=>{"use strict";a.d(t,{default:()=>k});var s=a(5155),r=a(2115),o=a(5565),n=a(1719),i=a(9053),l=a(767),d=a(6710),c=a(1902),u=a(8173),m=a.n(u),p=a(8126),f=a(765),x=a(4471),h=a(6967),g=a(8867),b=a(3565),v=a(7849);let y=x.bL,N=x.l9;x.YJ,x.ZL,x.Pb,x.z6,r.forwardRef((e,t)=>{let{className:a,inset:r,children:o,...n}=e;return(0,s.jsxs)(x.ZP,{ref:t,className:(0,v.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",a),...n,children:[o,(0,s.jsx)(h.A,{className:"ml-auto"})]})}).displayName=x.ZP.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(x.G5,{ref:t,className:(0,v.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})}).displayName=x.G5.displayName;let w=r.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...o}=e;return(0,s.jsx)(x.ZL,{children:(0,s.jsx)(x.UC,{ref:t,sideOffset:r,className:(0,v.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o})})});w.displayName=x.UC.displayName;let j=r.forwardRef((e,t)=>{let{className:a,inset:r,...o}=e;return(0,s.jsx)(x.q7,{ref:t,className:(0,v.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",r&&"pl-8",a),...o})});j.displayName=x.q7.displayName,r.forwardRef((e,t)=>{let{className:a,children:r,checked:o,...n}=e;return(0,s.jsxs)(x.H_,{ref:t,className:(0,v.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:o,...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(x.VF,{children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})}),r]})}).displayName=x.H_.displayName,r.forwardRef((e,t)=>{let{className:a,children:r,...o}=e;return(0,s.jsxs)(x.hN,{ref:t,className:(0,v.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(x.VF,{children:(0,s.jsx)(b.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=x.hN.displayName,r.forwardRef((e,t)=>{let{className:a,inset:r,...o}=e;return(0,s.jsx)(x.JU,{ref:t,className:(0,v.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",a),...o})}).displayName=x.JU.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(x.wv,{ref:t,className:(0,v.cn)("-mx-1 my-1 h-px bg-muted",a),...r})}).displayName=x.wv.displayName;let k=()=>{let[e,t]=(0,r.useState)(!1),[a,u]=(0,r.useState)(!1),x=()=>u(e=>!e),h=()=>{t(!1),u(!1)};return(0,s.jsxs)("div",{className:"absolute left-0 right-0 top-8 md:w-4/5 mx-4 md:mx-auto lg:mx-auto z-20",children:[(0,s.jsx)("nav",{className:"bg-transparent bg-navbarBGPrimary rounded-full backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary px-3 py-2.5 flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsx)(m(),{href:"/",children:(0,s.jsx)(o.default,{src:"/skyline_logo.png",alt:"The Skyline Aviation Club Logo",width:50,height:50,className:"rounded-full"})}),(0,s.jsx)("div",{className:"hidden lg:flex items-center md:gap-3 lg:gap-5 xl:gap-16",children:p.pw.map((e,t)=>"Courses"===e.text?(0,s.jsxs)(y,{onOpenChange:e=>u(e),children:[(0,s.jsxs)(N,{className:"text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2",children:[e.text,(0,s.jsx)(n.A,{className:"w-5 h-5 transition-transform ".concat(a?"rotate-180":"rotate-0")})]}),(0,s.jsx)(w,{className:"bg-navbarBGPrimary backdrop-blur-[20px] shadow-dropdownShadow border-navbarBGPrimary flex flex-col w-42 gap-3 p-4 rounded-xl",children:p.U8.map((e,t)=>(0,s.jsx)(j,{asChild:!0,className:"p-0 focus:bg-transparent hover:text-textBluePrimary",children:(0,s.jsx)(m(),{className:"text-customBlack font-semibold hover:text-textBluePrimary transition block",href:"/course/".concat(e.link),children:e.text})},t))})]},t):(0,s.jsx)(m(),{href:e.link,className:"text-customBlack font-semibold hover:text-textBluePrimary transition flex items-center gap-2",children:e.text},t))}),(0,s.jsxs)(m(),{href:"/enquire",className:"hidden lg:flex items-center gap-2 bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 md:py-2 md:pr-2 md:pl-6 rounded-full border border-transparent",children:["Enquire Now",(0,s.jsx)(i.A,{size:18,className:"ml-2 w-8 h-8 p-1 bg-white rounded-full text-arrowIconColor"})]}),(0,s.jsx)("div",{className:"lg:hidden flex",children:(0,s.jsx)("button",{onClick:()=>{t(e=>!e),u(!1)},type:"button",children:e?(0,s.jsx)(l.A,{size:28}):(0,s.jsx)(d.A,{size:28})})})]})}),(0,s.jsx)("div",{className:"w-full flex flex-col absolute right-0 top-[4.5rem] bg-navbarBGPrimary border-navbarBGPrimary shadow-dropdownShadow backdrop-blur-[20px] rounded-lg transition-all duration-300 ease-in-out overflow-hidden transform ".concat(e?"scale-100 opacity-100 max-h-[600px]":"scale-95 opacity-0 max-h-0 pointer-events-none"),children:p.pw.map((e,t)=>"Courses"===e.text?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:x,className:"p-3 text-customBlack font-semibold w-full flex items-center justify-between",children:["Courses",a?(0,s.jsx)(c.A,{}):(0,s.jsx)(n.A,{})]}),(0,s.jsx)("div",{className:"transition-all duration-300 ease-in-out overflow-hidden ".concat(a?"max-h-96 opacity-100":"max-h-0 opacity-0"),children:(0,s.jsx)("ul",{className:"ml-4",children:f.W.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)(m(),{href:"/course/".concat(e.slug),className:"block text-customBlack font-semibold hover:text-textBluePrimary transition p-2",onClick:h,children:e.courseDesc})},t))})})]},t):(0,s.jsx)(m(),{href:e.link,className:"p-3 text-customBlack font-semibold hover:text-textBluePrimary transition",onClick:h,children:e.text},t))})]})}},6124:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>g});var s=a(5155),r=a(9398),o=a(2115),n=a(9930),i=a(1027),l=a(767),d=a(7849);let c=n.Kq,u=o.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.LM,{ref:t,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...r})});u.displayName=n.LM.displayName;let m=(0,i.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground",success:"success group border-green-500 bg-green-500 text-neutral-50"}},defaultVariants:{variant:"default"}}),p=o.forwardRef((e,t)=>{let{className:a,variant:r,...o}=e;return(0,s.jsx)(n.bL,{ref:t,className:(0,d.cn)(m({variant:r}),a),...o})});p.displayName=n.bL.displayName,o.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.rc,{ref:t,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...r})}).displayName=n.rc.displayName;let f=o.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.bm,{ref:t,className:(0,d.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...r,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});f.displayName=n.bm.displayName;let x=o.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.hE,{ref:t,className:(0,d.cn)("text-sm font-semibold [&+div]:text-xs",a),...r})});x.displayName=n.hE.displayName;let h=o.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.VY,{ref:t,className:(0,d.cn)("text-sm opacity-90",a),...r})});function g(){let{toasts:e}=(0,r.dj)();return(0,s.jsxs)(c,{children:[e.map(function(e){let{id:t,title:a,description:r,action:o,...n}=e;return(0,s.jsxs)(p,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[a&&(0,s.jsx)(x,{children:a}),r&&(0,s.jsx)(h,{children:r})]}),o,(0,s.jsx)(f,{})]},t)}),(0,s.jsx)(u,{})]})}h.displayName=n.VY.displayName},8126:(e,t,a)=>{"use strict";a.d(t,{U8:()=>r,pw:()=>s});let s=[{text:"Home",link:"/"},{text:"Gallery",link:"/gallery"},{text:"Courses",link:"/course"},{text:"Events",link:"/events"},{text:"About Us",link:"/aboutus"},{text:"Contact Us",link:"/enquire"}],r=[{text:"Indian Commercial Pilot",link:"indian-commercial-pilot-licence-course"},{text:"American Commercial Pilot",link:"american-commercial-pilots-licence"},{text:"CPL Package Program",link:"commercial-pilot-licence-package-program"},{text:"Foreign CPL Conversion Course",link:"foreign-commercial-pilot-licence-conversion-course"},{text:"Helicopter Pilot Training",link:"helicopter-commercial-pilot-licence-course"},{text:"American Flight Dispatcher",link:"aircraft-flight-dispatcher-licence-course"},{text:"Radio Telephony Licence",link:"radio-telephony-r-aeromobile-frtol-licence"},{text:"Airhostess/Flight Purser",link:"airhostess-flight-purser-training-course"},{text:"Airport Ground Staff",link:"airport-ground-staff-course"},{text:"Aviation Foundation Course",link:"aviation-foundation-course"},{text:"Aeroplane/Helicopter Training Workshop",link:"two-day-aeroplane-or-helicopter-training-workshop"}]},9398:(e,t,a)=>{"use strict";a.d(t,{dj:()=>m});var s=a(2115);let r=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,a=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},7849:(e,t,a)=>{"use strict";a.d(t,{cn:()=>o});var s=a(3463),r=a(9795);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},347:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[690,565,181,173,579,966,929,765,441,517,358],()=>t(3352)),_N_E=e.O()}]);