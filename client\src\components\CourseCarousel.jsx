"use client";
import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Image from "next/image";
import { coursesData } from "@/constant/landingPageData";
import Link from "next/link";

const CourseCarousel = () => {
  const [api, setApi] = useState();
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setSelectedIndex(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setSelectedIndex(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const scrollTo = (index) => {
    if (api) {
      api.scrollTo(index);
    }
  };
  return (
    <div className="md:w-11/12 lg:w-4/5 mx-auto">
      <Carousel
        setApi={setApi}
        className="shadow-cardsBoxShadow bg-white"
        opts={{
          loop: true,
        }}
      >
        <CarouselContent >
          {coursesData.map((item, index) => (
            <CarouselItem className="h-fit" key={index}>
              <Card>
                <CardContent className="flex flex-col md:flex-row items-center gap-3 md:gap-6 p-3 md:p-5 rounded-[20px]">
                  <Image
                    className="rounded-lg w-full h-[330px] md:w-[208px] md:h-[400px] md:basis-[40%] object-cover"
                    src={item.image}
                    width={360}
                    height={438}
                    alt="cardImage"
                  />
                  <div className="flex flex-col md:gap-2 md:basis-[60%]">
                    <h3 className="text-textBluePrimary font-bold mb-1 md:mb-0 text-base lg:text-xl">
                      {(item.subTitle).toUpperCase()}
                    </h3>
                    <h4 className="text-customBlack font-semibold mb-2 md:mb-0 text-sm leading-[20px] desktop:text-sm lg:text-lg">
                      {item.title}
                    </h4>
                    <p className="font-medium text-customBlack mb-2 md:mb-0 text-xs desktop:text-sm lg:text-base">
                      {item.desc}
                    </p>
                    {
                      item.topics && (
                        <div>
                          <span className="text-sm text-customBlack text-light mb-1 block">
                            You&apos;ll Learn:
                          </span>
                          {item.topics}
                        </div>
                      )
                    }
                    <Link
                      href={`/course/${item.slug}`}
                      className="flex  gap-2 bg-buttonBGPrimary font-bold text-sm text-white py-2 px-6 w-fit  justify-between rounded-full border border-transparent mt-4"
                    >
                      Learn More
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className=" hidden md:flex border bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6 [&_svg]:mx-auto" />
        <CarouselNext className="hidden md:flex border bg-white shadow-cardButtonShadow w-8 h-8 [&_svg]:size-6 [&_svg]:mx-auto" />
      </Carousel>
      <div className="flex justify-center items-center mt-4 space-x-2">
        {coursesData.map((_, index) => (
          <button
            key={index}
            onClick={() => scrollTo(index)}
            className={`${selectedIndex === index + 1
              ? "h-3 w-8 rounded-full bg-textBluePrimary" // Active dot
              : "h-3 w-3 rounded-full bg-carouselDotsBG" // Inactive dots
              }`}
          />
        ))}
      </div>
    </div>
  );
};

export default CourseCarousel;
