@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --whiteOne: #FCFCF4;
    --textBluePrimary: #1420CD;
    --customGrey100: #7D8175;
    --customBlack: #222222;
    --descLightBlack: #080E5E;
    --neutralGrayText: #212529;
    --textGreyPrimary: #464C3B;
    --boxShadow: 0px 8px 30px 0px #00000014;
    --footerBG: #F3F5FF;
    --footerPrimaryText: #0E1795;
    --blue900: #0C1483;
    --fullStarBG: #F2C94C;
    --halfStarBG: #F2C94C80;
    --emptyStarBG: #B2B2B2;
    --whyChoosePara: #323232;
    --arrowIconColor: #1865C1;
    --formTextColor: #3D4852;
    --formTextOptionColor: #808080;
    --formBG: #F5F5F5;
    --formInputColor: #ACB1B6;
    --formLabelSubColor: #A81F00;
    --carouselDotsBG: #E0E1DE;
    --accordionBG: #D6E6FF70;
    --footerBottomText: #9d9d9d;
    --black: #000000;
    --formPara: #616557;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Add this to your global CSS or a module */
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: #1a1a2e;
  /* dark background */
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;  
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: #3b82f6;
  /* Tailwind's blue-500 */
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: #2563eb;
  /* Tailwind's blue-600 */
}