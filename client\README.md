
## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

# Git commands

Follow below methods while commiting code.

```
-Feat – feature
-Fix – bug fixes
-Docs – changes to the documentation like README
-Style – style or formatting change 
-Perf – improves code performance
-Test – test a feature
```

# Installing shadcn components

While installing shadcn component if it ask to choose any one option from below:

```
-Use --force
-Use --legacy-peer-deps
```

always choose 2nd option which is Use --legacy-peer-deps.