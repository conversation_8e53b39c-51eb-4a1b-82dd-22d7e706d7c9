{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/constant/galleryData.jsx"], "sourcesContent": ["const tabButtons = [\r\n  \"Captain <PERSON>\",\r\n  \"Celebrities\",\r\n  \"Events\",\r\n  \"TV and Press\",\r\n  \"Awards\",\r\n  \"VVIP Testimonials\",\r\n  \"Government Approvals\",\r\n];\r\n\r\nconst tabSubButtons = [\r\n  \"All Albums\",\r\n  \"Capt AD Manek Album\",\r\n  \"Celebrities Album\",\r\n  \"Events Album\",\r\n];\r\n\r\nconst albumData = [\r\n\r\n  // Captain Manek Album\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/3.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/5.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/6.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Captain<PERSON>anek/7.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/8.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/10.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/11.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/12.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/13.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/14.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/15.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/16.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/17.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/19.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/20.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/21.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/22.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/23.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/24.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/CaptainManek/25.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 1,\r\n  },\r\n\r\n  // Celebrities Album\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/2.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/4.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/5.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/6.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/7.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/8.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/9.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/10.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/11.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/12.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/13.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/14.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/15.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/16.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/17.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/18.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/19.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/20.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/21.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/22.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/23.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/24.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/25.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/27.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/28.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/29.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/30.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/31.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/32.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/33.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/34.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/35.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/36.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/37.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/38.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Celebs/39.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 2,\r\n  },\r\n\r\n\r\n  // Events Album\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventOne.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventTwo.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventThree.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventFour.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventFive.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventSix.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventSeven.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventEight.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventNine.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventTen.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventEleven.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventTwelve.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventThirteen.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventFourteen.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventFifteen.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/album/Events/eventSixteen.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n    subTab: 3,\r\n  },\r\n];\r\n\r\n\r\nconst tvImageData = [\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/2.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/3.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/4.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/5.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/6_pepar.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/7_Ham_Radio.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/8_aviators1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/Amrut_Manek_Sandesh_Story.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/bombay-times.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/airline_news_shibu.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/Captain_article.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/indian_express.png\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/ia1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/citizen.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/divyabhaskar_image1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/Picture9.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/Sadbhavana_Book-1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/gjsir.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/AD_Manek_News.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/ento1.png\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/ento2.png\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/IMG_0001_4.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Tv_And_Press/gmitra2.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 0,\r\n  },\r\n];\r\n\r\nconst Awards = [\r\n  {\r\n    image: \"/assets/gallery/Awards/1.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/2_j9qwon.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/3_ptig9x.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/4_s14bwz.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/5_qqs3lw.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/6_p3scqs.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/7_qsdmyv.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/8_svckpi.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/9_ebuc6l.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/10_hqyuzb.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/11_p3jbv3.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Awards/prideofgujarat_2_gvkmdv.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 2,\r\n  }\r\n];\r\n\r\nconst VVIP = [\r\n  {\r\n    image: \"/assets/gallery/VVIP/aeronautical_society.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/anandiben_patel.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/aw4.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/aw14.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/cmletter.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/gopalletter.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/jrdTata.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/lionsclub.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/modi.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/modiletter25.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/rajiv_gandhi.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/rkpant.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/sanjay.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/school.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/shivajirao_Deshmukh.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/tushar.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/vasant_purke.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/vasavaletter.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/vilasrao.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/VVIP/vinod.jpg\",\r\n    title: \"\",\r\n    msg: \"\",\r\n    tab: 3,\r\n  }\r\n];\r\n\r\nconst GovermentApprovals = [\r\n  {\r\n    image: \"/assets/gallery/Goverments/govt2.jpg\",\r\n    title: \"DGCA - INDIA\",\r\n    msg: \"Letter of Intimation from DGCA Govt. of India, New Delhi\",\r\n    tab: 4,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Goverments/pilot_license_tefp7d.jpg\",\r\n    title: \"FAA - USA\",\r\n    msg: \"American Aviation (FAA) Ground Training Authorization Certificate\",\r\n    tab: 4,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Goverments/govt1.jpg\",\r\n    title: \"Registration\",\r\n    msg: \"Government of India Registration Certificate\",\r\n    tab: 4,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Goverments/govt3_vdzpmf.jpg\",\r\n    title: \"Govt. India Wireless Licence\",\r\n    msg: \"Club Licence, Government of India\",\r\n    tab: 4,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Goverments/qci.jpg\",\r\n    title: \"Quality Council of India\",\r\n    msg: \"QCI Organisational Member Certificate\",\r\n    tab: 4,\r\n  },\r\n  {\r\n    image: \"/assets/gallery/Goverments/govt5.jpg\",\r\n    title: \"Trademark\",\r\n    msg: \"Trademark Registration Certificate - The Skyline Aviation Club\",\r\n    tab: 4,\r\n  }\r\n];\r\n\r\n\r\nexport { tabButtons, tabSubButtons, albumData, tvImageData, Awards, VVIP, GovermentApprovals };\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;CACD;AAED,MAAM,YAAY;IAEhB,sBAAsB;IACtB;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IAEA,oBAAoB;IACpB;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IAGA,eAAe;IACf;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,QAAQ;IACV;CACD;AAGD,MAAM,cAAc;IAClB;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;CACD;AAED,MAAM,SAAS;IACb;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;CACD;AAED,MAAM,OAAO;IACX;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;CACD;AAED,MAAM,qBAAqB;IACzB;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;IACA;QACE,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;IACP;CACD"}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/ui/dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-[90%] md:w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <DialogPrimitive.Close\r\n        className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)}\r\n    {...props} />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props} />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,gBAAgB,mKAAgB,OAAO;AAE7C,MAAM,eAAe,mKAAgB,MAAM;AAE3C,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAEb,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,sMAAM,UAAU,CAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,mKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0gBACA;gBAED,GAAG,KAAK;;oBACR;kCACD,8OAAC,mKAAgB,KAAK;wBACpB,WAAU;;0CACV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;AAEb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;AAEb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,sMAAM,UAAU,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,sMAAM,UAAU,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,8OAAC,mKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,kBAAkB,WAAW,GAAG,mKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Skyline-Aviation/client/src/components/GalleryComponent.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  albumData,\r\n  tvImageData,\r\n  Awards,\r\n  VVIP,\r\n  tabButtons,\r\n  GovermentApprovals,\r\n} from \"../constant/galleryData\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from \"@/components/ui/dialog\";\r\nimport { useEffect, useState, useRef, Suspense } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { useSearchParams, useRouter } from \"next/navigation\";\r\n\r\nconst tabSlugs = [\r\n  \"captain-ad-manek\",\r\n  \"celebrities\",\r\n  \"events\",\r\n  \"tv-and-press\",\r\n  \"awards\",\r\n  \"vvip-testimonials\",\r\n  \"government-approvals\",\r\n];\r\n\r\n// ✅ Reusable Section Component\r\nconst GallerySection = ({ data }) => (\r\n  <div className=\"grid grid-cols-2 sm:grid-cols-[repeat(auto-fit,_minmax(200px,_1fr))] gap-4 px-0 md:px-4 py-6 justify-items-center\">\r\n    {data.map((item, index) => (\r\n      <Dialog key={index}>\r\n        <div className=\"relative w-full max-w-[250px] aspect-square bg-gray-200 cursor-pointer rounded-md overflow-hidden\">\r\n          <DialogTrigger asChild>\r\n            <Image\r\n              src={item.image}\r\n              alt={item.title || `Image ${index + 1}`}\r\n              fill\r\n              quality={100}\r\n              className=\"object-cover hover:scale-105 transition-transform duration-300\"\r\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 250px\"\r\n              priority\r\n            />\r\n          </DialogTrigger>\r\n          <DialogOverlay className=\"fixed inset-0 bg-black/90 backdrop-blur-md transition-all duration-300\" />\r\n          <DialogContent className=\"max-h-[90vh] overflow-y-auto p-4 rounded-lg bg-white\">\r\n            <DialogHeader>\r\n              <DialogTitle className=\"mb-1 font-semibold text-lg\">\r\n                {item.title}\r\n              </DialogTitle>\r\n              <DialogDescription className=\"font-normal text-lg mb-4\">\r\n                {item.msg}\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            <div className=\"flex justify-center items-center\">\r\n              <Image\r\n                src={item.image}\r\n                alt={item.title || `Image ${index + 1}`}\r\n                width={800}\r\n                height={600}\r\n                quality={100}\r\n                className=\"max-h-[60vh] w-auto h-auto object-contain\"\r\n              />\r\n            </div>\r\n          </DialogContent>\r\n        </div>\r\n      </Dialog>\r\n    ))}\r\n  </div>\r\n);\r\n\r\n// ✅ Separate component for search params logic\r\nconst GalleryWithSearchParams = () => {\r\n  const [activeTab, setActiveTab] = useState(1);\r\n  const [hasScrolledFromURL, setHasScrolledFromURL] = useState(false);\r\n  const searchParams = useSearchParams();\r\n  const galleryRef = useRef(null);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const slug = searchParams.get(\"activeTab\");\r\n    if (slug) {\r\n      const index = tabSlugs.indexOf(slug);\r\n      if (index !== -1) {\r\n        setActiveTab(index + 1);\r\n        \r\n        // Only scroll if this is from URL redirect (not from tab click)\r\n        // and we haven't already scrolled from URL in this session\r\n        if (!hasScrolledFromURL && galleryRef.current) {\r\n          galleryRef.current.scrollIntoView({ behavior: \"smooth\" });\r\n          setHasScrolledFromURL(true);\r\n        }\r\n      }\r\n    }\r\n  }, [searchParams, hasScrolledFromURL]);\r\n\r\n  return (\r\n    <div\r\n      className=\"p-6 lg:py-8 lg:px-[3rem] lg:relative\"\r\n      ref={galleryRef}\r\n    >\r\n      <div className=\"items-baseline gap-2 mb-8 flex-col xl:flex-row\">\r\n        <div className=\"hidden lg:block absolute bg-dotsImage top-40 left-0 bg-center bg-no-repeat w-10 h-36\" />\r\n\r\n        <div className=\"py-2 md:py-4 xl:flex xl:items-center w-full\">\r\n          <h3 className=\"text-textBluePrimary text-xl font-bold lg:text-3xl mr-4\">\r\n            Categories : Album\r\n          </h3>\r\n        </div>\r\n        {/* ✅ Card-style Category Tabs */}\r\n        <div className=\"flex flex-col gap-4 w-full \">\r\n          <div className=\"flex flex-wrap gap-3 justify-start\">\r\n            {tabButtons.map((tab, index) => (\r\n              <button\r\n                key={index}\r\n                className={`px-2 py-1 text-sm rounded-3xl border font-semibold shadow-sm transition-all\r\n                  ${\r\n                    activeTab === index + 1\r\n                      ? \"bg-buttonBGPrimary text-white border-transparent\"\r\n                      : \"bg-white text-textBluePrimary border-textBluePrimary hover:bg-buttonBGPrimary hover:text-white\"\r\n                  }`}\r\n                onClick={() => {\r\n                  const newTabIndex = index + 1;\r\n                  setActiveTab(newTabIndex);\r\n                  // Update URL without triggering scroll\r\n                  router.push(\r\n                    `/gallery?activeTab=${tabSlugs[newTabIndex - 1]}`,\r\n                    { scroll: false }\r\n                  );\r\n                }}\r\n              >\r\n                {tab}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* ✅ Gallery Sections */}\r\n      {activeTab === 1 && (\r\n        <GallerySection\r\n          data={albumData.filter((item) => item.subTab === 1)}\r\n        />\r\n      )}\r\n      {activeTab === 2 && (\r\n        <GallerySection\r\n          data={albumData.filter((item) => item.subTab === 2)}\r\n        />\r\n      )}\r\n      {activeTab === 3 && (\r\n        <GallerySection\r\n          data={albumData.filter((item) => item.subTab === 3)}\r\n        />\r\n      )}\r\n      {activeTab === 4 && <GallerySection data={tvImageData} />}\r\n      {activeTab === 5 && <GallerySection data={Awards} />}\r\n      {activeTab === 6 && <GallerySection data={VVIP} />}\r\n      {activeTab === 7 && <GallerySection data={GovermentApprovals} />}\r\n    </div>\r\n  );\r\n};\r\n\r\n// ✅ Loading fallback component\r\nconst GalleryLoading = () => (\r\n  <div className=\"p-6 lg:py-8 lg:px-[3rem]\">\r\n    <div className=\"animate-pulse\">\r\n      <div className=\"h-8 bg-gray-200 rounded w-64 mb-8\"></div>\r\n      <div className=\"flex flex-wrap gap-3 mb-8\">\r\n        {Array.from({ length: 7 }).map((_, i) => (\r\n          <div key={i} className=\"h-8 bg-gray-200 rounded-3xl w-24\"></div>\r\n        ))}\r\n      </div>\r\n      <div className=\"grid grid-cols-2 sm:grid-cols-[repeat(auto-fit,_minmax(200px,_1fr))] gap-4\">\r\n        {Array.from({ length: 6 }).map((_, i) => (\r\n          <div key={i} className=\"aspect-square bg-gray-200 rounded-md animate-pulse\"></div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst GalleryComponent = () => {\r\n  return (\r\n    <>\r\n      {/* Banner */}\r\n      <div className=\"relative bg-galleryBanner bg-cover bg-center h-[700px]\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]\" />\r\n        <div className=\"absolute left-0 right-0 bottom-12 lg:bottom-24 lg:w-4/5 lg:mx-auto mx-4 text-center\">\r\n          <h1 className=\"font-extrabold text-whiteOne text-5xl lg:text-6xl mb-4\">\r\n            Gallery\r\n          </h1>\r\n          <p className=\"text-whiteOne font-bold text-sm md:text-xl\">\r\n            Explore The Skyline Aviation Club&apos;s journey through our\r\n            Gallery, featuring training moments, VVIP testimonials, and award\r\n            recognitions. Each album showcases the achievements, milestones, and\r\n            trust we&apos;ve built in the aviation industry.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tabs & Filters wrapped in Suspense */}\r\n      <Suspense fallback={<GalleryLoading />}>\r\n        <GalleryWithSearchParams />\r\n      </Suspense>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default GalleryComponent;"], "names": [], "mappings": ";;;;AAEA;AAQA;AASA;AACA;AACA;AArBA;;;;;;;AAuBA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,+BAA+B;AAC/B,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAE,iBAC9B,8OAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC,kIAAA,CAAA,SAAM;0BACL,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,gBAAa;4BAAC,OAAO;sCACpB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,KAAK;gCACf,KAAK,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG;gCACvC,IAAI;gCACJ,SAAS;gCACT,WAAU;gCACV,OAAM;gCACN,QAAQ;;;;;;;;;;;sCAGZ,8OAAC,kIAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC,kIAAA,CAAA,gBAAa;4BAAC,WAAU;;8CACvB,8OAAC,kIAAA,CAAA,eAAY;;sDACX,8OAAC,kIAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,KAAK,KAAK;;;;;;sDAEb,8OAAC,kIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC1B,KAAK,GAAG;;;;;;;;;;;;8CAGb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,KAAK;wCACf,KAAK,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG;wCACvC,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,WAAU;;;;;;;;;;;;;;;;;;;;;;;eA9BP;;;;;;;;;;AAwCnB,+CAA+C;AAC/C,MAAM,0BAA0B;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,IAAI,MAAM;YACR,MAAM,QAAQ,SAAS,OAAO,CAAC;YAC/B,IAAI,UAAU,CAAC,GAAG;gBAChB,aAAa,QAAQ;gBAErB,gEAAgE;gBAChE,2DAA2D;gBAC3D,IAAI,CAAC,sBAAsB,WAAW,OAAO,EAAE;oBAC7C,WAAW,OAAO,CAAC,cAAc,CAAC;wBAAE,UAAU;oBAAS;oBACvD,sBAAsB;gBACxB;YACF;QACF;IACF,GAAG;QAAC;QAAc;KAAmB;IAErC,qBACE,8OAAC;QACC,WAAU;QACV,KAAK;;0BAEL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;;;;;;kCAK1E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,+HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,KAAK,sBACpB,8OAAC;oCAEC,WAAW,CAAC;kBACV,EACE,cAAc,QAAQ,IAClB,qDACA,kGACJ;oCACJ,SAAS;wCACP,MAAM,cAAc,QAAQ;wCAC5B,aAAa;wCACb,uCAAuC;wCACvC,OAAO,IAAI,CACT,CAAC,mBAAmB,EAAE,QAAQ,CAAC,cAAc,EAAE,EAAE,EACjD;4CAAE,QAAQ;wCAAM;oCAEpB;8CAEC;mCAjBI;;;;;;;;;;;;;;;;;;;;;YAyBd,cAAc,mBACb,8OAAC;gBACC,MAAM,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;;;;;;YAGpD,cAAc,mBACb,8OAAC;gBACC,MAAM,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;;;;;;YAGpD,cAAc,mBACb,8OAAC;gBACC,MAAM,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM,KAAK;;;;;;YAGpD,cAAc,mBAAK,8OAAC;gBAAe,MAAM,+HAAA,CAAA,cAAW;;;;;;YACpD,cAAc,mBAAK,8OAAC;gBAAe,MAAM,+HAAA,CAAA,SAAM;;;;;;YAC/C,cAAc,mBAAK,8OAAC;gBAAe,MAAM,+HAAA,CAAA,OAAI;;;;;;YAC7C,cAAc,mBAAK,8OAAC;gBAAe,MAAM,+HAAA,CAAA,qBAAkB;;;;;;;;;;;;AAGlE;AAEA,+BAA+B;AAC/B,MAAM,iBAAiB,kBACrB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;8BAGd,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;4BAAY,WAAU;2BAAb;;;;;;;;;;;;;;;;;;;;;AAOpB,MAAM,mBAAmB;IACvB,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;;;;;;;0BAU9D,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC;;;;;0BACnB,cAAA,8OAAC;;;;;;;;;;;;AAIT;uCAEe"}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}