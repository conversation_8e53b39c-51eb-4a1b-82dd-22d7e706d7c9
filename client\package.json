{"name": "sky-aviation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.1", "lucide-react": "^0.469.0", "next": "15.1.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sky-aviation": "file:", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8", "tailwindcss": "^3.4.1"}}