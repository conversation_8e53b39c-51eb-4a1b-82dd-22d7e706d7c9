(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{9200:(e,t,s)=>{Promise.resolve().then(s.bind(s,3698))},6046:(e,t,s)=>{"use strict";var l=s(6658);s.o(l,"useParams")&&s.d(t,{useParams:function(){return l.useParams}}),s.o(l,"useRouter")&&s.d(t,{useRouter:function(){return l.useRouter}}),s.o(l,"useSearchParams")&&s.d(t,{useSearchParams:function(){return l.useSearchParams}})},3698:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var l=s(5155),a=s(5565),c=s(765),r=s(8173),x=s.n(r),i=s(6046),n=s(2115),m=s(4351);let d=()=>{let[e,t]=(0,n.useState)(null),s=(0,n.useRef)(null),[r,d]=(0,n.useState)(null),{slug:o}=(0,i.useParams)(),h=c.W.find(e=>e.slug===o);return h?((0,n.useEffect)(()=>{if(o){let e=m.pU.find(e=>e.slug===o);d(e),console.log(e),console.log(e.image)}},[o]),(0,n.useEffect)(()=>{function l(e){s.current&&!s.current.contains(e.target)&&t(!1)}return e&&document.addEventListener("mousedown",l),()=>{document.removeEventListener("mousedown",l)}},[e]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{style:{backgroundImage:r?"url(".concat(null==r?void 0:r.bgImage,")"):"none"},className:"relative bg-cover bg-no-repeat  bg-center h-[500px] md:h-[700px]",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-black/40 to-transparent z-[-1]"}),h?(0,l.jsxs)("div",{className:"absolute left-0 right-0 bottom-28 md:bottom-32 mx-4 md:mx-20 text-center",children:[(0,l.jsx)("h1",{className:"font-extrabold text-whiteOne text-2xl md:text-6xl lg:text-6xl xl:text-6xl mb-4",children:h.courseDesc.toUpperCase()}),(0,l.jsx)("p",{className:"text-formBG font-bold md:font-light text-xl md:text-3xl lg:text-2xl lg:w-[32rem] lg:mx-auto",children:h.courseName.toUpperCase()})]}):""]}),(0,l.jsxs)("div",{className:"relative z-0 bg-white",children:[(0,l.jsx)("div",{className:"hidden lg:block z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute top-80 left-0 right-0 w-full h-[300px]"}),(0,l.jsx)("div",{className:"hidden lg:block absolute bg-dotsImage top-36 right-0 bg-center bg-no-repeat w-10 h-36"}),(0,l.jsx)("div",{className:"z-[-1] bg-spiralImage bg-no-repeat bg-cover bg-center absolute bottom-0 lg:bottom-60 left-0 right-0 w-full h-[300px]"}),(0,l.jsx)("div",{className:"hidden lg:block absolute bg-dotsImage top-[48rem] left-0 bg-center bg-no-repeat w-10 h-36"}),(0,l.jsxs)("div",{className:"py-4 px-4 lg:px-12 xl:px-32 relative z-10",children:[(0,l.jsxs)("div",{className:"absolute left-0 right-0 -top-20 mx-4 text-center p-3 md:p-4 lg:p-8 lg:mx-36 xl:p-8 rounded-xl shadow-courseCardShadow bg-white",children:[(0,l.jsx)("h3",{className:"text-textBluePrimary mb-2 font-bold text-xl md:text-2xl xl:text-3xl lg:text-3xl",children:"Introduction"}),(0,l.jsx)("p",{className:"descLightBlack text-xs leading-5   lg:text-base xl:text-base font-medium lg:mx-32",children:h.introduction})]}),(0,l.jsxs)("div",{className:"shadow-cardButtonShadow lg:mt-32 xl:mt-44 md:mt-32 mt-24 p-4 md:shadow-none mb-8",children:[(0,l.jsx)("h3",{className:"text-textBluePrimary text-center mb-4 md:mb-6 font-bold text-xl md:text-2xl lg:text-3xl",children:"Eligibility"}),(0,l.jsxs)("div",{className:"grid gap-2 md:gap-4 lg:gap-6 ".concat(3===h.eligibilityData.length?"grid-cols-1 sm:grid-cols-3":"grid-cols-2 md:grid-cols-4"," w-full"),children:[h.eligibilityData.map((e,s)=>(0,l.jsxs)("div",{className:"flex flex-col items-center p-2 md:p-4 rounded-xl shadow-xl bg-white",children:[(0,l.jsx)(a.default,{src:e.image,alt:"".concat(e.title," Icon"),className:"".concat(e.class," mb-2 md:lg-4"),width:100,height:100}),(0,l.jsx)("h3",{className:"font-semibold text-center text-sm md:text-base text-neutralGrayText",children:e.title}),"Education"===e.title?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"text-center text-sm md:text-base text-neutralGrayText",children:e.desc}),(0,l.jsx)("button",{onClick:()=>t("Education"),className:"mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm",children:"Note"})]}):"Medical"===e.title?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"text-center text-sm md:text-base text-neutralGrayText",children:e.desc}),(0,l.jsx)("button",{onClick:()=>t("Medical"),className:"mt-2 text-[--textBluePrimary] hover:text-blue-700 underline text-sm",children:"Note"})]}):(0,l.jsx)("p",{className:"text-center text-sm md:text-base text-neutralGrayText",children:e.desc})]},s)),e&&(0,l.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50 px-4 md:px-6",style:{background:"rgba(0,0,0,0.3)"},children:(0,l.jsxs)("div",{ref:s,className:"bg-white rounded-xl p-4 md:p-6 shadow-lg relative ".concat("Scholarship"===e?"max-w-4xl w-full max-h-[90vh] overflow-y-auto":"max-w-md"),children:[(0,l.jsx)("button",{onClick:()=>t(null),className:"absolute right-4 top-4 text-gray-500 hover:text-gray-700 text-xl font-bold z-10",children:"\xd7"}),"Scholarship"===e?(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl md:text-2xl lg:text-3xl font-bold text-textBluePrimary mb-4 md:mb-6 text-center md:text-left",children:"Scholarship Details"}),h.theoryTrainingData.length>0&&(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h3",{className:"font-bold text-lg md:text-xl text-customBlack mb-3",children:"Scholarships for Theory Training"}),(0,l.jsx)("ul",{className:"space-y-3",children:h.theoryTrainingData.map((e,t)=>(0,l.jsxs)("li",{className:"border-b border-gray-200 pb-3 last:border-b-0",children:[(0,l.jsx)("h4",{className:"text-base md:text-lg font-semibold text-customBlack mb-2",children:e.title}),(0,l.jsx)("p",{className:"font-medium text-sm md:text-base text-gray-700",children:e.description})]},t))})]}),h.practicalTrainingData.length>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-bold text-lg md:text-xl text-customBlack mb-3",children:"Scholarships for Practical Flight Training"}),(0,l.jsx)("ul",{className:"space-y-3",children:h.practicalTrainingData.map((e,t)=>(0,l.jsxs)("li",{className:"border-b border-gray-200 pb-3 last:border-b-0",children:[(0,l.jsx)("h4",{className:"text-base md:text-lg font-semibold text-customBlack mb-2",children:e.title}),(0,l.jsx)("p",{className:"font-medium text-sm md:text-base text-gray-700",children:e.description})]},t))})]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Important Note"}),"Education"===e&&(0,l.jsx)("p",{className:"text-sm text-gray-700",children:"If not from science background, then you require to appear and pass Physics and Mathematics subjects of 12th Level conducted by NIOS, New Delhi. Students can pursue our training program and simultaneously appear for above mentioned exams. Please contact us for further information."}),"Medical"===e&&(0,l.jsx)("p",{className:"text-sm text-gray-700",children:"We Will arrange Medical Examination with our panel doctor who is DGCA approved Class II Medical Examiner"})]})]})})]})]}),(0,l.jsxs)("div",{className:"flex gap-4 mb-8 flex-col md:flex-row",children:[(0,l.jsxs)("div",{className:"bg-white shadow-courseCardShadow p-4 md:p-6 rounded-[20px] md:basis-2/5",children:[(0,l.jsx)("h2",{className:"text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center md:text-left lg:text-left xl:text-left font-bold text-textBluePrimary mb-4",children:"Course Structure"}),(0,l.jsx)("ul",{className:"space-y-4 md:pl-3 md:marker:text-2xl marker:font-bold md:list-decimal",children:(null==h?void 0:h.courseStructure)&&h.courseStructure.map((e,t)=>(0,l.jsxs)("li",{className:"md:ml-4",children:[(0,l.jsx)("h3",{className:"font-bold text-base lg:text-2xl text-customBlack mb-2",children:e.title}),(0,l.jsxs)("p",{className:"font-bold text-base lg:text-xl text-customBlack flex  gap-1 mb-2",children:["Course Duration:",(0,l.jsx)("span",{className:"font-medium text-base md:text-xl",children:e.courseDuration})]}),e.parts.map((e,t)=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("p",{className:"font-normal text-baxe lg:text-lg text-customBlack flex flex-col gap-1 mb-2",children:[e.title,(0,l.jsx)("span",{className:"font-medium text-base md:text-lg",children:e.duration})]},t),e.papers&&(0,l.jsx)("ul",{className:"space-y-1",children:e.papers.map((e,t)=>(0,l.jsx)("li",{className:"font-small text-base md:text-lg",children:e},t))})]})),h.subCourseStructure&&h.subCourseStructure.map((e,t)=>(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-bold text-base lg:text-xl text-customBlack flex flex-col gap-1 mb-2",children:e.title}),e.subjects&&(0,l.jsx)("ul",{className:"space-y-1",children:e.subjects.map((e,t)=>(0,l.jsx)("li",{className:"font-medium text-base md:text-lg",children:e},t))}),e.licence&&(0,l.jsx)("h4",{className:"font-bold text-base lg:text-lg text-customBlack flex flex-col gap-1 mb-2",children:e.licence}),e.parts&&(0,l.jsx)("ul",{className:"space-y-1",children:e.parts.map((e,t)=>(0,l.jsx)("li",{className:"font-medium text-base md:text-lg",children:e},t))})]},t))]},"course-".concat(t)))}),h.visaAsistant&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h3",{className:"font-bold text-base text-customBlack mb-2",children:"Student Visa Assistant"}),(0,l.jsx)("p",{className:"mt-4 font-medium text-md text-customBlack mb-4",children:h.visaAsistant})]}),h.shortNote&&(0,l.jsx)("p",{className:"mt-4 font-medium text-md text-customBlack mb-4",children:h.shortNote}),(0,l.jsx)(x(),{href:"/enquire",className:"block text-center bg-buttonBGPrimary text-sm font-bold text-white py-3 px-4 mt-3 lg:text-lg rounded-full border border-transparent",children:"Enquire Now"})]}),(0,l.jsxs)("div",{className:"flex flex-col gap-6 md:basis-3/5",children:[(h.theoryTrainingData.length||h.practicalTrainingData.length)>0&&(0,l.jsxs)("div",{className:"bg-white shadow-lg p-6 rounded-[20px]",children:[(0,l.jsx)("h2",{className:"text-center md:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4",children:"Scholarship"}),(0,l.jsx)("div",{children:h.theoryTrainingData.length>0&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h3",{className:"font-bold text-lg text-customBlack mb-2",children:"Scholarships for Theory Training"}),(0,l.jsx)("ul",{className:"space-y-2",children:h.theoryTrainingData.slice(0,2).map((e,t)=>(0,l.jsxs)("li",{children:[(0,l.jsx)("h3",{className:" text-base font-semibold text-customBlack mb-1",children:e.title}),(0,l.jsx)("p",{className:"font-medium text-xs lg:text-sm",children:e.description})]},t))})]})}),h.practicalTrainingData.length>0&&(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("h3",{className:"font-bold text-lg text-customBlack mb-2",children:"Scholarships for Practical Flight Training"}),(0,l.jsx)("ul",{className:"space-y-2",children:h.practicalTrainingData.slice(0,2).map((e,t)=>(0,l.jsxs)("li",{children:[(0,l.jsx)("h3",{className:" text-base font-semibold text-customBlack mb-1",children:e.title}),(0,l.jsx)("p",{className:"font-medium text-xs lg:text-sm",children:e.description})]},t))})]}),(h.theoryTrainingData.length>2||h.practicalTrainingData.length>2)&&(0,l.jsx)("div",{className:"mt-4 text-center",children:(0,l.jsx)("button",{onClick:()=>t("Scholarship"),className:"text-textBluePrimary hover:text-blue-700 font-semibold text-sm md:text-base underline",children:"...more"})})]}),h.careerProspects.length>0&&(0,l.jsxs)("div",{className:"bg-white shadow-lg p-6 rounded-[20px] col-span-full",children:[(0,l.jsx)("h2",{className:"text-center md:text-left lg:text-left xl:text-left text-xl md:text-2xl lg:text-3xl xl:text-3xl font-bold text-textBluePrimary mb-4",children:"Career Prospects"}),(0,l.jsx)("ul",{className:"space-y-1 md:space-y-2",children:h.careerProspects.map((e,t)=>(0,l.jsxs)("li",{children:[(0,l.jsx)("h3",{className:"font-semibold text-base lg:text-xl text-customBlack mb-1 md:mb-2",children:e.title}),(0,l.jsx)("p",{className:"font-medium text-xs lg:text-sm",children:e.description})]},t))})]})]})]}),h.jobAssistanceData&&(0,l.jsxs)("div",{className:"bg-white shadow-courseCardShadow rounded-lg p-4 md:p-6",children:[(0,l.jsx)("h3",{className:"mb-2 md:mb-6 text-textBluePrimary font-bold text-xl md:text-2xl lg:text-3xl xl:text-3xl text-center",children:"Job Placement Assistance"}),(0,l.jsx)("div",{className:"flex mx-auto flex-col gap-4",children:h.jobAssistanceData&&h.jobAssistanceData.map((e,t)=>(0,l.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,l.jsx)("h4",{className:"text-sm md:text-base lg:text-xl text-customBlack font-bold",children:e.title}),(0,l.jsx)("p",{className:"text-xs md:text-sm lg:text-base text-customBlack font-semibold",children:e.desc})]},"job-".concat(t)))})]})]})]})]})):(0,l.jsx)("div",{children:"Course Not Found"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[565,173,351,765,441,517,358],()=>t(9200)),_N_E=e.O()}]);